package likewallet.likewallet;

import android.os.Build;
import android.app.ActivityManager;
import android.content.Context;
import androidx.multidex.MultiDexApplication;

public class LikeWalletApplication extends MultiDexApplication {
    @Override
    public void onCreate() {
        super.onCreate();

        // Additional configuration for Android 11 (API level 30) and above
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11 specific configurations
            ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager != null) {
                // Ensure the app can handle the memory requirements for Android 11
                activityManager.isLowRamDevice();
            }
        }
    }
}
