import 'dart:io';
import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:get/get.dart';

class BiometricService {
  static final LocalAuthentication _localAuth = LocalAuthentication();

  /// Check if biometric authentication is available on the device
  static Future<bool> isAvailable() async {
    try {
      return await _localAuth.canCheckBiometrics;
    } catch (e) {
      print('Error checking biometric availability: $e');
      return false;
    }
  }

  /// Get available biometric types
  static Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      print('Error getting available biometrics: $e');
      return [];
    }
  }

  /// Check if device has biometric authentication set up
  static Future<bool> isDeviceSupported() async {
    try {
      return await _localAuth.isDeviceSupported();
    } catch (e) {
      print('Error checking device support: $e');
      return false;
    }
  }

  /// Authenticate using biometric
  static Future<bool> authenticate({
    String? localizedReason,
    bool useErrorDialogs = true,
    bool stickyAuth = true,
  }) async {
    try {
      // Check if biometric is available
      final isAvailable = await BiometricService.isAvailable();
      if (!isAvailable) {
        return false;
      }

      // Get available biometrics
      final availableBiometrics = await getAvailableBiometrics();
      if (availableBiometrics.isEmpty) {
        return false;
      }

      // Set localized reason based on platform and available biometrics
      String reason = localizedReason ?? _getLocalizedReason(availableBiometrics);

      // Perform authentication
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: reason,
        options: AuthenticationOptions(
          useErrorDialogs: useErrorDialogs,
          stickyAuth: stickyAuth,
          biometricOnly: true,
        ),
      );

      return didAuthenticate;
    } on PlatformException catch (e) {
      print('Biometric authentication error: ${e.message}');
      return false;
    } catch (e) {
      print('Unexpected error during biometric authentication: $e');
      return false;
    }
  }

  /// Get localized reason based on available biometrics
  static String _getLocalizedReason(List<BiometricType> availableBiometrics) {
    if (Platform.isIOS) {
      if (availableBiometrics.contains(BiometricType.face)) {
        return 'biometric_face_reason'.tr;
      } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
        return 'biometric_touch_reason'.tr;
      }
    } else if (Platform.isAndroid) {
      if (availableBiometrics.contains(BiometricType.fingerprint)) {
        return 'biometric_fingerprint_reason'.tr;
      }
    }
    return 'biometric_default_reason'.tr;
  }

  /// Get biometric type name for display
  static String getBiometricTypeName(List<BiometricType> availableBiometrics) {
    if (Platform.isIOS) {
      if (availableBiometrics.contains(BiometricType.face)) {
        return 'Face ID';
      } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
        return 'Touch ID';
      }
    } else if (Platform.isAndroid) {
      if (availableBiometrics.contains(BiometricType.fingerprint)) {
        return 'biometric_fingerprint'.tr;
      }
    }
    return 'biometric_authentication'.tr;
  }

  /// Check if biometric authentication is enabled in app settings
  static bool isBiometricEnabled() {
    // This would typically check app settings/preferences
    // For now, return true if biometric is available
    return true;
  }
}
