import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class AppApi {

  static Future<dynamic> post(String url, Map jsonMap, [String? xKey]) async {
    try {
      var urlApi = Uri.parse(url);

      Map<String, String> requestHeaders = {
        'Content-Type': 'application/json',
        if (xKey != null) 'x-api-key': xKey,
      };

      debugPrint('Making POST request to: $url');
      debugPrint('Headers: $requestHeaders');
      debugPrint('Body: ${jsonEncode(jsonMap)}');

      var response = await http.post(
        urlApi,
        headers: requestHeaders,
        body: jsonEncode(jsonMap),
      ).timeout(const Duration(seconds: 30), onTimeout: () {
        throw TimeoutException('Request timed out');
      });

      debugPrint('Response status code: ${response.statusCode}');
      debugPrint('Response body: ${response.body}');

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        // Try to parse error response
        try {
          final errorBody = json.decode(response.body);
          throw HttpException('API error: ${response.statusCode}, message: ${errorBody['message'] ?? 'Unknown error'}');
        } catch (e) {
          throw HttpException('Failed to load data: ${response.statusCode}, body: ${response.body}');
        }
      }
    } catch (e) {
      debugPrint('Error during POST request: $e');
      throw Exception('Error during POST request: $e');
    }
  }

  static Future<dynamic> get(String url) async {
    try {
      var urlApi = Uri.parse(url);
      debugPrint('Making GET request to: $url');

      var response = await http.get(urlApi).timeout(const Duration(seconds: 30), onTimeout: () {
        throw TimeoutException('Request timed out');
      });

      debugPrint('Response status code: ${response.statusCode}');
      debugPrint('Response body: ${response.body}');

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        // Try to parse error response
        try {
          final errorBody = json.decode(response.body);
          throw HttpException('API error: ${response.statusCode}, message: ${errorBody['message'] ?? 'Unknown error'}');
        } catch (e) {
          throw HttpException('Failed to load data: ${response.statusCode}, body: ${response.body}');
        }
      }
    } catch (e) {
      debugPrint('Error during GET request: $e');
      throw Exception('Error during GET request: $e');
    }
  }

}

class TimeoutException implements Exception {
  final String message;
  TimeoutException(this.message);

  @override
  String toString() => message;
}