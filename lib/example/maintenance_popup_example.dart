import 'package:flutter/material.dart';
import 'package:likewallet/view/alert/closeMaintenance.dart';

/// Example usage of CloseMaintenancePopup
/// 
/// This file demonstrates how to use the CloseMaintenancePopup widget
/// in your application.
class MaintenancePopupExample extends StatelessWidget {
  const MaintenancePopupExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Maintenance Popup Example'),
        backgroundColor: const Color(0xff00c5c2),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Tap the button below to show the maintenance popup',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                // Show the maintenance popup
                _showMaintenancePopup(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xff00c5c2),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
              ),
              child: const Text('Show Maintenance Popup'),
            ),
          ],
        ),
      ),
    );
  }

  /// Show the maintenance popup
  void _showMaintenancePopup(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CloseMaintenancePopup(),
        fullscreenDialog: true,
      ),
    );
  }
}
