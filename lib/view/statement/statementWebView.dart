import 'dart:async';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StatementWebView extends StatefulWidget {
  final String url;
  final String title;
  
  const StatementWebView({
    Key? key, 
    required this.url,
    this.title = 'Statement',
  }) : super(key: key);

  @override
  State<StatementWebView> createState() => _StatementWebViewState();
}

class _StatementWebViewState extends State<StatementWebView> {
  final Completer<WebViewController> _controller = Completer<WebViewController>();
  bool isLoading = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff141322),
      appBar: AppBar(
        backgroundColor: const Color(0xff141322),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.title,
          style: TextStyle(
            color: Colors.white,
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () async {
              final controller = await _controller.future;
              controller.reload();
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          WebView(
            initialUrl: widget.url,
            javascriptMode: JavascriptMode.unrestricted,
            onWebViewCreated: (controller) {
              _controller.complete(controller);
            },
            navigationDelegate: (request) {
              // Allow all navigation for statement pages
              print('Navigating to: ${request.url}');
              return NavigationDecision.navigate;
            },
            onPageStarted: (url) {
              print("📥 Statement page started loading: $url");
              setState(() {
                isLoading = true;
              });
            },
            onPageFinished: (url) {
              print("✅ Statement page finished loading: $url");
              setState(() {
                isLoading = false;
              });
            },
            gestureNavigationEnabled: true,
          ),
          if (isLoading)
            Container(
              color: const Color(0xff141322),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xff08e8de)),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
