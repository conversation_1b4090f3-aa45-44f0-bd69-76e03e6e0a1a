import 'package:flutter/material.dart';
import 'package:likewallet/service/components.dart';


class PopupTermPolicy extends StatefulWidget {
  const PopupTermPolicy({Key? key}) : super(key: key);

  @override
  State<PopupTermPolicy> createState() => _PopupTermPolicyState();
}

class _PopupTermPolicyState extends State<PopupTermPolicy> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body:Stack(
        alignment: Alignment.center,
        children: [
          Positioned(
            bottom: 0,
            child: Container(
              height: MediaQuery.of(context).size.height * 0.9,
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                color: Color(0xFFFFFFFF),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              padding: EdgeInsets.only(
                top: 36,
                left: 24,
                right: 24,
              ),
              child: Column(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Terms and Fees",
                        style: TextStyle(fontSize: 14,
                          // fontWeight: FontWeight.bold,
                          fontFamily: 'Proxima Nova',
                          color: Color(0xFF666570),
                        ),
                      ),
                      Container(
                        height: MediaQuery.of(context).size.height * 0.8,
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              buildSection("1. Processing Time", "Gold withdrawal requests will be processed within 7–14 business days after confirmation. You may track the status of your transaction via the 'Recent Transactions' section."),
                              buildSection("2. Service Fees", "Processing, shipping, and insurance fees will be deducted from the total gold weight in your account.",),
                              buildSection("3. Delivery & Insurance Coverage", "Gold will be delivered Thailand nationwide via EMS. Insurance coverage is provided for the actual gold value, capped at THB 50,000 (or equivalent).",),
                              buildSection("4. Receiving Your Gold", "Please review the attached illustration for step-by-step instructions on receiving your gold. This ensures a smooth and efficient process.",),
                              buildSection("5. Check the EMS Package Receiving Process", "All fees and timelines are final upon confirmation. For inquiries, contact Support via ? button.",),
                              SizedBox(height: 10),
                              Image.asset('assets/image/like2crypto/deliveredStep.png',
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),

                ],
              )
            ),
          ),
          Positioned(
            bottom: MediaQuery.of(context).size.height * 0.84,
            right: MediaQuery.of(context).size.width * 0,
            child: GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Image.asset(
                LikeWalletImage.icon_button_cancel_white,
                height: mediaQuery(context, 'height', 193),
              ),
            ),
          ),
        ],
      )
    );
  }

  Widget buildSection(String title, points) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(fontSize: 14,
              fontFamily: 'Proxima Nova',
              color: Color(0xFF666570),
            ),
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: MediaQuery.of(context).size.width * 0.05,
                alignment: Alignment.topRight,
                child: Text("•",style: TextStyle(fontSize: 14,
                  fontFamily: 'Proxima Nova',
                  color: Color(0xFF666570),),
                ),
              ),
              SizedBox(width: 5,),
              Container(
                width: MediaQuery.of(context).size.width * 0.7,
                child: Text("${points}",
                  style: TextStyle(fontSize: 14,
                    fontFamily: 'Proxima Nova',
                    color: Color(0xFF666570),),
                ),
              ),
            ],
          )

        ],
      ),
    );
  }

}
