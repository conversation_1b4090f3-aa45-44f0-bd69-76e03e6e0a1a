import 'package:flutter/material.dart';
import 'package:likewallet/view/like2crypto/select_KYC.dart';

class PopupKYC extends StatefulWidget {
  const PopupKYC({Key? key}) : super(key: key);

  @override
  State<PopupKYC> createState() => _PopupKYCState();
}

class _PopupKYCState extends State<PopupKYC> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              color: Colors.black.withOpacity(0.2),
            ),
          ),
          Positioned(
            bottom: 0,
            child: Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height * 0.35,
              decoration: BoxDecoration(
                color: Color(0xFF2B2A38),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Container(
                    width: 40,
                    height: 5,
                    decoration: BoxDecoration(
                      color: Color(0x66FFFFFF),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  Image.asset('assets/image/like2crypto/kycIcon.png',
                    width: 100,
                    height: 100,
                  ),
                  Container(
                    width: MediaQuery.of(context).size.width * 0.8,
                    child: Text('For your security and to continue using LikeWallet, please verify your identity (KYC).',
                      maxLines: 2,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.6),
                        fontSize: 14,
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(context, MaterialPageRoute(builder: (context) => SelectKYC()));
                    },
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.8,
                      height: MediaQuery.of(context).size.height * 0.06,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        gradient: LinearGradient(
                          begin: Alignment(-1.0, -0.94),
                          end: Alignment(1.0, 1.0),
                          colors: [
                            const Color(0xff52fcf0),
                            const Color(0xff33faec),
                            const Color(0xff22c4e6)
                          ],
                          stops: [0.0, 0.335, 1.0],
                        ),
                      ),
                      child: Center(
                        child: Text('Get Started',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ),
          ),
        ],
      )
    );
  }
}
