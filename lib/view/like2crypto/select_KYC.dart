import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/view/like2crypto/scan_ID_card.dart';


class SelectKYC extends StatefulWidget {
  const SelectKYC({Key? key}) : super(key: key);

  @override
  State<SelectKYC> createState() => _SelectKYCState();
}

class _SelectKYCState extends State<SelectKYC> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Color(0xFF2B2A38), // สีพื้นหลังเข้มเหมือนในภาพ
        padding: EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              height: MediaQuery.of(context).size.height * 0.4,
              alignment: Alignment.topCenter,
              child: Column(
                children: [
                  SizedBox(
                    height: MediaQuery.of(context).size.height * 0.1,
                  ),
                  Text(
                    'Verify your identity',
                    style: TextStyle(
                      fontSize: 24.0,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 8.0),
                  Text(
                    'It will only take 2 minute',
                    style: TextStyle(
                      fontSize: 16.0,
                      color: Colors.white70,
                    ),
                  ),
                  SizedBox(height: 25.0),
                  InkWell(
                    onTap: () {
                      print("click");
                      Navigator.push(context, MaterialPageRoute(builder: (context) => ScanIdCardPage()));
                    },
                    child: Container(
                      child: _buildOptionCard(
                        icon: 'assets/image/like2crypto/Identity_Icon.png',
                        title: 'Identity document',
                        subtitle: 'Take a photo of your ID',
                        onTap: () {
                          // ใส่ logic เมื่อกดเลือก Identity document
                        },
                      ),
                    ),
                  ),
                  SizedBox(height: 10.0),
                  _buildOptionCard(
                    icon: 'assets/image/like2crypto/Selfie_Icon.png',
                    title: 'Selfie',
                    subtitle: 'Take a selfie',
                    onTap: () {
                      // ใส่ logic เมื่อกดเลือก Selfie
                    },
                  ),
                ],
              ),
            ),
            Container(
                child: Column(
                  children: [
                    Text("Powered by samsub",
                      style: TextStyle(color: Colors.white70),
                    ),
                    SizedBox(height: 30.0),
                  ],
                ),
            ),
          ],
        )
      ),
    );
  }

  Widget _buildOptionCard({
    required String icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.08,
      width: MediaQuery.of(context).size.width * 0.85,
      decoration: BoxDecoration(
        color: Color(0xFF3A3947),
        borderRadius: BorderRadius.circular(12.0),
      ),
      alignment: Alignment.center,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.75,
        height: MediaQuery.of(context).size.height * 0.06,
        // color: Colors.red.withOpacity(0.5),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Image.asset(
                  icon,
                  width: 40.0,
                  height: 40.0,
                ),
                SizedBox(width: 10.0),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.w600,
                        color: Colors.white70,
                      ),
                    ),
                    SizedBox(height: 4.0),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12.0,
                        color: Colors.white70,
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.white70,
              size: 16.0,
            ),
          ],
        ),
      ),
    );
  }
}
