import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/controller/newsController/newsController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/loading.dart';
import 'package:likewallet/view/newsPage/imageSlider.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class NewsPage extends StatefulWidget {
  const NewsPage({super.key});

  @override
  State<NewsPage> createState() => _NewsPageState();
}

class _NewsPageState extends State<NewsPage> {

  var lang = Storage.get<String>(StorageKeys.language) ?? 'en';
  final NewsController newsController = Get.find<NewsController>();

  final f_en = DateFormat('MMMM d kk:mm a');
  final f_th = DateFormat('d MMMM kk:mm น.');

  @override
  void initState() {
    super.initState();
    // โหลดแค่ครั้งเดียว
    newsController.loadDataIfNeeded();
    newsController.loadNotify();
  }


  @override
  Widget build(BuildContext context) {
    return Obx(() => ModalProgressHUD(
      opacity: 0.1,
      progressIndicator: CustomLoading(),
      inAsyncCall: newsController.isLoading.value,
      child: Scaffold(
        body: Container(
          color: LikeWalletAppTheme.white,
          height: 1.sh,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              SizedBox(height: 10.h),
              _appBar(),
              newsController.isLoading.value
                  ? Center(
                child: SpinKitFadingCircle(
                  color: LikeWalletAppTheme.bule1,
                  size: 50.sp,
                ),
              )
                  : newsController.tabSelect.value == 1
                  ? ImageSlider()
                  : notificationPage(),
            ],
          ),
        ),
      ),
    ));
  }

  Widget _appBar() {
    return Align(
      alignment: Alignment.center,
      child: Container(
        margin: EdgeInsets.only(top: 50.h),
        width: 350.0.w,
        height: 30.0.h,
        decoration: BoxDecoration(
          color: const Color(0xff201F2D).withOpacity(0.08),
          borderRadius: BorderRadius.circular(100.0.h),
          border: Border.all(width: 2.0.sp, color: const Color(0x4dffffff)),
        ),
        child: Stack(
          children: [
            Obx(() => AnimatedAlign(
              alignment: newsController.tabSelect.value == 1
                  ? Alignment.centerLeft
                  : Alignment.centerRight,
              duration: const Duration(milliseconds: 300),
              curve: Curves.ease,
              child: Container(
                width: 175.0.w,
                height: 40.0.h,
                decoration: BoxDecoration(
                  color: const Color(0xff474652),
                  borderRadius: BorderRadius.circular(100.0.h),
                ),
              ),
            )),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _tabSelect(
                  alignmentTarget: Alignment.centerLeft,
                  translationKey: "notify_title_news",
                  tabIndex: 1,
                ),
                _tabSelect(
                  alignmentTarget: Alignment.centerRight,
                  translationKey: "notify_title_notification",
                  tabIndex: 2,
                  showBadge: true,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _tabSelect({
    required Alignment alignmentTarget,
    required String translationKey,
    required int tabIndex,
    bool showBadge = false,
  }) {
    return Expanded(
      child: InkWell(
        onTap: () {
          if (newsController.tabSelect.value != tabIndex) {
            newsController.changeTabSelection(tabIndex);
          }
        },
        child: Container(
          alignment: Alignment.center,
          height: double.infinity,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Obx(() => Text(
                translationKey.tr,
                style: TextStyle(
                  fontFamily: 'Proxima Nova',
                  fontSize: 10.h,
                  color: newsController.alignment.value == alignmentTarget
                      ? Color(0xff08e8de)
                      : Colors.black.withOpacity(0.8),
                  letterSpacing: 1.5.sp,
                  shadows: [
                    Shadow(
                      color: const Color(0x29000000),
                      offset: Offset(0, 3.sp),
                      blurRadius: 6.sp,
                    )
                  ],
                ),
                textAlign: TextAlign.center,
              )),
              if (showBadge)
                Obx(() => newsController.uid.value == ''
                    ? Container()
                    : StreamBuilder<QuerySnapshot>(
                  stream: newsController.fireStore
                      .collection('notificationByUser')
                      .doc(newsController.uid.value)
                      .collection('notify')
                      .where("status", isEqualTo: "unread")
                      .snapshots(),
                  builder: (context, snapshot) {
                    if (snapshot.hasError ||
                        snapshot.connectionState ==
                            ConnectionState.waiting ||
                        snapshot.data!.docs.isEmpty) {
                      return Container();
                    }
                    return Container(
                      margin: EdgeInsets.symmetric(horizontal: 20.w),
                      height: 22.h,
                      width: 22.h,
                      decoration: const BoxDecoration(
                        color: Color(0xffFFC400),
                        shape: BoxShape.circle,
                      ),
                    );
                  },
                )),
            ],
          ),
        ),
      ),
    );
  }



  // Border separator
  Widget border() {
    return Container(
      height: 460.h,
      decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              spreadRadius: 0,
              blurRadius: 3.sp,
              color: Color(0xff707070).withOpacity(0.2),
              offset: Offset(
                1.1.sp,
                0.0,
              ),
            ),
          ],
          border: Border(
            right: BorderSide(
              color: Color(0xff707070).withOpacity(0.12),
              width: 3.sp,
            ),
          )),
    );
  }

  Widget notificationPage() {
    return Expanded(
      child: Obx(() => newsController.uid.value.isEmpty
          ? SpinKitFadingCircle(
        color: LikeWalletAppTheme.bule1,
        size: 200.sp,
      )
          : StreamBuilder<QuerySnapshot>(
          stream: newsController.fireStore
              .collection('notificationByUser')
              .doc(newsController.uid.value)
              .collection('notify')
              .orderBy('timeStamp', descending: true)
              .snapshots(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return Center(child: Text(snapshot.error.toString()));
            }

            if (!snapshot.hasData) {
              return Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                ),
              );
            } else {
              if (snapshot.data!.docs.isEmpty) {
                return Center(child: Text('not_massage'.tr));
              } else {
                return ListView(
                  padding: EdgeInsets.only(bottom: 350.h),
                  children: snapshot.data!.docs
                      .map((DocumentSnapshot document) {
                    return _body(
                        document.data() as Map<String, dynamic>,
                        int.parse(document.id),
                        context);
                  }).toList(),
                );
              }
            }
          })),
    );
  }

  // Card list view item
  Widget _body(Map<String, dynamic> document, int id,context) {
    return Padding(
      padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 5.h),
      child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: Colors.white70,
              width: 1.sp,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                blurRadius: 10.sp,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: document['detail']=="คุณผ่านการ KYC เรียบร้อย"
              ?InkWell(
              onTap: () async {
                if (document['url'] == "") {
                  newsController.showKycDialog(context, document, id);
                } else {
                  newsController.openWebView(document['url']);
                }
              },
              child: Stack(
                children: <Widget>[
                  Row(
                    children: <Widget>[
                      Padding(
                        padding: EdgeInsets.all(25.w),
                        child: Container(
                          // height: mediaQuery(context, "height", 310),
                          width: 232.5.sp,
                          child: CachedNetworkImage(
                            imageUrl: document['icon'],
                            placeholder: (context, url) =>
                                SpinKitFadingCircle(
                                  color: LikeWalletAppTheme.bule1,
                                  size: 100.sp,
                                ),
                            errorWidget: (context, url, error) =>
                                Icon(Icons.error),
                          ),
                        ),
                      ),
                      Expanded(
                        child: Container(),
                      ),
                      Container(
                        alignment: Alignment.center,
                        child: Container(
                          padding: EdgeInsets.only(right: 10.w),
                          alignment: Alignment.center,
                          height: 310.h,
                          width: 570.w,
                          child: Column(
                            children: <Widget>[
                              Align(
                                alignment: Alignment.topLeft,
                                child: Container(
                                  margin: EdgeInsets.only(
                                    top: 40.sp,
                                  ),
                                  child: Text(
                                    document['title'],
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        fontSize: 40.sp,
                                        fontFamily: 'Proxima Nova',
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black),
                                  ),
                                ),
                              ),
                              Align(
                                alignment: Alignment.topLeft,
                                child: Container(
                                  margin: EdgeInsets.only(
                                    top: 10.sp,
                                  ),
                                  child: Text(
                                    document['detail'],
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        fontSize: 36.sp,
                                        fontFamily: 'Proxima Nova',
                                        fontWeight: FontWeight.normal,
                                        color:
                                        Colors.black.withOpacity(0.6)),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Container(),
                              ),
                              Align(
                                alignment: Alignment.topLeft,
                                child: Container(
                                  // margin: EdgeInsets.only(
                                  //     bottom: 58.sp,
                                  //     ),
                                  child: FutureBuilder<String>(
                                    future: newsController.formatDate(DateTime.fromMillisecondsSinceEpoch(id * 1000), lang),
                                    builder: (context, snapshot) {
                                      if (!snapshot.hasData) return SizedBox.shrink();
                                      return Text(
                                        snapshot.data!,
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          fontSize: 16.sp,
                                          fontFamily: 'Proxima Nova',
                                          fontWeight: FontWeight.normal,
                                          color: Colors.black.withOpacity(0.6),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                              SizedBox(height: 30.h),
                            ],
                          ),
                        ),
                      ),
                      document['status'] == 'unread'
                          ? Container(
                        margin:
                        EdgeInsets.symmetric(horizontal: 20.w),
                        height: 30.h,
                        width: 30.h,
                        decoration: BoxDecoration(
                            color: Color(0xffFFC400),
                            shape: BoxShape.circle),
                      )
                          : Container(
                        margin:
                        EdgeInsets.symmetric(horizontal: 20.w),
                      ),
                      SizedBox(width: 25.w),
                    ],
                  ),
                  Positioned(
                    top: 38.5.h,
                    right: 42.5.w,
                    child: Container(
                      height: 35.h,
                      width: 35.w,
                      child: InkWell(
                        // onTap: () => deleteNotify(id),
                        child: Image.asset(
                          LikeWalletImage.notify_close,
                        ),
                      ),
                    ),
                  ),
                ],
              ))
              : document['detail']=="Your KYC is verify!"
              ?InkWell(
              onTap: () async {
                if (document['url'] == "") {
                  newsController.showKycDialog(context, document, id);
                } else {
                  newsController.openWebView(document['url']);
                }
              },
              child: Stack(
                children: [
                  Row(
                    children: [
                      Padding(
                        padding: EdgeInsets.all(15.w),
                        child: Container(
                          // height: mediaQuery(context, "height", 310),
                          width: 100.sp,
                          child: CachedNetworkImage(
                            imageUrl: document['icon'],
                            placeholder: (context, url) =>
                                SpinKitFadingCircle(
                                  color: LikeWalletAppTheme.bule1,
                                  size: 60.sp,
                                ),
                            errorWidget: (context, url, error) =>
                                Icon(Icons.error),
                          ),
                        ),
                      ),
                      Expanded(
                        child: Container(),
                      ),
                      Container(
                        alignment: Alignment.center,
                        child: Container(
                          padding: EdgeInsets.only(right: 10.w),
                          alignment: Alignment.center,
                          height: 150.h,
                          width: 0.40.sw,
                          child: Column(
                            children: <Widget>[
                              Align(
                                alignment: Alignment.topLeft,
                                child: Container(
                                  margin: EdgeInsets.only(
                                    top: 20.sp,
                                  ),
                                  child: Text(
                                    document['title'],
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      fontFamily: 'Proxima Nova',
                                      fontWeight: FontWeight.w600,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              ),
                              Align(
                                alignment: Alignment.topLeft,
                                child: Container(
                                  margin: EdgeInsets.only(
                                    top: 10.sp,
                                  ),
                                  child: Text(
                                    document['detail'],
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      fontSize: 13.sp,
                                      fontFamily: 'Proxima Nova',
                                      fontWeight: FontWeight.normal,
                                      color:
                                      Colors.black.withOpacity(0.6),
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Container(),
                              ),
                              Align(
                                alignment: Alignment.topLeft,
                                child: Container(
                                  // margin: EdgeInsets.only(
                                  //     bottom: 58.sp,
                                  //     ),
                                  child: FutureBuilder<String>(
                                    future: newsController.formatDate(DateTime.fromMillisecondsSinceEpoch(id * 1000), lang),
                                    builder: (context, snapshot) {
                                      if (!snapshot.hasData) return SizedBox.shrink();
                                      return Text(
                                        snapshot.data!,
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          fontSize: 16.sp,
                                          fontFamily: 'Proxima Nova',
                                          fontWeight: FontWeight.normal,
                                          color: Colors.black.withOpacity(0.6),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                              SizedBox(height: 18.h),
                            ],
                          ),
                        ),
                      ),
                      document['status'] == 'unread'
                          ? Container(
                        margin:
                        EdgeInsets.symmetric(horizontal: 20.w),
                        height: 30.h,
                        width: 30.h,
                        decoration: BoxDecoration(
                            color: Color(0xffFFC400),
                            shape: BoxShape.circle),
                      )
                          : Container(
                        margin:
                        EdgeInsets.symmetric(horizontal: 20.w),
                      ),
                      SizedBox(width: 25.w),
                    ],
                  ),
                  Positioned(
                    top: 20.h,
                    right: 22.5.w,
                    child: Container(
                      height: 15.h,
                      width: 15.w,
                      child: InkWell(
                        // onTap: () => deleteNotify(id),
                        child: Image.asset(
                          LikeWalletImage.notify_close,
                        ),
                      ),
                    ),
                  ),
                ],
              ))
              :InkWell(
              onTap: () async {
                if (document['url'] == "") {
                  Dialog simpleDialog = Dialog(
                    elevation: 500,
                    backgroundColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30.0),
                    ),
                    child: Container(
                      // height: mediaQuery(context, 'height', 554.63),
                      width: mediaQuery(context, 'width', 929.64),
                      color: Colors.transparent,
                      margin: EdgeInsets.only(
                          bottom: mediaQuery(context, 'height', 600)),
                      child: ClipRect(
                        child: BackdropFilter(
                          filter: ImageFilter.blur(
                              sigmaX: 10.0, sigmaY: 10.0),
                          child: Container(
                            decoration: BoxDecoration(
                              color:
                              LikeWalletAppTheme.white.withOpacity(0.6),
                              borderRadius:
                              BorderRadius.all(Radius.circular(20.0)),
                            ),
                            height: mediaQuery(context, 'height', 554.63),
                            width: mediaQuery(context, 'width', 929.64),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                SizedBox(height: 30.h),
                                Text(
                                  document['title'],
                                  style: TextStyle(
                                    letterSpacing: 0.3,
                                    fontFamily: 'Proxima Nova',
                                    color: LikeWalletAppTheme.black
                                        .withOpacity(1),
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Expanded(child: Container()),
                                Container(
                                  margin: EdgeInsets.only(
                                      bottom: mediaQuery(
                                          context, 'height', 30)),
                                  width:
                                  mediaQuery(context, 'width', 777.62),
                                  child: Text(
                                    document['detail'],
                                    textAlign: TextAlign.center,
                                    maxLines: 4,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      letterSpacing: 0.3,
                                      fontFamily: 'Proxima Nova',
                                      color: LikeWalletAppTheme.black
                                          .withOpacity(1),
                                      height: mediaQuery(
                                          context, "height", 3.5),
                                      fontSize:
                                      mediaQuery(context, "height", 42),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                                Expanded(child: Container()),

                                Container(
                                    width: mediaQuery(
                                        context, 'width', 777.62),
                                    decoration: BoxDecoration(
                                      border: Border(
                                        top: BorderSide(
                                          //                   <--- left side
                                          color: LikeWalletAppTheme.black
                                              .withOpacity(0.4),
                                          width: mediaQuery(
                                              context, 'width', 1),
                                        ),
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                      MainAxisAlignment.center,
                                      children: <Widget>[
                                        GestureDetector(
                                          onTap: () {
                                            Navigator.of(context).pop();
                                          },
                                          child: Container(
                                            alignment: Alignment.center,
                                            height: mediaQuery(
                                                context, 'height', 127.66),
                                            width: mediaQuery(context,
                                                'width', 777.62) /
                                                2,
                                            child: Text(
                                              'messages_ok'.tr,
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                letterSpacing: 0.3,
                                                fontFamily: 'Proxima Nova',
                                                color: LikeWalletAppTheme
                                                    .bule1_7
                                                    .withOpacity(1),
                                                fontSize: mediaQuery(
                                                    context, "height", 40),
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    )),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                  // showDialog(
                  //     context: context,
                  //     builder: (BuildContext context) => simpleDialog);
                  //
                  // await fireStore
                  //     .collection('notificationByUser')
                  //     .doc(uid)
                  //     .collection('notify')
                  //     .doc(id.toString())
                  //     .update({"status": "read"});
                } else {
                  // Navigator.push(
                  //     context,
                  //     MaterialPageRoute(
                  //         builder: (context) => WebOpenNoTitle(
                  //           title: AppLocalizations.of(context)!
                  //               .translate('notifications'),
                  //           url: document['url'],
                  //         )));
                }
              },
              child: Stack(
                children: <Widget>[
                  Row(
                    children: <Widget>[
                      Padding(
                        padding: EdgeInsets.all(25.w),
                        child: Container(
                          // height: mediaQuery(context, "height", 310),
                          width: 232.5.sp,
                          child: CachedNetworkImage(
                            imageUrl: document['icon'],
                            placeholder: (context, url) =>
                                SpinKitFadingCircle(
                                  color: LikeWalletAppTheme.bule1,
                                  size: 100.sp,
                                ),
                            errorWidget: (context, url, error) =>
                                Icon(Icons.error),
                          ),
                        ),
                      ),
                      Expanded(
                        child: Container(),
                      ),
                      Container(
                        alignment: Alignment.center,
                        child: Container(
                          padding: EdgeInsets.only(right: 10.w),
                          alignment: Alignment.center,
                          height: 310.h,
                          width: 570.w,
                          child: Column(
                            children: <Widget>[
                              Align(
                                alignment: Alignment.topLeft,
                                child: Container(
                                  margin: EdgeInsets.only(
                                    top: 40.sp,
                                  ),
                                  child: Text(
                                    document['title'],
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        fontSize: 40.sp,
                                        fontFamily: 'Proxima Nova',
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black),
                                  ),
                                ),
                              ),
                              Align(
                                alignment: Alignment.topLeft,
                                child: Container(
                                  margin: EdgeInsets.only(
                                    top: 10.sp,
                                  ),
                                  child: Text(
                                    document['detail'],
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        fontSize: 36.sp,
                                        fontFamily: 'Proxima Nova',
                                        fontWeight: FontWeight.normal,
                                        color:
                                        Colors.black.withOpacity(0.6)),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Container(),
                              ),
                              Align(
                                alignment: Alignment.topLeft,
                                child: Container(
                                  // margin: EdgeInsets.only(
                                  //     bottom: 58.sp,
                                  //     ),
                                  child: FutureBuilder<String>(
                                    future: newsController.formatDate(DateTime.fromMillisecondsSinceEpoch(id * 1000), lang),
                                    builder: (context, snapshot) {
                                      if (!snapshot.hasData) return SizedBox.shrink();
                                      return Text(
                                        snapshot.data!,
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          fontSize: 30.sp,
                                          fontFamily: 'Proxima Nova',
                                          fontWeight: FontWeight.normal,
                                          color: Colors.black.withOpacity(0.6),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                              SizedBox(height: 30.h),
                            ],
                          ),
                        ),
                      ),
                      document['status'] == 'unread'
                          ? Container(
                        margin:
                        EdgeInsets.symmetric(horizontal: 20.w),
                        height: 30.h,
                        width: 30.h,
                        decoration: BoxDecoration(
                            color: Color(0xffFFC400),
                            shape: BoxShape.circle),
                      )
                          : Container(
                        margin:
                        EdgeInsets.symmetric(horizontal: 20.w),
                      ),
                      SizedBox(width: 25.w),
                    ],
                  ),
                  Positioned(
                    top: 38.5.h,
                    right: 42.5.w,
                    child: Container(
                      height: 35.h,
                      width: 35.w,
                      child: InkWell(
                        onTap: () => newsController.deleteNotification(id),
                        child: Image.asset(
                          LikeWalletImage.notify_close,
                        ),
                      ),
                    ),
                  ),
                ],
              ))
      ),
    );
  }
}