import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import '../../controller/newsController/newsController.dart';
import '../../model/newsModel/newsModel.dart';
import '../../service/components.dart';

class ImageSlider extends StatefulWidget {
  @override
  State<ImageSlider> createState() => _ImageSliderState();
}

class _ImageSliderState extends State<ImageSlider> {
  final NewsController newsController = Get.find<NewsController>();
  int _current = 0;

  @override
  void initState() {
    super.initState();
    newsController.getNews();
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        color: LikeWalletAppTheme.white,
        child: Obx(() {
          final dataNews = newsController.dataNews;
          if (dataNews.isEmpty) {
            return Center(
              child: SpinKitFadingCircle(
                color: LikeWalletAppTheme.bule1,
                size: 50,
              ),
            );
          }
          return SingleChildScrollView(
            physics: NeverScrollableScrollPhysics(),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: 0.4.sh,
                  child: CarouselSlider.builder(
                    itemCount: dataNews.length,
                    itemBuilder: (context, index, realIndex) {
                      final item = dataNews[index];
                      return InkWell(
                        onTap: () {
                          if (item.url != null && item.url!.isNotEmpty) {
                            print('URL: ${item.url}');
                            // Get.to(() => WebOpenNoTitle(...));
                          }
                        },
                        child: Container(
                          margin: EdgeInsets.symmetric(horizontal: 2.w),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16.r),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black26,
                                blurRadius: 5,
                                offset: Offset(0, 3),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8.r),
                            child: CachedNetworkImage(
                              imageUrl: item.image ?? '',
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Center(
                                child: SpinKitFadingCircle(
                                  color: LikeWalletAppTheme.bule1,
                                  size: 50,
                                ),
                              ),
                              errorWidget: (context, url, error) =>
                                  Icon(Icons.error),
                            ),
                          ),
                        ),
                      );
                    },
                    options: CarouselOptions(
                      height: 0.31.sh,
                      viewportFraction: 0.7,
                      enlargeCenterPage: true,
                      autoPlay: false,
                      enableInfiniteScroll: false,
                      onPageChanged: (index, reason) {
                        setState(() {
                          _current = index;
                        });
                      },
                    ),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(dataNews.length, (index) {
                    return Container(
                      width: 6.0.w,
                      height: 6.0.h,
                      margin: EdgeInsets.symmetric(
                          vertical: 10.0.h, horizontal: 4.0.w),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _current == index
                            ? Color(0xff08e8de)
                            : Color.fromRGBO(0, 0, 0, 0.4),
                      ),
                    );
                  }),
                ),
                SizedBox(
                  height: 0.5.sh,
                  child: SingleChildScrollView(
                    child: Container(
                      padding:
                      EdgeInsets.symmetric(horizontal: 48.w, vertical: 24.h),
                      width: 1.sw,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Center(
                            child: Text(
                              'แจ้งปรับการใช้งานระบบสะสมคะแนน',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          SizedBox(height: 24.h),
                          Text(
                            'LikeWalletจะทำการอัปเกรดสิทธิ\nประโยชน์ให้ผู้ใช้งาน เพื่อประสบการณ์ที่\nหลากหลายขึ้น',
                            style: TextStyle(
                              color: Colors.black.withOpacity(0.7),
                              fontSize: 16.sp,
                            ),
                          ),
                          SizedBox(height: 24.h),
                          Text(
                            'ผู้ใช้งานสามารถสะสมและใช้คะแนนจากการร่วมกิจกรรมต่างๆต่อเนื่องได้ที่แอป\nAAM และ Prachakij',
                            style: TextStyle(
                              color: Colors.black.withOpacity(0.7),
                              fontSize: 16.sp,
                            ),
                          ),
                          SizedBox(height: 24.h),
                          Text(
                            'โดย LikeWallet จะโอนคะแนนสะสม Likepoint ทั้งหมดในทุกกิจกรรมของผู้ใช้งาน ไปยังแอป AAM หรือ Prachakij ที่ผู้ใช้เลือกเป็นแอปอักเกรดคะแนน Likepoint จะถูกโอนไปเป็น AAMpoint (เอเอเอ็มพอยท์) หรือ PMSpoint (พีเอ็มเอสพอยท์) โดยจะมีจำนวนและมูลค่าเท่ากันกับ Likepoint ที่มีอยู่เดิม และยังคงสามารถร่วมกิจกรรมสะสมคะแนนต่างๆได้เช่นเดิม ตามที่แอป AAM และ Prachakij กำหนด',
                            style: TextStyle(
                              color: Colors.black.withOpacity(0.7),
                              fontSize: 16.sp,
                            ),
                          ),
                          SizedBox(height: 24.h),
                          Text(
                            'ซึ่ง LikeWallet จะมีข้อความแจ้งระบบพร้อมอัปเกรดในช่องทางนี้ พร้อมคำแนะนำในการอัปเกรดโดยละเอียด เร็วๆนี้ค่ะ',
                            style: TextStyle(
                              color: Colors.black.withOpacity(0.7),
                              fontSize: 16.sp,
                            ),
                          ),
                          SizedBox(height: 150.h),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }
}
