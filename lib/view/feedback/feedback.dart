import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:likewallet/service/components.dart';
import 'package:path/path.dart' as path;

class FeedbackPage extends StatelessWidget {
  final RxInt selectedRating = 0.obs;
  final RxInt selectedIssueIndex = (-1).obs;
  final TextEditingController commentController = TextEditingController();
  final RxString imagePath = ''.obs;

  List<String> get issues => [
    'feedback_choice1'.tr,
    'feedback_choice2'.tr,
    'feedback_choice3'.tr,
    'feedback_choice4'.tr,
    'feedback_choice5'.tr,
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xff2be8d8),
            Color(0xff2be8d8),
            Color(0xff24cde4)
          ],
          stops: [0.0, 0.493, 1.0],
        ),
      ),
      child: Column(
        children: [
          Container(
            height: 160.h,
            color: Color(0xf21d2b44),
            child: Stack(
              children: [
                // 🔙 Back Button (มุมซ้ายบน)
                Positioned(
                  top: 60.h,
                  left: 20.w,
                  child: backButton(context, LikeWalletAppTheme.gray),
                ),

                // 🌟 ข้อความ + ดาว
                Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: EdgeInsets.only(bottom: 20.h), // 👈 ปรับให้ข้อความ+ดาวต่ำลง
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          "feedback_head".tr,
                          style: TextStyle(
                            fontSize: 18.sp,
                            color: Color(0xff2be8d8),
                            fontWeight: FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 10.h),
                        Obx(() => Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: List.generate(5, (index) {
                            final bool isSelected = selectedRating.value > index;
                            return GestureDetector(
                              onTap: () => selectedRating.value = index + 1,
                              child: Container(
                                margin: EdgeInsets.symmetric(horizontal: 3.w),
                                child: Icon(
                                  isSelected ? Icons.star_rounded : Icons.star_border_rounded,
                                  size: 30.sp,
                                  color: isSelected ? Colors.white : Color(0xff2be8d8),
                                ),
                              ),
                            );
                          }),
                        )),
                      ],
                    ),
                  ),
                ),

              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 20.h),
                Text(
                  "feedback_title1".tr,
                  style: TextStyle(fontSize: 16.sp, color: Colors.black),
                ),
                SizedBox(height: 16.h),
                Obx(() => Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12.w),
                  child: Column(
                    children: List.generate(issues.length, (index) {
                      final isSelected = selectedIssueIndex.value == index;

                      return GestureDetector(
                        onTap: () => selectedIssueIndex.value = index,
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 8.h),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              // custom radio
                              Container(
                                width: 20.w,
                                height: 20.w,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.white,
                                    width: isSelected ? 0 : 1.2, // ขอบบางถ้าไม่เลือก
                                  ),
                                  color: isSelected ? Colors.white : Colors.transparent, // วงกลมขาวถ้าเลือก
                                ),
                              ),
                              SizedBox(width: 12.w),
                              Expanded(
                                child: Text(
                                  issues[index],
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: Colors.black,
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                      );
                    }),
                  ),
                )),

                SizedBox(height: 16.h),
                Text(
                  "feedback_title2".tr,
                  style: TextStyle(fontSize: 16.sp, color: Colors.black),
                ),
                SizedBox(height: 8.h),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                    color: Colors.white.withOpacity(0.15),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  child: TextField(
                    controller: commentController,
                    maxLines: 4,
                    decoration: InputDecoration(
                      hintText: "feedback_comment".tr,
                      border: InputBorder.none,
                      hintStyle: TextStyle(color: Colors.black.withOpacity(0.3)),
                    ),
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                SizedBox(height: 16.h),
                Obx(() => GestureDetector(
                  onTap: () async {
                    final picker = ImagePicker();
                    final picked = await picker.pickImage(source: ImageSource.gallery);
                    if (picked != null) imagePath.value = picked.path;
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 12.w),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(color: Colors.white30),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            imagePath.isEmpty
                                ? "feedback_upload".tr
                                : path.basename(imagePath.value),
                            style: TextStyle(color: Colors.black),
                          ),
                        ),
                        Icon(Icons.image, color: Colors.white70),
                      ],
                    ),
                  ),
                )),
                SizedBox(height: 24.h),
                Center(
                  child: ElevatedButton(
                    onPressed: () => _submitFeedback(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1D2B44),
                      minimumSize: Size(220.w, 48.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    child: Text("SUBMIT", style: TextStyle(color: Colors.cyanAccent)),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _submitFeedback() {
    if (selectedRating.value == 0) {
      Get.snackbar("Please rate us", "Select at least one star",
          backgroundColor: Colors.red, colorText: Colors.white);
      return;
    }
    // ส่ง Feedback logic...
    Get.snackbar("Thank you!", "Your feedback has been submitted.",
        backgroundColor: Colors.green, colorText: Colors.white);
  }
}
