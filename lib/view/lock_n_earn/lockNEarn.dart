  import 'dart:ui';

  import 'package:carousel_slider/carousel_slider.dart';
  import 'package:flutter/material.dart';
  import 'package:flutter/services.dart';
  import 'package:flutter_screenutil/flutter_screenutil.dart';
  import 'package:flutter_svg/flutter_svg.dart';
import 'package:focus_detector/focus_detector.dart';
  import 'package:get/get.dart';
  import 'package:intl/intl.dart';
  import 'package:likewallet/controller/lockNEarn/lockNEarnController.dart';
  import 'package:likewallet/service/loading.dart';
  import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
  import 'package:intl/intl.dart' as formatIntl;

  import '../../controller/profile/profileController.dart';
import '../../controller/walletController/walletDataController.dart';
import '../../service/components.dart';

  class LockLikeView extends StatefulWidget {
    const LockLikeView({super.key});

    @override
    State<LockLikeView> createState() => _LockLikeViewState();
  }

  class _LockLikeViewState extends State<LockLikeView> {
     final WalletDataController walletController = Get.find<WalletDataController>();
     final ProfileController profileController = Get.find<ProfileController>();
    final LockNEarnController lockNearnCtrl =
        Get.isRegistered<LockNEarnController>()
            ? Get.find<LockNEarnController>()
            : Get.put(LockNEarnController());

    final showConfirmButtons = false.obs;
    final f = new formatIntl.NumberFormat("###,###.##");

     String formatNumber(double number) {
    return NumberFormat('###0.0000000000').format(number);
  }

    @override
    void initState() {
      // TODO: implement initState
      super.initState();
      final likeValue = double.tryParse(lockNearnCtrl.totalLike.value) ?? 0.0;
      if (likeValue == 0.0) {
        lockNearnCtrl.initializeData();
      }
    }

    @override
    Widget build(BuildContext context) {
      return FocusDetector(
    onFocusGained: () async {
      print('Focus Gained — calling setData');
      await lockNearnCtrl.setData();
    },
    child: Obx(() => ModalProgressHUD(
            opacity: 0.1,
            inAsyncCall: lockNearnCtrl.isLoading.value,
            progressIndicator: CustomLoading(),
            child: GestureDetector(
              onTap: () {
                FocusScopeNode currentFocus = FocusScope.of(context);
                if (!currentFocus.hasPrimaryFocus) {
                  currentFocus.unfocus();
                }
              },
              child: Scaffold(
                resizeToAvoidBottomInset: true,
                body: LayoutBuilder(
                  builder: (context, constraints) {
                    return  SingleChildScrollView(
                    padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
                     child: ConstrainedBox(
                      constraints: BoxConstraints(minHeight: constraints.maxHeight),
                       child: IntrinsicHeight(
                         child: Stack(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    LikeWalletAppTheme.bule2_9,
                                    LikeWalletAppTheme.bule2_10,
                                    LikeWalletAppTheme.bule2_11,
                                  ],
                                  stops: [0.0, 0.493, 1.0],
                                ),
                              ),
                            ),
                            Positioned(
                              top: 0,
                              left: 0,
                              child: SvgPicture.network(
                                LikeWalletImage.icon_locklike_image,
                                fit: BoxFit.cover,
                                width: 0.77.sw,
                                height: 0.34.sh,
                              ),
                            ),
                            Column(
                              children: [
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                        top: MediaQuery.of(context).padding.top + 12,
                                        left: 24),
                                    child: 
                                    InkWell(
                                      child: SizedBox(
                                        height: mediaQuery(context, 'height', 45),
                                        width: mediaQuery(context, 'height', 45),
                                        child: Image.asset(
                                          LikeWalletImage.icon_back_button,
                                          height: mediaQuery(context, "height", 30),
                                          color: Colors.black,
                                        ),
                                      ),
                                      onTap: () async{
                                        Navigator.of(context).pop();
                                        var userPhone = profileController.user?.phoneNumber;
                                        await walletController.getDataWallet(userPhone);
                                      },
                                    )
                                      // backButton(context, LikeWalletAppTheme.gray,onTap: (){
  //                                     Get.back();
  //                                     await walletController.getDataWallet();
  //                                   },
                                  ),
                                ),
                                _buildBalanceSection(),
                                SizedBox(height: 20.h),
                                _buildMainLockArea(context),
                                if (lockNearnCtrl.success.value)
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Text('✅ Success!',
                                        style:
                                            TextStyle(color: Colors.green, fontSize: 18)),
                                  ),
                                // SizedBox(height: 20),
                                _buildConfirmButtons(),
                                Spacer(),
                                _buildCarousel(),
                              ],
                            ),
                          ],
                                           ),
                       ),
                     ),
                   );
                   }
                ),
              ),
            ),
          ))
      );
    }

    Widget _buildBalanceSection() {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            SizedBox(height: 18.h),
            Text('lock_likepoint_all'.tr,
                style: TextStyle(color: LikeWalletAppTheme.bule1_3)),
            Obx(() => Text(
              f.format(double.parse(lockNearnCtrl.totalLike.value)),
              style: TextStyle(
                  color: LikeWalletAppTheme.bule1_3, fontSize: 20.sp),
            )),
            SizedBox(height: 18.h),
            Text('lock_likepoint_locklike'.tr,
                style: TextStyle(color: LikeWalletAppTheme.bule1_3)),
            Obx(() => Text(
              lockNearnCtrl.lockedBalance.value,
              style: TextStyle(
                  color: LikeWalletAppTheme.bule1_3, fontSize: 36.sp),
            )),
            SizedBox(height: 18.h),
            Text('lock_likepoint_balance'.tr,
                style: TextStyle(color: LikeWalletAppTheme.bule1_3)),
            Obx(() => Text(
              f.format(double.parse(lockNearnCtrl.amountUnlock.value)),
              style: TextStyle(
                  color: LikeWalletAppTheme.bule1_3, fontSize: 20.sp),
            )),
          ],
        ),
      );
    }

    Widget _buildMainLockArea(BuildContext context) {
      return Container(
        width: 1.sw,
        height: 370.h,
        child: Center(
          child: Stack(
            alignment: Alignment.center,
            children: [
              // กรอบข้างหลัง
              Positioned(
                top: 25.h,
                child: Container(
                  width: 0.87.sw,
                  height: 200.h,
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: LikeWalletAppTheme.bule1, width: 2),
                  ),
                ),
              ),
              // ปุ่ม Lock/Unlock
              Positioned(
                top: 0,
                child: Container(
                  width: 0.8.sw,
                  height: 60.h,
                  decoration: BoxDecoration(
                    color: LikeWalletAppTheme.bule1,
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10.w),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // LEFT (Disabled look)
                        Expanded(
                          child: Container(
                            height: 50.h,
                            alignment: Alignment.center,
                            child: Text(
                              'lock_lock_more'.tr,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: LikeWalletAppTheme.black.withOpacity(0.7),
                                fontSize: 18.sp,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Proxima Nova',
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 12.w),
                        // RIGHT (Active look)
                        Expanded(
                          child: Container(
                            height: 50.h,
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                center: Alignment.center,
                                radius: 0.85,
                                colors: [
                                  LikeWalletAppTheme.bule1_7.withOpacity(0.35),
                                  LikeWalletAppTheme.bule1_7.withOpacity(0.6),
                                ],
                                stops: [0.0, 1.0],
                              ),
                              borderRadius: BorderRadius.circular(50),
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              'lock_unlock'.tr,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: LikeWalletAppTheme.black,
                                fontSize: 18.sp,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Proxima Nova',
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Obx(() {
                return AnimatedSwitcher(
                  duration: Duration(milliseconds: 300),
                  transitionBuilder: (Widget child, Animation<double> animation) {
                    final inFromRight = Tween<Offset>(
                      begin: Offset(1.0, 0.0), // Start from right
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeOutCubic,
                    ));
                    return SlideTransition(
                      position: inFromRight,
                      child: child,
                    );
                  },
                  child: lockNearnCtrl.onTapUnlock.value
                      ? _buildConfirmUnlock()
                      : Container(
                    key: ValueKey('input_area'),
                    width: 0.8.sw,
                    child: Center(
                      child: Padding(
                        padding: EdgeInsets.only(bottom: 90.h),
                        child: SizedBox(
                          width: double.infinity,
                          child: TextField(
                            keyboardType: TextInputType.number,
                            controller: lockNearnCtrl.amountUnLockEditor,
                            inputFormatters: [ThousandsFormatter()],
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 35.sp,
                              fontFamily: 'Proxima Nova',
                            ),
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              hintText: '0',
                              hintStyle: TextStyle(
                                color: Colors.white30,
                                fontSize: 35.sp,
                                fontFamily: 'Proxima Nova',
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              }),
              // ICON วงกลมล่าง
              Positioned(
                bottom: 100.h,
                child: Container(
                  margin: EdgeInsets.only(bottom: 20.h),
                  child: Center(
                    child: GestureDetector(
                      onTap: () async {
                        lockNearnCtrl.changeOnTapUnlock();
                        return;
                        // final checkShow = await lockNearnCtrl.checkUnlockConditions();
                        // if (checkShow) {
                        //   showDialog(
                        //     context: context,
                        //     barrierDismissible: false,
                        //     builder: (_) => WillPopScope(
                        //       onWillPop: () async => false,
                        //       child: Dialog(
                        //         elevation: 500,
                        //         backgroundColor: Colors.transparent,
                        //         shape: RoundedRectangleBorder(
                        //           borderRadius: BorderRadius.circular(30.0),
                        //         ),
                        //         child: Container(
                        //           height: mediaQuery(context, 'height', 554.63),
                        //           width: mediaQuery(context, 'width', 929.64),
                        //           margin: EdgeInsets.only(bottom: mediaQuery(context, 'height', 600)),
                        //           child: ClipRect(
                        //             child: BackdropFilter(
                        //               filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
                        //               child: Container(
                        //                 decoration: BoxDecoration(
                        //                   color: LikeWalletAppTheme.white.withOpacity(0.6),
                        //                   borderRadius: BorderRadius.all(Radius.circular(20.0)),
                        //                 ),
                        //                 child: Column(
                        //                   mainAxisAlignment: MainAxisAlignment.center,
                        //                   children: <Widget>[
                        //                     Text(
                        //                       'notify_title_notification'.tr,
                        //                       style: TextStyle(
                        //                         fontSize: mediaQuery(context, "height", 56),
                        //                         fontWeight: FontWeight.bold,
                        //                         color: LikeWalletAppTheme.black,
                        //                         fontFamily: 'Proxima Nova',
                        //                       ),
                        //                     ),
                        //                     SizedBox(height: mediaQuery(context, 'height', 40)),
                        //                     Text(
                        //                       'alert_lendex_borrow_unlock'.tr,
                        //                       textAlign: TextAlign.center,
                        //                       style: TextStyle(
                        //                         fontSize: mediaQuery(context, "height", 42),
                        //                         fontWeight: FontWeight.w500,
                        //                         color: LikeWalletAppTheme.black,
                        //                         fontFamily: 'Proxima Nova',
                        //                       ),
                        //                     ),
                        //                     Divider(color: LikeWalletAppTheme.black.withOpacity(0.4)),
                        //                     Row(
                        //                       children: <Widget>[
                        //                         Expanded(
                        //                           child: GestureDetector(
                        //                             onTap: () {
                        //                               Navigator.of(context).pop(); // Close dialog
                        //                             },
                        //                             child: Container(
                        //                               height: mediaQuery(context, 'height', 127.66),
                        //                               alignment: Alignment.center,
                        //                               decoration: BoxDecoration(
                        //                                 border: Border(
                        //                                   right: BorderSide(color: LikeWalletAppTheme.black.withOpacity(0.4)),
                        //                                 ),
                        //                               ),
                        //                               child: Text(
                        //                                 'network_error_button'.tr,
                        //                                 style: TextStyle(
                        //                                   fontSize: mediaQuery(context, "height", 52),
                        //                                   fontWeight: FontWeight.w600,
                        //                                   color: LikeWalletAppTheme.bule1_7,
                        //                                   fontFamily: 'Proxima Nova',
                        //                                 ),
                        //                               ),
                        //                             ),
                        //                           ),
                        //                         ),
                        //                         Expanded(
                        //                           child: GestureDetector(
                        //                             onTap: () => Navigator.pop(context),
                        //                             child: Container(
                        //                               height: mediaQuery(context, 'height', 127.66),
                        //                               alignment: Alignment.center,
                        //                               child: Text(
                        //                                 'logout_no'.tr,
                        //                                 style: TextStyle(
                        //                                   fontSize: mediaQuery(context, "height", 52),
                        //                                   fontWeight: FontWeight.w600,
                        //                                   color: LikeWalletAppTheme.bule1_7,
                        //                                   fontFamily: 'Proxima Nova',
                        //                                 ),
                        //                               ),
                        //                             ),
                        //                           ),
                        //                         ),
                        //                       ],
                        //                     ),
                        //                   ],
                        //                 ),
                        //               ),
                        //             ),
                        //           ),
                        //         ),
                        //       ),
                        //     ),
                        //   );
                        // }
                      },
                      child: Container(
                        width: 50.w,
                        height: 50.h,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: LikeWalletAppTheme.bule1,
                            width: 2.w,
                          ),
                          color: LikeWalletAppTheme.bule2_10,
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Image.asset(
                            LikeWalletImage.locklike_icon_unlock,
                            width: 30.w,
                            height: 30.w,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              // ปุ่ม ALL badge (เมื่อกดจะ set onTapUnlock = true)
              // ปุ่ม ALL badge (กดแล้วซ่อนตัวเอง, โชว์ confirm unlock)
              Obx(() => !lockNearnCtrl.onTapUnlock.value
                  ? Container(
                key: ValueKey('all_button'),
                margin: EdgeInsets.only(bottom: 100, right: 20),
                alignment: Alignment.bottomRight,
                child: GestureDetector(
                  onTap: () {
                    lockNearnCtrl.onTapUnlock.value = true;
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 15, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.black,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'lock_button_all'.tr,
                      style: TextStyle(
                          color: LikeWalletAppTheme.bule1, fontSize: 12.sp),
                    ),
                  ),
                ),
              )
                  : SizedBox()),
            ],
          ),
        ),
      );
    }

    Widget _buildToggleButton(bool isLock, String text) {
      return GestureDetector(
        onTap: () {
          // lockNearnCtrl.toggleMode(isLock);
        },
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            image: lockNearnCtrl.selected.value == isLock
                ? DecorationImage(
                    image: AssetImage(LikeWalletImage.locklike_button_select))
                : null,
            borderRadius: BorderRadius.circular(50),
          ),
          child: Center(
              child: Text(text,
                  style: TextStyle(color: Colors.black, fontSize: 16.sp))),
        ),
      );
    }

    Widget _buildInputArea(context) {
      final isLock = lockNearnCtrl.selected.value;
      final amountController = isLock
          ? lockNearnCtrl.amountLockEditor
          : lockNearnCtrl.amountUnLockEditor;
      final screenHeight = MediaQuery.of(context).size.height;
      final screenWidth = MediaQuery.of(context).size.width;

      return Obx(() {
        final hasPreview = lockNearnCtrl.unlockPreviewText.value.isNotEmpty;
        return Column(
          children: [
            Container(
              height: screenHeight * 0.12, // Responsive height
              child: Center(
                child: AnimatedSwitcher(
                  duration: Duration(milliseconds: 300),
                  transitionBuilder: (child, animation) {
                    final slide = Tween<Offset>(
                      begin: Offset(1.0, 0.0),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                        parent: animation, curve: Curves.easeOut));

                    final fade =
                        Tween<double>(begin: 0, end: 1).animate(animation);

                    return SlideTransition(
                      position: slide,
                      child: FadeTransition(opacity: fade, child: child),
                    );
                  },
                  child: hasPreview
                      ? Padding(
                          key: ValueKey('preview_text'),
                          padding: EdgeInsets.symmetric(
                              horizontal: screenWidth * 0.04),
                          child: Text(
                            lockNearnCtrl.unlockPreviewText.value,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize:
                                  screenWidth * 0.035, // Responsive font size
                            ),
                          ),
                        )
                      : TextField(
                          key: ValueKey('amount_input'),
                          controller: lockNearnCtrl.amountUnLockEditor,
                          inputFormatters: [ThousandsFormatter()],
                          decoration: InputDecoration(
                            hintText: '0',
                            hintStyle: TextStyle(color: Colors.white30),
                            border: InputBorder.none,
                          ),
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: screenWidth * 0.09, // Responsive font size
                          ),
                          keyboardType: TextInputType.number,
                        ),
                ),
              ),
            ),
            GestureDetector(
              onTap: () async {
                lockNearnCtrl.changeOnTapUnlock();
                return;
                final checkShow = await lockNearnCtrl.checkUnlockConditions();
                if (checkShow) {
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (_) => WillPopScope(
                      onWillPop: () async => false,
                      child: Dialog(
                        elevation: 500,
                        backgroundColor: Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30.0),
                        ),
                        child: Container(
                          height: mediaQuery(context, 'height', 554.63),
                          width: mediaQuery(context, 'width', 929.64),
                          margin: EdgeInsets.only(
                              bottom: mediaQuery(context, 'height', 600)),
                          child: ClipRect(
                            child: BackdropFilter(
                              filter:
                                  ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
                              child: Container(
                                decoration: BoxDecoration(
                                  color:
                                      LikeWalletAppTheme.white.withOpacity(0.6),
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(20.0)),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    Text(
                                      'notify_title_notification'.tr,
                                      style: TextStyle(
                                        fontSize:
                                            mediaQuery(context, "height", 56),
                                        fontWeight: FontWeight.bold,
                                        color: LikeWalletAppTheme.black,
                                        fontFamily: 'Proxima Nova',
                                      ),
                                    ),
                                    SizedBox(
                                        height:
                                            mediaQuery(context, 'height', 40)),
                                    Text(
                                      'alert_lendex_borrow_unlock'.tr,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize:
                                            mediaQuery(context, "height", 42),
                                        fontWeight: FontWeight.w500,
                                        color: LikeWalletAppTheme.black,
                                        fontFamily: 'Proxima Nova',
                                      ),
                                    ),
                                    Divider(
                                        color: LikeWalletAppTheme.black
                                            .withOpacity(0.4)),
                                    Row(
                                      children: <Widget>[
                                        Expanded(
                                          child: GestureDetector(
                                            onTap: () {
                                              Navigator.of(context).pop(); // Close dialog
                                            },
                                            child: Container(
                                              height: mediaQuery(
                                                  context, 'height', 127.66),
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                border: Border(
                                                  right: BorderSide(
                                                      color: LikeWalletAppTheme
                                                          .black
                                                          .withOpacity(0.4)),
                                                ),
                                              ),
                                              child: Text(
                                                'network_error_button'.tr,
                                                style: TextStyle(
                                                  fontSize: mediaQuery(
                                                      context, "height", 52),
                                                  fontWeight: FontWeight.w600,
                                                  color:
                                                      LikeWalletAppTheme.bule1_7,
                                                  fontFamily: 'Proxima Nova',
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        Expanded(
                                          child: GestureDetector(
                                            onTap: () => Navigator.pop(context),
                                            child: Container(
                                              height: mediaQuery(
                                                  context, 'height', 127.66),
                                              alignment: Alignment.center,
                                              child: Text(
                                                'logout_no'.tr,
                                                style: TextStyle(
                                                  fontSize: mediaQuery(
                                                      context, "height", 52),
                                                  fontWeight: FontWeight.w600,
                                                  color:
                                                      LikeWalletAppTheme.bule1_7,
                                                  fontFamily: 'Proxima Nova',
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                }
              },
              child: Container(
                padding: EdgeInsets.only(top: screenHeight * 0.026),
                width: screenWidth * 0.08,
                child: Image.asset(LikeWalletImage.locklike_icon_unlock),
              ),
            ),
          ],
        );
      });
    }

    Widget _buildConfirmUnlock() {
      final screenHeight = MediaQuery.of(context).size.height;
      final screenWidth = MediaQuery.of(context).size.width;

      return Column(
        children: [
          SizedBox(height: screenHeight * 0.1), // Responsive height
          Container(
              alignment: Alignment.center,
              child: Column(
                children: <Widget>[
                  Text(
                    lockNearnCtrl.amountUnLockText.isEmpty
                        ? "0"
                        : f
                                .format(double.parse(lockNearnCtrl
                                    .amountUnLockText
                                    .replaceAll(',', '')))
                                .toString() +
                            " " +
                            'lock_symbol'.tr,
                    textAlign: TextAlign.right,
                    style: TextStyle(
                        shadows: [
                          Shadow(
                            blurRadius: 5.0,
                            color: LikeWalletAppTheme.black.withOpacity(0.5),
                            offset: Offset(0.0, 0.0),
                          ),
                        ],
                        color: LikeWalletAppTheme.bule1,
                        fontFamily: 'Proxima Nova',
                        fontWeight: FontWeight.w300,
                        fontSize: screenWidth * 0.05), // Responsive font size
                  ),
                  Text(
                    'lock_detail_unlock'.tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        shadows: [
                          Shadow(
                            blurRadius: 5.0,
                            color: LikeWalletAppTheme.black.withOpacity(0.5),
                            offset: Offset(0.0, 0.0),
                          ),
                        ],
                        color: LikeWalletAppTheme.bule1,
                        fontFamily: 'Proxima Nova',
                        fontWeight: FontWeight.w300,
                        fontSize: screenWidth * 0.05), // Responsive font size
                  ),
                ],
              )),
          SizedBox(height: screenHeight * 0.15), // Responsive height
          Container(
            margin: EdgeInsets.symmetric(horizontal: screenWidth * 0.08),
              width: double.infinity,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  GestureDetector(
                      onTap: () {
                        lockNearnCtrl.changeOnTapUnlock();
                        lockNearnCtrl.onTapUnlock.value = false;
                      },
                      child: Container(
                          child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: <Widget>[
                          Image.asset(
                            LikeWalletImage.icon_button_cancel,
                            height: screenHeight * 0.05, // Responsive height
                          ),
                          SizedBox(
                            width: screenWidth * 0.02, // Responsive width
                          ),
                          Text(
                            'lock_button_cancel'.tr,
                            style: TextStyle(
                                color: LikeWalletAppTheme.bule1,
                                letterSpacing: 0.1,
                                fontFamily: 'Proxima Nova',
                                fontWeight: FontWeight.w300,
                                fontSize:
                                    screenWidth * 0.04), // Responsive font size
                          )
                        ],
                      ))),
                  GestureDetector(
                      onTap: () async {
                        // Confirm action implementation (similar to your old code)
                        await lockNearnCtrl.handleConfirmAction();
                      },
                      child: Container(
                          alignment: Alignment.center,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: <Widget>[
                              Text(
                                'lock_button_confirm'.tr,
                                style: TextStyle(
                                    color: LikeWalletAppTheme.bule1,
                                    letterSpacing: 0.1,
                                    fontFamily: 'Proxima Nova',
                                    fontWeight: FontWeight.w300,
                                    fontSize: screenWidth *
                                        0.04), // Responsive font size
                              ),
                              SizedBox(
                                width: screenWidth * 0.02, // Responsive width
                              ),
                              Image.asset(
                                LikeWalletImage.icon_button_next,
                                height: screenHeight * 0.05, // Responsive height
                              ),
                            ],
                          ))),
                ],
              )),
        ],
      );
    }

  // ส่วน build ปุ่ม All
    Widget _buildSetAllButton(context) {
      final screenWidth = MediaQuery.of(context).size.width;
      final screenHeight = MediaQuery.of(context).size.height;

      return Obx(() => lockNearnCtrl.onTapUnlock.value
          ? Padding(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.08),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () {
                      lockNearnCtrl.onTapUnlock.value = false;
                      // ไม่ต้องแก้อะไร ไม่ต้องเรียก action อื่น
                    },
                    child: Container(
                      width: screenWidth * 0.15, // Responsive width
                      height: screenHeight * 0.03, // Responsive height
                      padding: EdgeInsets.symmetric(
                          horizontal: screenWidth * 0.01,
                          vertical: screenHeight * 0.005),
                      decoration: BoxDecoration(
                        color: Color(0xff17171e),
                        borderRadius: BorderRadius.circular(30),
                        boxShadow: [
                          BoxShadow(
                              color: Colors.black.withOpacity(0.3),
                              blurRadius: 6,
                              offset: Offset(0, 3))
                        ],
                      ),
                      child: Center(
                        child: Text(
                          'lock_button_all'.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Color(0xff00ffff),
                            fontSize: screenWidth * 0.03, // Responsive font size
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            )
          : SizedBox());
    }

    Widget _buildConfirmButtons() {
      return Obx(() => AnimatedSwitcher(
            duration: Duration(milliseconds: 300),
            transitionBuilder: (child, animation) {
              final offsetAnimation = Tween<Offset>(
                begin: Offset(1.0, 0.0), // จากขวาเข้า
                end: Offset.zero,
              ).animate(
                  CurvedAnimation(parent: animation, curve: Curves.easeOut));

              return SlideTransition(position: offsetAnimation, child: child);
            },
            child: lockNearnCtrl.showConfirmButtons.value
                ? Padding(
                    key: ValueKey(
                        'confirm_buttons'), // ต้องใช้ key เพื่อให้ switch ทำงาน
                    padding: EdgeInsets.symmetric(horizontal: 12.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: TextButton(
                              onPressed: lockNearnCtrl.cancelAction,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Image.asset(LikeWalletImage.icon_button_cancel,
                                      width: 30, height: 30),
                                  SizedBox(width: 6),
                                  Text('ยกเลิก',
                                      style: TextStyle(color: Colors.cyan)),
                                ],
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: TextButton(
                              onPressed: lockNearnCtrl.handleActionButton,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text('ยืนยัน',
                                      style: TextStyle(color: Colors.cyan)),
                                  SizedBox(width: 6),
                                  Image.asset(LikeWalletImage.icon_button_next,
                                      width: 30, height: 30),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                : SizedBox(
                    key: ValueKey('empty_space'),
                  ),
          ));
    }

    Widget _buildCarousel() {
      return Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Image.asset(LikeWalletImage.locklike_BG_slider,
              fit: BoxFit.cover, width: double.infinity, height: 140),
          CarouselSlider(
            options: CarouselOptions(
                height: 140,
                autoPlay: true,
                enlargeCenterPage: true,
                viewportFraction: 1),
            items: lockNearnCtrl.carouselImages.map((imgPath) {
              return Builder(
                builder: (BuildContext context) {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.asset(imgPath, width: double.infinity),
                  );
                },
              );
            }).toList(),
          ),
        ],
      );
    }
  }

  class ThousandsFormatter extends TextInputFormatter {
    final NumberFormat _formatter = NumberFormat("#,###");

    @override
    TextEditingValue formatEditUpdate(
        TextEditingValue oldValue, TextEditingValue newValue) {
      // Remove all non-digit characters
      String digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');

      if (digitsOnly.isEmpty) return newValue.copyWith(text: '');

      final number = int.parse(digitsOnly);
      final newString = _formatter.format(number);

      return TextEditingValue(
        text: newString,
        selection: TextSelection.collapsed(offset: newString.length),
      );
    }
  }
