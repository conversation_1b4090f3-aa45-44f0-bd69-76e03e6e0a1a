import 'dart:async';

import 'package:flutter/material.dart';
import 'package:focus_detector/focus_detector.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/controller/historyController/historyController.dart';
import 'package:intl/intl.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:likewallet/model/historyModel/transactionModel.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/view/notification/statementPage.dart';
import 'package:webview_flutter/webview_flutter.dart';

class HistoryPage extends StatefulWidget {
  const HistoryPage({super.key});

  @override
  State<HistoryPage> createState() => _HistoryPageState();
}

class _HistoryPageState extends State<HistoryPage> {

  final historyCtrl = Get.isRegistered<HistoryController>()
      ? Get.find<HistoryController>()
      : Get.put(HistoryController());

  var account = Storage.get(StorageKeys.addressETH) ?? "";

  String? expandedItemKey;

  String formatDate(String rawDate) {
    try {
      final date = DateTime.parse(rawDate);
      // แบบภาษาไทย:
      return DateFormat('d MMMM yyyy', 'th').format(date);
      // หรือถ้าอยากได้แบบอังกฤษ:
      // return DateFormat('MMMM d, yyyy').format(date);
    } catch (e) {
      return rawDate; // fallback
    }
  }

  // bool _isForward() {
  //   final order = ['All', 'Rewards', '+', '-'];
  //   return order.indexOf(selectedFilter.value) >
  //       order.indexOf(previousFilter.value);
  // }

  // Map<String, List<Map<String, String>>> get mockDataAll => {};
  //
  // Map<String, List<Map<String, String>>> get mockDataPlus => {
  //   "2025-05-20": [
  //     {"title": "รับ Like Point", "amount": "+20", "type": "reward"},
  //   ],
  //   "2025-05-19": [
  //     {"title": "รับ Like Point", "amount": "+50", "type": "reward"},
  //     {"title": "โอน Like Point2", "amount": "-50", "type": "transaction"},
  //     {"title": "รับ Like Point3", "amount": "+50", "type": "reward"},
  //   ],
  // };

  String _formatDateToEnglish(String dateString) {
    try {
      DateTime date = DateFormat('dd MMMM yyyy', 'th').parse(dateString);
      return DateFormat('dd MMMM yyyy', 'en').format(date);
    } catch (e) {
      return dateString;
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return  FocusDetector(
      onFocusGained: () {
        historyCtrl.getHistory();
      },
      child: Obx(() => Scaffold(
        // floatingActionButton: Padding(
        //   padding: const EdgeInsets.only(bottom: 200),
        //   child: FloatingActionButton(
        //     onPressed: () {
        //       // print(Get.locale?.languageCode);
        //       historyCtrl.getHistory();
        //     },
        //     child: Icon(Icons.add),
        //   ),
        // ),
        body: Stack(
          children: [
            Container(
              height: 1.sh,
              width: 1.sw,
              color: Color(0x4dffffff),
            ),
            Column(
              children: [
                Container(
                  height: 140.h,
                  color: const Color(0xff201F2D).withOpacity(0.9),
                  padding: EdgeInsets.only(bottom: 10.h),
                  alignment: Alignment.topCenter,
                  child: _appBar(),
                ),
                Expanded(
                  child: historyCtrl.tabSelect.value == 1
                      ? _buildTransactionList()
                      : StatementPage()
                ),

              ],
            ),

            // ✅ ปุ่ม Overlay Filter
            Positioned(
              top: 120.h,
              left: 0,
              right: 0,
              child: historyCtrl.tabSelect.value != 1
                  ? Padding(
                padding: EdgeInsets.symmetric(horizontal: 100.w),
                child: Container(
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: const Color(0xff201F2D),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Center(
                    child: Text(
                      "statement_look".tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Color(0xff08e8de),
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                ),
              )
                  : Padding(
                padding: EdgeInsets.symmetric(horizontal: 30.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _filterButton('history_all'.tr, isCircle: false),
                    _filterButton('history_rewards'.tr, isCircle: false),
                    _filterButton('+', isCircle: true),
                    _filterButton('-', isCircle: true),
                  ],
                ),
              ),
            ),

      ],
        ),
      ),
    )
    );
    
  }

  // Widget _buildCard(item, {bool isExpanded = false, VoidCallback? onTap}) {
  //   return GestureDetector(
  //     onTap: onTap,
  //     child: Container(
  //       decoration: BoxDecoration(
  //         color: Colors.white,
  //         borderRadius: BorderRadius.circular(12.r),
  //         boxShadow: [
  //           BoxShadow(
  //             color: Colors.black12,
  //             blurRadius: 6,
  //             offset: Offset(0, 2),
  //           ),
  //         ],
  //       ),
  //       padding: EdgeInsets.symmetric(vertical: 14.h, horizontal: 16.w),
  //       margin: EdgeInsets.only(bottom: 7.h,top: 7.h),
  //       child: Column(
  //         children: [
  //           // ส่วนแรก - แสดงเหมือนเดิม
  //           Row(
  //             children: [
  //               // ICON ฝั่งซ้าย
  //               Container(
  //                 margin: EdgeInsets.only(
  //                   top: 0,
  //                   right: mediaQuery(context, 'width', 25),
  //                 ),
  //                 child: SvgPicture.asset(
  //                   item.type == 'transaction' && item.accountNumber != account
  //                       ? LikeWalletImage.icon_plus
  //                       : LikeWalletImage.icon_minus,
  //                   height: 24.h,
  //                 ),
  //               ),
  //               // Text กลาง
  //               Expanded(
  //                 child: Column(
  //                   crossAxisAlignment: CrossAxisAlignment.start,
  //                   mainAxisAlignment: MainAxisAlignment.center,
  //                   children: [
  //                     Text(
  //                       item.type == 'transaction' && item.accountNumber != account
  //                           ? 'history_received'.tr
  //                           : 'history_send'.tr,
  //                       style: TextStyle(
  //                         color: LikeWalletAppTheme.black.withOpacity(0.9),
  //                         letterSpacing: 0.3,
  //                         fontFamily: 'Proxima Nova',
  //                         fontSize: mediaQuery(context, 'height', 39),
  //                       ),
  //                       maxLines: 1,
  //                       overflow: TextOverflow.ellipsis,
  //                     ),
  //                     Row(
  //                       children: [
  //                         Text(
  //                           item.baht.toString(),
  //                           style: TextStyle(
  //                             color: LikeWalletAppTheme.gray3.withOpacity(0.9),
  //                             letterSpacing: 0.3,
  //                             fontFamily: 'Proxima Nova',
  //                             fontSize: mediaQuery(context, 'height', 36),
  //                           ),
  //                         ),
  //                         SizedBox(
  //                           width: mediaQuery(context, 'width', 25),
  //                         ),
  //                         Text(
  //                           'LIKE',
  //                           style: TextStyle(
  //                             color: LikeWalletAppTheme.gray3.withOpacity(0.9),
  //                             letterSpacing: 0.3,
  //                             fontWeight: FontWeight.w100,
  //                             fontFamily: 'Proxima Nova',
  //                             fontSize: mediaQuery(context, 'height', 36),
  //                           ),
  //                         ),
  //                       ],
  //                     ),
  //                   ],
  //                 ),
  //               ),
  //               // ขวาสุด 2 บรรทัด (from + เวลา)
  //               Column(
  //                 crossAxisAlignment: CrossAxisAlignment.end,
  //                 children: [
  //                   Container(
  //                     width: 100.w,
  //                     child: Text(
  //                       item.to.toString() == 'no' ? 'Kick Back' : item.to.toString(),
  //                       style: TextStyle(
  //                         fontSize: 13.sp,
  //                         color: Colors.grey.shade800,
  //                         fontWeight: FontWeight.w500,
  //                       ),
  //                       maxLines: 1,
  //                       overflow: TextOverflow.ellipsis,
  //                       textAlign: TextAlign.right,
  //                     ),
  //                   ),
  //                   SizedBox(height: 8.h),
  //                   Text(
  //                     DateFormat('HH:mm a').format(
  //                       DateTime.fromMillisecondsSinceEpoch(item.updateTime * 1000),
  //                     ),
  //                     style: TextStyle(
  //                       fontSize: 12.sp,
  //                       color: Colors.grey.shade400,
  //                       fontWeight: FontWeight.w400,
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //             ],
  //           ),
  //
  //           // ส่วนที่สอง - แสดงเมื่อกดแล้ว (status และ message)
  //           if (isExpanded) ...[
  //             Row(
  //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //               children: [
  //                 Row(
  //                   children: [
  //                     Container(
  //                       margin: EdgeInsets.only(
  //                         top: 0,
  //                         right: mediaQuery(context, 'width', 90),
  //                       ),
  //                     ),
  //                     Column(
  //                       crossAxisAlignment: CrossAxisAlignment.start,
  //                       children: [
  //                         // Status
  //                         GestureDetector(
  //                           onTap: () {
  //                             if(item.status == "3") {
  //
  //                             } else {
  //
  //                             }
  //                           },
  //                           child: Container(
  //                             child: Text(
  //                               item.status == "3" ? "history_transaction_success".tr : "history_transaction".tr,
  //                               style: TextStyle(
  //                                 color: item.status == "3" ? Color(0xFF909E53) : Color(0xFFFFA600),
  //                                 letterSpacing: 0.3,
  //                                 fontFamily: "ProximaNova",
  //                                 fontSize: mediaQuery(context, 'height', 36),
  //                                 fontWeight: FontWeight.w500,
  //                               ),
  //                             ),
  //                           ),
  //                         ),
  //                           Text(
  //                             "message_note".tr + ' : ${item.message}',
  //                             style: TextStyle(
  //                               color: LikeWalletAppTheme.gray3.withOpacity(0.9),
  //                               letterSpacing: 0.3,
  //                               fontFamily: 'Proxima Nova',
  //                               fontSize: mediaQuery(context, 'height', 34),
  //                             ),
  //                             maxLines: 2,
  //                             overflow: TextOverflow.ellipsis,
  //                           ),
  //                       ],
  //                     ),
  //                   ],
  //                 ),
  //                 Column(
  //                   crossAxisAlignment: CrossAxisAlignment.start,
  //                   children: [
  //                     Text(
  //                       DateFormat('dd MMMM yyyy', Get.locale?.languageCode ?? 'en').format(
  //                         DateTime.fromMillisecondsSinceEpoch(item.updateTime * 1000),
  //                       ),
  //                       style: TextStyle(
  //                         fontSize: 12.sp,
  //                         color: Colors.grey.shade400,
  //                         fontWeight: FontWeight.w400,
  //                       ),
  //                     ),
  //                     Text(
  //                       '',
  //                       style: TextStyle(
  //                         color: LikeWalletAppTheme.gray3.withOpacity(0.9),
  //                         letterSpacing: 0.3,
  //                         fontFamily: 'Proxima Nova',
  //                         fontSize: mediaQuery(context, 'height', 34),
  //                       ),
  //                       maxLines: 2,
  //                       overflow: TextOverflow.ellipsis,
  //                     ),
  //                   ],
  //                 ),
  //               ],
  //             ),
  //           ],
  //         ],
  //       ),
  //     ),
  //   );
  // }
  Widget _buildCard(item, {
    bool isExpanded = false,
    String? title,
    VoidCallback? onTap,
    required bool isReceive, // เพิ่ม isReceive
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 6,
              offset: Offset(0, 2),
            ),
          ],
        ),
        padding: EdgeInsets.symmetric(vertical: 14.h, horizontal: 16.w),
        margin: EdgeInsets.only(bottom: 7.h, top: 7.h),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  margin: EdgeInsets.only(
                    right: mediaQuery(context, 'width', 25),
                  ),
                  child: SvgPicture.asset(
                       title == "recv"
                        ? LikeWalletImage.icon_plus
                        : LikeWalletImage.icon_minus,
                    height: 24.h,
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        // title.toString(),
                        title == "recv"
                            ? 'history_received'.tr
                            : 'history_send'.tr,
                        style: TextStyle(
                          color: LikeWalletAppTheme.black.withOpacity(0.9),
                          letterSpacing: 0.3,
                          fontFamily: 'Proxima Nova',
                          fontSize: mediaQuery(context, 'height', 39),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Row(
                        children: [
                          Text(
                            _formatBaht(item.baht, currency: item.currency),
                            style: TextStyle(
                              color: LikeWalletAppTheme.gray3.withOpacity(0.9),
                              letterSpacing: 0.3,
                              fontFamily: 'Proxima Nova',
                              fontSize: mediaQuery(context, 'height', 36),
                            ),
                          ),
                          // Text(
                          //   item.baht.toString(),
                          //   style: TextStyle(
                          //     color: LikeWalletAppTheme.gray3.withOpacity(0.9),
                          //     letterSpacing: 0.3,
                          //     fontFamily: 'Proxima Nova',
                          //     fontSize: mediaQuery(context, 'height', 36),
                          //   ),
                          // ),
                          SizedBox(width: mediaQuery(context, 'width', 25)),
                          Text(
                            item.currency.toString() == 'no'
                                ? 'Kick Back'
                                : item.currency.toString(),
                            // 'LIKE',
                            style: TextStyle(
                              color: LikeWalletAppTheme.gray3.withOpacity(0.9),
                              letterSpacing: 0.3,
                              fontWeight: FontWeight.w100,
                              fontFamily: 'Proxima Nova',
                              fontSize: mediaQuery(context, 'height', 36),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Container(
                      width: 100.w,
                      child: Text(
                        item.to.toString() == 'no' ? 'Kick Back' : item.to.toString(),//...........
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: Colors.grey.shade800,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.right,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      DateFormat('HH:mm a').format(
                        DateTime.fromMillisecondsSinceEpoch(item.updateTime * 1000),
                      ),
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey.shade400,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            if (isExpanded) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      SizedBox(width: mediaQuery(context, 'width', 90)),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          GestureDetector(
                            onTap: () {},
                            child: Text(
                              item.status == "3"
                                  ? "history_transaction_success".tr
                                  : "history_transaction".tr,
                              style: TextStyle(
                                color: item.status == "3"
                                    ? const Color(0xFF909E53)
                                    : const Color(0xFFFFA600),
                                letterSpacing: 0.3,
                                fontFamily: "ProximaNova",
                                fontSize: mediaQuery(context, 'height', 36),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          Text(
                            "message_note".tr + ' : ${item.message}',
                            style: TextStyle(
                              color: LikeWalletAppTheme.gray3.withOpacity(0.9),
                              letterSpacing: 0.3,
                              fontFamily: 'Proxima Nova',
                              fontSize: mediaQuery(context, 'height', 34),
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        DateFormat('dd MMMM yyyy', Get.locale?.languageCode ?? 'en')
                            .format(DateTime.fromMillisecondsSinceEpoch(item.updateTime * 1000)),
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey.shade400,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      const SizedBox(height: 2),
                      const Text(''),
                    ],
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
  // String _formatBaht(dynamic baht) {
  //   if (baht == null) return '0.00';
  //   try {
  //     final value = double.tryParse(baht.toString()) ?? 0.0;
  //     return value.toStringAsFixed(9); // ปรับจำนวนทศนิยมได้ตามต้องการ
  //   } catch (_) {
  //     return baht.toString();
  //   }
  // }

  String _formatBaht(dynamic baht, {required String currency }) {
  if (baht == null) return '0.00';
  try {
    final value = double.tryParse(baht.toString()) ?? 0.0;
    int decimalPlaces = (currency == 'BTC' || currency == "กรัม(GOLD)") ? 9 : 2;
    return value.toStringAsFixed(decimalPlaces);
  } catch (_) {
    return baht.toString();
  }
}


  Widget _filterButton(String text,
      {bool isCircle = false, bool isInverted = false}) {
    final isIcon = text == '+' || text == '-';
    return Obx(() {
      final isSelected = historyCtrl.selectedFilter.value == text;
      return GestureDetector(
        onTap: () {
          historyCtrl.previousFilter.value = historyCtrl.selectedFilter.value;
          historyCtrl.selectedFilter.value = text;
        },
        child: Container(
          width: isCircle ? 44.w : null,
          height: 44.w,
          padding: isCircle
              ? EdgeInsets.zero
              : EdgeInsets.symmetric(horizontal: 18.w),
          decoration: BoxDecoration(
              color: isSelected
                  ? Colors.white
                  : (isInverted ? Colors.white : LikeWalletAppTheme.gray4),
              shape: isCircle ? BoxShape.circle : BoxShape.rectangle,
              borderRadius: isCircle ? null : BorderRadius.circular(24.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black54,
                  blurRadius: 3,
                  offset: Offset(0, 0),
                ),
              ]
          ),
          child: Center(
            child: Container(
                height: 30.h,
              decoration: BoxDecoration(
                shape: isCircle ? BoxShape.circle : BoxShape.rectangle,
                borderRadius: isCircle ? null : BorderRadius.circular(24.r),
              ),
              alignment: Alignment.center,
              child: isIcon
                  ? text == '+' ? SvgPicture.asset(
                  LikeWalletImage.button_plus,
                  height: 28.h) :
                SvgPicture.asset(
                    LikeWalletImage.button_minus,
                    height: 28.h)
                  : Text(
                text,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  color: isSelected
                      ? Colors.black
                      : (isInverted ? Colors.black : Color(0xff08e8de)),
                ),
              ),
            )
          ),
        ),
      );
    });
  }

  Widget _appBar() {
    return Align(
      alignment: Alignment.center,
      child: Container(
        width: 350.0.w,
        height: 30.0.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.h),
          border: Border.all(width: 1.0.sp, color: const Color(0x6dffffff)),
        ),
        child: Stack(
          children: [
            Obx(() => AnimatedAlign(
              alignment: historyCtrl.tabSelect.value == 1
                  ? Alignment.centerLeft
                  : Alignment.centerRight,
              duration: const Duration(milliseconds: 300),
              curve: Curves.ease,
              child: Container(
                width: 175.0.w,
                height: 40.0.h,
                decoration: BoxDecoration(
                  color: const Color(0x4dffffff),
                  borderRadius: BorderRadius.circular(4.h),
                ),
              ),
            )),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _tabSelect(tabIndex: 1, translationKey: "recent_transaction"),
                _tabSelect(tabIndex: 2, translationKey: "statement"),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _tabSelect({required int tabIndex, required String translationKey,}) {
    return Expanded(
      child: InkWell(
        onTap: () => historyCtrl.changeTabSelection(tabIndex),
        child: Obx(() => Container(
          alignment: Alignment.center,
          height: double.infinity,
          child: Text(
            translationKey.tr,
            style: TextStyle(
              fontSize: 10.h,
              color: historyCtrl.tabSelect.value == tabIndex
                  ? Color(0xff08e8de)
                  : Color(0x9dffffff),
            ),
          ),
        )),
      ),
    );
  }

  bool mockUp = true;
  int status = 0;

  Widget like2Noti(mock, statusInwidget){
    return Padding(
      padding: EdgeInsets.only(
        top: mediaQuery(context, 'height', 20),
      ),
      child: GestureDetector(
        onTap: () {
          setState(() {
            mockUp = !mockUp;
          });
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.0),
            color: LikeWalletAppTheme.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 5.0,
                offset: const Offset(0.0, 0.0),
              ),
            ],
          ),
          // height: mediaQuery(context, 'height', 166),
          width: mediaQuery(context, 'width', 999),
          padding: EdgeInsets.only(
              top: mediaQuery(context, 'height', 30),
              bottom: mediaQuery(context, 'height', 30),
              right: mediaQuery(context, 'width', 57),
              left: mediaQuery(context, 'width', 57)),
          margin: EdgeInsets.only(
            right: mediaQuery(context, 'width', 40),
            left: mediaQuery(context, 'width', 40),
            bottom: mediaQuery(context, 'height', 10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Row(
                    children: <Widget>[
                      Container(
                        margin: EdgeInsets.only(
                          top: 0,
                          right: mediaQuery(context, 'width', 25),
                        ),
                        child: SvgPicture.asset(
                            LikeWalletImage.icon_plus,
                            height:
                            mediaQuery(context, 'height', 66)),
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            'history_received'.tr,
                            style: TextStyle(
                                color:
                                LikeWalletAppTheme.black.withOpacity(0.9),
                                letterSpacing: 0.3,
                                fontFamily: 'Proxima Nova',
                                fontSize: mediaQuery(context, 'height', 39)),
                          ),
                          Row(
                            children: <Widget>[
                              Text(
                                '0.00173',
                                style: TextStyle(
                                    color: LikeWalletAppTheme.gray3
                                        .withOpacity(0.9),
                                    letterSpacing: 0.3,
                                    fontFamily: 'Proxima Nova',
                                    fontSize:
                                    mediaQuery(context, 'height', 36)),
                              ),
                              SizedBox(
                                width: mediaQuery(context, 'width', 25),
                              ),
                              Text(
                                'BTC',
                                style: TextStyle(
                                    color: LikeWalletAppTheme.gray3
                                        .withOpacity(0.9),
                                    letterSpacing: 0.3,
                                    fontWeight: FontWeight.w100,
                                    fontFamily: 'Proxima Nova',
                                    fontSize:
                                    mediaQuery(context, 'height', 36)),
                              ),
                              SizedBox(
                                width: mediaQuery(context, 'width', 20),
                              ),

                            ],
                          )
                        ],
                      )
                    ],
                  ),
                  Expanded(
                    child: Container(),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: <Widget>[
                      Container(
                        width: 185.w,
                        alignment: Alignment.centerRight,
                        child: Text(
                          'history_from'.tr,
                          style: TextStyle(
                              color:
                              LikeWalletAppTheme.black.withOpacity(0.9),
                              fontFamily: 'Proxima Nova',
                              fontSize: mediaQuery(context, 'height', 39)),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        //+190 is timezone +7
                        DateFormat("HH:mm a","en_US")
                            .format(DateTime.fromMillisecondsSinceEpoch(
                            (1743148304) * 1000))
                            .toString(),
                        style: TextStyle(
                            color: LikeWalletAppTheme.gray3.withOpacity(0.9),
                            letterSpacing: 0.3,
                            fontWeight: FontWeight.w100,
                            fontFamily: 'Proxima Nova',
                            fontSize: mediaQuery(context, 'height', 39)),
                      ),
                    ],
                  ),
                ],
              ),
              Container(
                padding: EdgeInsets.only(
                  left: mediaQuery(context, 'width', 90),
                ),
                child: Text(
                  status == 0 ? 'In processing' : status == 1 ? 'Pending' : 'Success',
                  style: TextStyle(
                      color: status == 0 ? Color(0xFFE5C705) : status == 1 ? Color(0xFFFFA600) : Color(0xFF909E53),
                      letterSpacing: 0.3,
                      fontFamily: "ProximaNova",
                      fontSize: mediaQuery(
                          context, 'height', 39)),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget like2Gold(mock, statusInwidget){
    return Padding(
      padding: EdgeInsets.only(
        top: mediaQuery(context, 'height', 20),
      ),
      child: GestureDetector(
        onTap: () {
          print(status);
          if(status > 1){

            status = 0;
            setState(() { });
          } else {

            status++;
            setState(() {});
          }

          print(status);
        },
        child:
//          mock == true
//             ? Container(
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(8.0),
//               color: LikeWalletAppTheme.white,
//               boxShadow: [
//                 BoxShadow(
//                   color: Colors.grey.withOpacity(0.1),
//                   blurRadius: 5.0,
//                   offset: const Offset(0.0, 0.0),
//                 ),
//               ],
//             ),
//             // height: mediaQuery(context, 'height', 166),
//             // width: mediaQuery(context, 'width', 999),
//             padding: EdgeInsets.only(
//                 top: mediaQuery(context, 'height', 30),
//                 bottom: mediaQuery(context, 'height', 30),
//                 right: mediaQuery(context, 'width', 57),
//                 left: mediaQuery(context, 'width', 57)),
//             margin: EdgeInsets.only(
//               right: mediaQuery(context, 'width', 40),
//               left: mediaQuery(context, 'width', 40),
//               bottom: mediaQuery(context, 'height', 10),
//             ),

//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.start,
//               children: [
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: <Widget>[
//                     Row(
//                       crossAxisAlignment: CrossAxisAlignment.start,
// //                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: <Widget>[
//                         Container(
//                           margin: EdgeInsets.only(
//                             top: 0,
//                             right: mediaQuery(context, 'width', 25),
//                           ),
//                           child: SvgPicture.asset(
//                               LikeWalletImage.icon_plus,
//                               fit: BoxFit.fill,
//                               height: mediaQuery(context, 'height', 66)),
//                         ),
//                         Column(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: <Widget>[
//                             Text(
//                               'Withdraw',
//                               style: TextStyle(
//                                   color: LikeWalletAppTheme.black
//                                       .withOpacity(0.9),
//                                   letterSpacing: 0.3,
//                                   fontFamily:
//                                   'Proxima Nova',
//                                   fontSize:
//                                   mediaQuery(context, 'height', 39)),
//                             ),
//                             Row(
//                               children: <Widget>[
//                                 Text(
//                                   '1.15',
//                                   style: TextStyle(
//                                       color: LikeWalletAppTheme.gray3
//                                           .withOpacity(0.9),
//                                       letterSpacing: 0.3,
//                                       fontFamily: 'Proxima Nova',
//                                       fontSize: mediaQuery(
//                                           context, 'height', 36)),
//                                 ),
//                                 SizedBox(
//                                   width: mediaQuery(context, 'width', 25),
//                                 ),
//                                 Text(
//                                   'Gold(grams)',
//                                   style: TextStyle(
//                                       color: LikeWalletAppTheme.gray3
//                                           .withOpacity(0.9),
//                                       letterSpacing: 0.3,
//                                       fontWeight: FontWeight.w100,
//                                       fontFamily: 'Proxima Nova',
//                                       fontSize: mediaQuery(
//                                           context, 'height', 36)),
//                                 ),
//                                 SizedBox(
//                                   width: mediaQuery(context, 'width', 20),
//                                 ),
//                               ],
//                             ),
//                             Text(
//                               status == 0 ? 'In processing' : status == 1 ? 'In transit' : 'Delivered',
//                               style: TextStyle(
//                                   color: status == 0 ? Color(0xFFE5C705) : status == 1 ? Color(0xFFFFA600) : Color(0xFF909E53),
//                                   letterSpacing: 0.3,
//                                   fontFamily: "ProximaNova",
//                                   fontSize: mediaQuery(
//                                       context, 'height', 39)),
//                             ),
//                           ],
//                         )
//                       ],
//                     ),
//                     Column(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       crossAxisAlignment: CrossAxisAlignment.end,
//                       children: <Widget>[
//                         Container(
//                           width: 185.w,
//                           alignment: Alignment.centerRight,
//                           child: Text(
//                             'history_to'.tr,
//                             style: TextStyle(
//                                 color: LikeWalletAppTheme.black
//                                     .withOpacity(0.9),
//                                 fontFamily: 'Proxima Nova',
//                                 fontSize:
//                                 mediaQuery(context, 'height', 39)),
//                             overflow: TextOverflow.ellipsis,
//                           ),
//                         ),
//                         Text(
//                           //+190 is timezone +7
//                           " " +
//                               DateFormat("HH:mm a")
//                                   .format(
//                                   DateTime.fromMillisecondsSinceEpoch(
//                                       (1743148304) * 1000))
//                                   .toString(),
//                           style: TextStyle(
//                               color:
//                               LikeWalletAppTheme.gray3.withOpacity(0.9),
//                               letterSpacing: 0.3,
//                               fontWeight: FontWeight.w100,
//                               fontFamily: 'Proxima Nova',
//                               fontSize: mediaQuery(context, 'height', 39)),
//                         ),
//                         Row(
//                           children: [
//                             Text(
//                               //+190 is timezone +7
//                               DateFormat("dd MMMM yyyy", Get.locale?.languageCode == 'th' ? 'th' : 'en')
//                                   .format(
//                                   DateTime.fromMillisecondsSinceEpoch(
//                                       (1743148304) * 1000))
//                                   .toString(),
//                               style: TextStyle(
//                                   color: LikeWalletAppTheme.gray3
//                                       .withOpacity(0.9),
//                                   letterSpacing: 0.3,
//                                   fontWeight: FontWeight.w100,
//                                   fontFamily: 'Proxima Nova',
//                                   fontSize:
//                                   mediaQuery(context, 'height', 39)),
//                             ),

//                           ],
//                         )
//                       ],
//                     ),
//                   ],
//                 ),
//                 Container(
//                     padding: EdgeInsets.only(
//                       left: mediaQuery(context, 'width', 90),
//                     ),
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text("Shipping",
//                           style: TextStyle(
//                               color: Color(0xFFBFBFBF),
//                               fontFamily: "Proxima Nova",
//                               fontSize: 12),
//                         ),
//                         Row(
//                           children: [
//                             Text(": TH1234567891124L",
//                               style: TextStyle(
//                                   color: Color(0xFFBFBFBF),
//                                   fontFamily: "Proxima Nova",
//                                   fontSize: 12),
//                             ),
//                             Image.asset("assets/image/like2crypto/stash_copy-light.png", width: 20, height: 20)
//                           ],
//                         ),
//                         Text(
//                           'Flash Express',
//                           style: TextStyle(
//                             fontSize: 12,
//                             fontWeight: FontWeight.w400,
//                             fontFamily: 'Proxima Nova',
//                             color: Color(0xFF2FA2FA),
//                             decoration: TextDecoration.underline,),
//                         ),
//                         // SizedBox(height: 32.h),
//                       ],
//                     )
//                 ),
//               ],
//             )
//         )
//             :
             Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.0),
            color: LikeWalletAppTheme.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 5.0,
                offset: const Offset(0.0, 0.0),
              ),
            ],
          ),
          // height: mediaQuery(context, 'height', 166),
          width: mediaQuery(context, 'width', 999),
          padding: EdgeInsets.only(
              top: mediaQuery(context, 'height', 30),
              bottom: mediaQuery(context, 'height', 30),
              right: mediaQuery(context, 'width', 57),
              left: mediaQuery(context, 'width', 57)),
          margin: EdgeInsets.only(
            right: mediaQuery(context, 'width', 40),
            left: mediaQuery(context, 'width', 40),
            bottom: mediaQuery(context, 'height', 10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Container(

                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          margin: EdgeInsets.only(
                            top: 0,
                            right: mediaQuery(context, 'width', 25),
                          ),
                          child: SvgPicture.asset(
                              LikeWalletImage.icon_plus,
                              fit: BoxFit.fill,
                              height:
                              mediaQuery(context, 'height', 66)),
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Text(
                              "Withdraw",
                              style: TextStyle(
                                  color:
                                  LikeWalletAppTheme.black.withOpacity(0.9),
                                  letterSpacing: 0.3,
                                  fontFamily: 'Proxima Nova',
                                  fontSize: mediaQuery(context, 'height', 39)),
                            ),
                            Row(
                              children: <Widget>[
                                Text(
                                  '1.15',
                                  style: TextStyle(
                                      color: LikeWalletAppTheme.gray3
                                          .withOpacity(0.9),
                                      letterSpacing: 0.3,
                                      fontFamily: 'Proxima Nova',
                                      fontSize:
                                      mediaQuery(context, 'height', 36)),
                                ),
                                SizedBox(
                                  width: mediaQuery(context, 'width', 25),
                                ),
                                Text(
                                  'Gold(grams)',
                                  style: TextStyle(
                                      color: LikeWalletAppTheme.gray3
                                          .withOpacity(0.9),
                                      letterSpacing: 0.3,
                                      fontWeight: FontWeight.w100,
                                      fontFamily: 'Proxima Nova',
                                      fontSize:
                                      mediaQuery(context, 'height', 36)),
                                ),
                                SizedBox(
                                  width: mediaQuery(context, 'width', 20),
                                ),
                              ],
                            )
                          ],
                        )
                      ],
                    ),
                  ),

                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: <Widget>[
                      Container(
                        width: 400.w,
                        alignment: Alignment.centerRight,
                        child: Text(
                          'history_from'.tr,
                          style: TextStyle(
                              color:
                              LikeWalletAppTheme.black.withOpacity(0.9),
                              fontFamily: 'Proxima Nova',
                              fontSize: mediaQuery(context, 'height', 39)),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        //+190 is timezone +7
                        DateFormat("HH:mm a","en_US")
                            .format(DateTime.fromMillisecondsSinceEpoch(
                            (1743148304) * 1000))
                            .toString(),
                        style: TextStyle(
                            color: LikeWalletAppTheme.gray3.withOpacity(0.9),
                            letterSpacing: 0.3,
                            fontWeight: FontWeight.w100,
                            fontFamily: 'Proxima Nova',
                            fontSize: mediaQuery(context, 'height', 39)),
                      ),
                    ],
                  ),
                ],
              ),
              Container(
                padding: EdgeInsets.only(
                  left: mediaQuery(context, 'width', 90),
                ),
                child: Text(
                  status == 0 ? 'In processing' : status == 1 ? 'In transit' : 'Delicered',
                  style: TextStyle(
                      color: status == 0 ? Color(0xFFE5C705) : status == 1 ? Color(0xFFFFA600) : Color(0xFF909E53),
                      letterSpacing: 0.3,
                      fontFamily: "ProximaNova",
                      fontSize: mediaQuery(
                          context, 'height', 39)),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  // Widget _buildTransactionList() {
  //   return Obx(() {
  //     // Show loading indicator
  //     if (historyCtrl.isLoading.value) {
  //       return Center(
  //         child: Padding(
  //           padding: EdgeInsets.only(top: 100.h),
  //           child: Column(
  //             mainAxisAlignment: MainAxisAlignment.center,
  //             children: [
  //               CircularProgressIndicator(
  //                 color: Color(0xff08e8de),
  //               ),
  //               SizedBox(height: 16.h),
  //               Text(
  //                 'Loading transactions...',
  //                 style: TextStyle(
  //                   color: Colors.grey.shade600,
  //                   fontSize: 14.sp,
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //       );
  //     }
  //
  //     // Show error state
  //     if (historyCtrl.hasError.value) {
  //       return Center(
  //         child: Padding(
  //           padding: EdgeInsets.only(top: 100.h),
  //           child: Column(
  //             mainAxisAlignment: MainAxisAlignment.center,
  //             children: [
  //               Icon(
  //                 Icons.error_outline,
  //                 size: 48.h,
  //                 color: Colors.red.shade400,
  //               ),
  //               SizedBox(height: 16.h),
  //               Text(
  //                 'Failed to load transactions',
  //                 style: TextStyle(
  //                   color: Colors.grey.shade600,
  //                   fontSize: 16.sp,
  //                   fontWeight: FontWeight.w500,
  //                 ),
  //               ),
  //               SizedBox(height: 8.h),
  //               Text(
  //                 historyCtrl.errorMessage.value,
  //                 style: TextStyle(
  //                   color: Colors.grey.shade500,
  //                   fontSize: 12.sp,
  //                 ),
  //                 textAlign: TextAlign.center,
  //               ),
  //               SizedBox(height: 16.h),
  //               ElevatedButton(
  //                 onPressed: () => historyCtrl.getHistory(),
  //                 style: ElevatedButton.styleFrom(
  //                   backgroundColor: Color(0xff08e8de),
  //                 ),
  //                 child: Text(
  //                   'Retry',
  //                   style: TextStyle(color: Colors.white),
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //       );
  //     }
  //
  //     final rawList = {
  //       'All': historyCtrl.all,
  //       'Rewards': historyCtrl.reward,
  //       '+': historyCtrl.receive,
  //       '-': historyCtrl.sent,
  //     }[historyCtrl.selectedFilter.value] ?? historyCtrl.all;
  //
  //     // Show empty state
  //     if (rawList.isEmpty) {
  //       return Center(
  //         child: Padding(
  //           padding: EdgeInsets.only(top: 100.h),
  //           child: Column(
  //             mainAxisAlignment: MainAxisAlignment.center,
  //             children: [
  //               Icon(
  //                 Icons.receipt_long_outlined,
  //                 size: 48.h,
  //                 color: Colors.grey.shade400,
  //               ),
  //               SizedBox(height: 16.h),
  //               Text(
  //                 'No transactions found',
  //                 style: TextStyle(
  //                   color: Colors.grey.shade600,
  //                   fontSize: 16.sp,
  //                   fontWeight: FontWeight.w500,
  //                 ),
  //               ),
  //               SizedBox(height: 8.h),
  //               Text(
  //                 'Your transaction history will appear here',
  //                 style: TextStyle(
  //                   color: Colors.grey.shade500,
  //                   fontSize: 12.sp,
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //       );
  //     }
  //
  //     final Map<String, List<TransactionModel>> groupedData = {};
  //     for (var item in rawList) {
  //       try {
  //         final dateKey = formatDate(
  //           DateTime.fromMillisecondsSinceEpoch((item.updateTime ?? 0) * 1000).toString(),
  //         );
  //         groupedData.putIfAbsent(dateKey, () => []);
  //         groupedData[dateKey]!.add(item);
  //       } catch (e) {
  //         print('Error processing transaction item: $e');
  //       }
  //     }
  //
  //     final entries = groupedData.entries.toList();
  //
  //     return AnimatedSwitcher(
  //       duration: const Duration(milliseconds: 400),
  //       transitionBuilder: (child, animation) {
  //         final slideIn =
  //         Tween<Offset>(begin: const Offset(0, 0.2), end: Offset.zero)
  //             .animate(animation);
  //         final fadeIn =
  //         Tween<double>(begin: 0.0, end: 1.0).animate(animation);
  //
  //         return SlideTransition(
  //           position: slideIn,
  //           child: FadeTransition(
  //             opacity: fadeIn,
  //             child: child,
  //           ),
  //         );
  //       },
  //       child: RefreshIndicator(
  //         onRefresh: () async {
  //           await historyCtrl.getHistory();
  //         },
  //         color: const Color(0xff08e8de),
  //         backgroundColor: const Color(0xff201F2D),
  //         child: ListView.builder(
  //           key: ValueKey(historyCtrl.selectedFilter.value),
  //           padding: EdgeInsets.only(top: 40.h, bottom: 100.h),
  //           itemCount: entries.length,
  //           itemBuilder: (context, index) {
  //             final entry = entries[index];
  //             return Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 Padding(
  //                   padding: EdgeInsets.symmetric(vertical: 16.h),
  //                   child: Center(
  //                     child: Text(
  //                       Get.locale?.languageCode == 'th'
  //                           ? '${entry.key}'
  //                           : _formatDateToEnglish(entry.key),
  //                       style: TextStyle(
  //                         fontWeight: FontWeight.w500,
  //                         fontSize: 14.sp,
  //                         color: Colors.grey.shade600,
  //                       ),
  //                     ),
  //                   ),
  //                 ),
  //                 Column(
  //                   children: [
  //                     //like2Noti(mockUp, status),
  //                     //like2Gold(mockUp, status),
  //                     ...List.generate(entry.value.length, (i) {
  //                       final item = entry.value[i];
  //                       final itemKey = '${entry.key}_${i}_${item.tx ?? item.updateTime}';
  //                       final isExpanded = expandedItemKey == itemKey;
  //
  //                       return Padding(
  //                         padding: EdgeInsets.symmetric(horizontal: 20.w),
  //                         child: _buildCard(
  //                           item,
  //                           isExpanded: isExpanded,
  //                           onTap: () {
  //                             setState(() {
  //                               // ถ้า card นี้เปิดอยู่แล้ว ให้ปิด, ถ้าไม่ให้เปิด
  //                               expandedItemKey = expandedItemKey == itemKey ? null : itemKey;
  //                             });
  //                           },
  //                         ),
  //                       );
  //                     }),
  //                   ],
  //                 ),
  //
  //               ],
  //             );
  //           },
  //         ),
  //       ),
  //     );
  //   });
  // }
  Widget _buildTransactionList() {
    return Obx(() {
      if (historyCtrl.isLoading.value) {
        return Center(
          child: Padding(
            padding: EdgeInsets.only(top: 100.h),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(color: const Color(0xff08e8de)),
                SizedBox(height: 16.h),
                Text(
                  'Loading transactions...',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 14.sp),
                ),
              ],
            ),
          ),
        );
      }

      if (historyCtrl.hasError.value) {
        return Center(
          child: Padding(
            padding: EdgeInsets.only(top: 100.h),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 48.h, color: Colors.red.shade400),
                SizedBox(height: 16.h),
                Text(
                  'Failed to load transactions',
                  style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500),
                ),
                SizedBox(height: 8.h),
                Text(
                  historyCtrl.errorMessage.value,
                  style: TextStyle(color: Colors.grey.shade500, fontSize: 12.sp),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16.h),
                ElevatedButton(
                  onPressed: () => historyCtrl.getHistory(),
                  style: ElevatedButton.styleFrom(backgroundColor: const Color(0xff08e8de)),
                  child: const Text('Retry', style: TextStyle(color: Colors.white)),
                ),
              ],
            ),
          ),
        );
      }

      final filter = historyCtrl.selectedFilter.value;

      final rawList = {
        'All': historyCtrl.all,
        'Rewards': historyCtrl.reward,
        '+': historyCtrl.receive,
        '-': historyCtrl.sent,
      }[filter] ?? historyCtrl.all;

      if (rawList.isEmpty) {
        return Center(
          child: Padding(
            padding: EdgeInsets.only(top: 100.h),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.receipt_long_outlined, size: 48.h, color: Colors.grey.shade400),
                SizedBox(height: 16.h),
                Text(
                  'No transactions found',
                  style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500),
                ),
                SizedBox(height: 8.h),
                Text(
                  'Your transaction history will appear here',
                  style: TextStyle(color: Colors.grey.shade500, fontSize: 12.sp),
                ),
              ],
            ),
          ),
        );
      }

      final Map<String, List<TransactionModel>> groupedData = {};
      for (var item in rawList) {
        try {
          final dateKey = formatDate(
            DateTime.fromMillisecondsSinceEpoch((item.updateTime ?? 0) * 1000).toString(),
          );
          groupedData.putIfAbsent(dateKey, () => []);
          groupedData[dateKey]!.add(item);
        } catch (e) {
          print('Error processing transaction item: $e');
        }
      }

      final entries = groupedData.entries.toList();

      return AnimatedSwitcher(
        duration: const Duration(milliseconds: 400),
        transitionBuilder: (child, animation) {
          final slideIn = Tween<Offset>(begin: const Offset(0, 0.2), end: Offset.zero).animate(animation);
          final fadeIn = Tween<double>(begin: 0.0, end: 1.0).animate(animation);

          return SlideTransition(
            position: slideIn,
            child: FadeTransition(
              opacity: fadeIn,
              child: child,
            ),
          );
        },
        child: RefreshIndicator(
          onRefresh: () async {
            await historyCtrl.getHistory();
          },
          color: const Color(0xff08e8de),
          backgroundColor: const Color(0xff201F2D),
          child: ListView.builder(
            key: ValueKey(historyCtrl.selectedFilter.value),
            padding: EdgeInsets.only(top: 40.h, bottom: 100.h),
            itemCount: entries.length,
            itemBuilder: (context, index) {
              final entry = entries[index];
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    child: Center(
                      child: Text(
                        Get.locale?.languageCode == 'th'
                            ? '${entry.key}'
                            : _formatDateToEnglish(entry.key),
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14.sp,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                  ),
                  Column(
                    children: [
                      ...List.generate(entry.value.length, (i) {
                        final item = entry.value[i];
                        final itemKey = '${entry.key}_${i}_${item.tx ?? item.updateTime}';
                        final isExpanded = expandedItemKey == itemKey;
                        print("item.title = ${item.type}");
                        // ✅ ตรงนี้คือ logic ที่บอกว่า item ไหนคือ "รับ"
                        final isReceive = item.type == 'transaction' &&
                            (item.accountNumber != account || item.to == 'Like 2 Crypto');

                        return Padding(
                          padding: EdgeInsets.symmetric(horizontal: 20.w),
                          child: _buildCard(
                            item,
                            isExpanded: isExpanded,
                            title:item.title,
                            onTap: () {
                              setState(() {
                                expandedItemKey =
                                expandedItemKey == itemKey ? null : itemKey;
                              });
                            },
                            isReceive: isReceive,
                          ),
                        );
                      }),
                    ],
                  ),
                ],
              );
            },
          ),
        ),
      );
    });
  }

}

//URL
class WebOpenURL extends StatefulWidget {
  WebOpenURL({this.url});
  final String? url;
  @override
  _WebOpenURL createState() => _WebOpenURL(url: url);
}

class _WebOpenURL extends State<WebOpenURL> {
  _WebOpenURL({this.url});
  final String? url;

  final Completer<WebViewController> _controller =
  Completer<WebViewController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
//      appBar: AppBar(
//        backgroundColor: Colors.transparent,
//        title: const Text('Terms & Policy'),
//        // This drop down menu demonstrates that Flutter widgets can be shown over the web view.
//
//      ),
      // We're using a Builder here so we have a context that is below the Scaffold
      // to allow calling Scaffold.of(context) so we can show a snackbar.
      body: Builder(builder: (BuildContext context) {
        return WebView(
          initialUrl: url,
          javascriptMode: JavascriptMode.unrestricted,
          onWebViewCreated: (WebViewController webViewController) {
            _controller.complete(webViewController);
          },
          // TODO(iskakaushik): Remove this when collection literals makes it to stable.
          // ignore: prefer_collection_literals
          // javascriptChannels: <JavascriptChannel>[
          //   _toasterJavascriptChannel(context),
          // ].toSet(),
          navigationDelegate: (NavigationRequest request) {
            if (request.url.startsWith('https://www.youtube.com/')) {
              print('blocking navigation to $request}');
              return NavigationDecision.prevent;
            }
            print('allowing navigation to $request');
            return NavigationDecision.navigate;
          },
          onPageStarted: (String url) {
            print('Page started loading: $url');
          },
          onPageFinished: (String url) {
            print('Page finished loading: $url');
          },
          gestureNavigationEnabled: true,
        );
      }),
      floatingActionButton: favoriteButton(),
    );
  }

  // JavascriptChannel _toasterJavascriptChannel(BuildContext context) {
  //   return JavascriptChannel(
  //       name: 'Toaster',
  //       onMessageReceived: (JavascriptMessage message) {
  //         Scaffold.of(context).showSnackBar(
  //           SnackBar(content: Text(message.message)),
  //         );
  //       });
  // }

  Widget favoriteButton() {
    return FutureBuilder<WebViewController>(
        future: _controller.future,
        builder: (BuildContext context,
            AsyncSnapshot<WebViewController> controller) {
          if (controller.hasData) {
            return FloatingActionButton(
              onPressed: () async {
                Navigator.pop(context);
              },
              child: const Icon(Icons.arrow_back_ios),
            );
          }
          return Container();
        });
  }
}

//URL
class WebOpen extends StatefulWidget {
  WebOpen({this.txid});
  final String? txid;
  @override
  _WebOpen createState() => _WebOpen(txid: txid);
}

class _WebOpen extends State<WebOpen> {
  _WebOpen({this.txid});
  final String? txid;

  final Completer<WebViewController> _controller =
  Completer<WebViewController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
//      appBar: AppBar(
//        backgroundColor: Colors.transparent,
//        title: const Text('Terms & Policy'),
//        // This drop down menu demonstrates that Flutter widgets can be shown over the web view.
//
//      ),
      // We're using a Builder here so we have a context that is below the Scaffold
      // to allow calling Scaffold.of(context) so we can show a snackbar.
      body: Builder(builder: (BuildContext context) {
        return WebView(
          initialUrl: 'https://scan.tomochain.com/txs/' + txid.toString(),
          javascriptMode: JavascriptMode.unrestricted,
          onWebViewCreated: (WebViewController webViewController) {
            _controller.complete(webViewController);
          },
          // TODO(iskakaushik): Remove this when collection literals makes it to stable.
          // ignore: prefer_collection_literals
          javascriptChannels: <JavascriptChannel>[
            _toasterJavascriptChannel(context),
          ].toSet(),
          navigationDelegate: (NavigationRequest request) {
            if (request.url.startsWith('https://www.youtube.com/')) {
              print('blocking navigation to $request}');
              return NavigationDecision.prevent;
            }
            print('allowing navigation to $request');
            return NavigationDecision.navigate;
          },
          onPageStarted: (String url) {
            print('Page started loading: $url');
          },
          onPageFinished: (String url) {
            print('Page finished loading: $url');
          },
          gestureNavigationEnabled: true,
        );
      }),
      floatingActionButton: favoriteButton(),
    );
  }

  JavascriptChannel _toasterJavascriptChannel(BuildContext context) {
    return JavascriptChannel(
        name: 'Toaster',
        onMessageReceived: (JavascriptMessage message) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(message.message)),
          );
        });
  }

  Widget favoriteButton() {
    return FutureBuilder<WebViewController>(
        future: _controller.future,
        builder: (BuildContext context,
            AsyncSnapshot<WebViewController> controller) {
          if (controller.hasData) {
            return FloatingActionButton(
              onPressed: () async {
                Navigator.pop(context);
              },
              child: const Icon(Icons.arrow_back_ios),
            );
          }
          return Container();
        });
  }
}

class Websites {
  bool _isExpanded;
  List<String> contents = [];
  Websites(this._isExpanded, this.contents);
}

List<Websites> policies = [
  new Websites(false, [
    'Flutter',
  ]),
  new Websites(false, ['Android'])
];