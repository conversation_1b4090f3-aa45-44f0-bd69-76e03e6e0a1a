import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/scanController/scanController.dart';
import 'package:likewallet/controller/transferController/transferController.dart';
import 'package:likewallet/service/components.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

class ScanPageKYC extends StatefulWidget {
  const ScanPageKYC({Key? key}) : super(key: key);

  @override
  State<ScanPageKYC> createState() => _ScanPageKYCState();
}

class _ScanPageKYCState extends State<ScanPageKYC> {
  // QR code scanner controller
  QRViewController? controller;
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');

  // Get the scan controller
  late ScanController scanCtrl;

  // Get the transfer controller
  var transferCtrl = Get.find<TransferController>();

  @override
  void initState() {
    super.initState();

    // Initialize scan controller
    if (!Get.isRegistered<ScanController>()) {
      scanCtrl = Get.put(ScanController());
    } else {
      scanCtrl = Get.find<ScanController>();
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  // For hot reload (only available in debug mode)
  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller!.pauseCamera();
    } else if (Platform.isIOS) {
      controller!.resumeCamera();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => ModalProgressHUD(
      inAsyncCall: scanCtrl.isLoading.value,
      child: Scaffold(
        body: Stack(
          children: [
            // Background
            Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    LikeWalletAppTheme.bule2_3,
                    LikeWalletAppTheme.bule2_6,
                  ],
                ),
              ),
            ),

            // Content
            Column(
              children: [
                // App bar
                Container(
                  height: 100.h,
                  padding: EdgeInsets.only(top: 40.h),
                  child: Row(
                    children: [
                      IconButton(
                        icon: Icon(Icons.arrow_back_ios, color: Colors.white),
                        onPressed: () => Get.back(),
                      ),
                      Text(
                        'scanpay_title'.tr,
                        style: TextStyle(
                          fontFamily: 'Proxima Nova',
                          fontSize: 18.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),

                // Description
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  margin: EdgeInsets.only(bottom: 20.h),
                  child: Text(
                    'scanpay_details'.tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 14.sp,
                      color: Colors.white,
                    ),
                  ),
                ),

                // QR Scanner
                Expanded(
                  flex: 5,
                  child: Container(
                    margin: EdgeInsets.all(20.w),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: QRView(
                        key: qrKey,
                        onQRViewCreated: _onQRViewCreated,
                        overlay: QrScannerOverlayShape(
                          borderColor: LikeWalletAppTheme.bule1,
                          borderRadius: 10,
                          borderLength: 30,
                          borderWidth: 10,
                          cutOutSize: 300.w,
                        ),
                      ),
                    ),
                  ),
                ),

                // Bottom buttons
                Expanded(
                  flex: 2,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Scan button
                      Container(
                        width: 150.w,
                        height: 50.h,
                        margin: EdgeInsets.only(bottom: 20.h),
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: LikeWalletAppTheme.bule1,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                          ),
                          onPressed: () {
                            // Resume camera if paused
                            controller?.resumeCamera();
                          },
                          child: Text(
                            'scanpay_scan'.tr,
                            style: TextStyle(
                              fontFamily: 'Proxima Nova',
                              fontSize: 16.sp,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),

                      // Select photo button
                      GestureDetector(
                        onTap: () => scanCtrl.scanFromGallery(),
                        child: Text(
                          'scanpay_select'.tr,
                          style: TextStyle(
                            fontFamily: 'Proxima Nova',
                            fontSize: 14.sp,
                            color: Colors.white,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ));
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) async {
      // Pause camera to prevent multiple scans
      controller.pauseCamera();

      // Process the QR data
      await scanCtrl.processQRData(scanData.code);

      transferCtrl.setAddress(scanData.code.toString());
      // Resume camera after a delay if still on this screen
      Future.delayed(Duration(seconds: 2), () {
        if (mounted) {
          controller.resumeCamera();
        }
      });
    });
  }
}
