import 'dart:ui';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/loading.dart';
import 'package:likewallet/view/alert/alertPatchUpdate.dart';
import 'package:likewallet/view/login/passcode.dart';
import 'package:likewallet/view/login/signIn.dart';
import 'package:likewallet/view/navigationBar/mainNavigator.dart';
import 'package:likewallet/view/termAndPolicy/policyDialog.dart';
import 'package:likewallet/view/termAndPolicy/termConditionDialog.dart';

class IndexLike extends StatefulWidget {
  const IndexLike({super.key});

  @override
  State<StatefulWidget> createState() =>  _IndexLike();
}

class _IndexLike extends State<IndexLike> {

  bool agreementPolicy = false;
  bool agreementTermsAndCondition = false;

  bool checkBoxVal = false;
  bool showLoading = false;

  @override
  void initState() {
    super.initState();

    Future.delayed(
      const Duration(milliseconds: 2000),
          () {
        // อ่านค่า passcode ใหม่ทุกครั้งเพื่อให้แน่ใจว่าได้ข้อมูลล่าสุด
        final passStore = Storage.get(StorageKeys.passcode);
        print('IndexLike initState - passStore: $passStore');

        if(passStore != null && passStore.isNotEmpty){
          Get.off(() => const PasscodePage(), transition: Transition.rightToLeft, duration: const Duration(milliseconds: 500));
        } else {
          showLoading = true;
          setState(() {});
        }
      },
    );
  }

  void _setTermAndPolicy() {
    Storage.save(StorageKeys.agreementPolicy, agreementPolicy);
    Storage.save(StorageKeys.agreementTermsAndCondition, agreementTermsAndCondition);
  }

  buttonLanguage(text, value, context) {
    return TextButton(
        onPressed: () {
          print(value);

          Get.updateLocale(Locale(value));
          Navigator.of(context).pop();
        },
        child:  Text(
          text,
          style: TextStyle(
            color: LikeWalletAppTheme.gray4,
            fontSize: 27.sp,
            fontWeight: FontWeight.w100,
            fontFamily: 'Proxima Nova',
            shadows: <Shadow>[
              Shadow(
                offset: const Offset(0.0, 0.0),
                blurRadius: 3.0,
                color: Colors.grey.withOpacity(1),
              ),
            ],
          ),
        ));
  }

  dialogContent() {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            alignment: Alignment.bottomCenter,
            child: Container(
              width: MediaQuery.of(context).size.width * 0.9,
              height: MediaQuery.of(context).size.height * 0.4,
              // padding: const EdgeInsets.symmetric(vertical: 40),
              decoration: BoxDecoration(
                image: DecorationImage(
                  colorFilter: ColorFilter.mode(
                    LikeWalletAppTheme.white.withOpacity(0.7),
                    BlendMode.dstIn,
                  ),
                  image: const AssetImage("assets/image/index/modal_bg.png"),
                  fit: BoxFit.fill,
                ),
                borderRadius: BorderRadius.circular(40.0),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(40.0),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      buttonLanguage('language_us'.tr, "en", context),
                      buttonLanguage('language_thai'.tr, "th", context),
                      buttonLanguage('language_lao'.tr, "lo", context),
                      buttonLanguage('language_cam'.tr, "km", context),
                    ],
                  ),
                ),
              ),
            ),
          );
        }
    );
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
          alignment: Alignment.center,
          children: [
            SizedBox(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              child: Image.asset(
                'assets/image/back.png',
                fit: BoxFit.fill,
              ),
            ),
            showLoading ? Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  height: MediaQuery.of(context).size.height * 0.14,
                  alignment: Alignment.bottomCenter,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(),
                      Container(
                        width: MediaQuery.of(context).size.width * 0.3,
                        alignment: Alignment.centerLeft,
                        child: _language(context),
                      ),
                    ],
                  ),
                ),
                Container(
                  height: MediaQuery.of(context).size.height * 0.24,
                  alignment: Alignment.topCenter,
                  child: Column(
                    children: [
                      GestureDetector(
                        onTap: () async {

                        },
                        child:  Container(
                          child:  Image.asset(
                            'assets/image/logo.png',
                            height: MediaQuery.of(context).size.height * 0.085,
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      Image.asset(
                        'assets/image/likewallet_text.png',
                        width: MediaQuery.of(context).size.width * 0.23,
                      ),
                    ],
                  ),
                ),
                Container(
                  child: Column(
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.85,
                        child: RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: 'กรุณาศึกษาและยอมรับ ',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: Colors.white,
                                  fontFamily: 'Proxima Nova',
                                ),
                              ),
                              TextSpan(
                                text: 'ข้อตกลงในการใช้บริการ',
                                style: TextStyle(
                                  color: agreementTermsAndCondition
                                      ? LikeWalletAppTheme.bule1
                                      : Colors.orange,
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: 'Proxima Nova',
                                  decoration: TextDecoration.underline,
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () async {
                                    var resTermsAndCondition = await showDialog(
                                      barrierDismissible: false,
                                      context: context,
                                      builder: (context) {
                                        return const TermsAndConditionsDialog();
                                      },
                                    );
                                    setState(() {
                                      agreementTermsAndCondition = resTermsAndCondition;
                                    });
                                    _setTermAndPolicy();
                                  },
                              ),
                              TextSpan(
                                text: ' และ ',
                                style: TextStyle(
                                  fontFamily: 'Proxima Nova',
                                  fontSize: 16.sp,
                                  color: Colors.white,
                                ),
                              ),
                              TextSpan(
                                text: 'นโยบายความเป็นส่วนตัว',
                                style: TextStyle(
                                  color: agreementPolicy ? LikeWalletAppTheme.bule1 : Colors.orange,
                                  fontSize: 16.sp,
                                  fontFamily: 'Proxima Nova',
                                  fontWeight: FontWeight.bold,
                                  decoration: TextDecoration.underline,
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () async {
                                    var resPolicy = await showDialog(
                                      barrierDismissible: false,
                                      context: context,
                                      builder: (context) {
                                        return const PolicyDialog();
                                      },
                                    );
                                    setState(() {
                                      agreementPolicy = resPolicy;
                                    });
                                    _setTermAndPolicy();
                                  },
                              ),
                              TextSpan(
                                text: ' ของ LikeWallet เพื่อดำเนินการต่อ',
                                style: TextStyle(
                                  fontFamily: 'Proxima Nova',
                                  fontSize: 16.sp,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.85,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Checkbox(
                              value: checkBoxVal,
                              onChanged: (bool? value) async {
                                if (value != null) {
                                  checkBoxVal = value;
                                  agreementTermsAndCondition = value;
                                  agreementPolicy = value;
                                  Storage.save(StorageKeys.agreementPolicy, value);
                                  Storage.save(StorageKeys.agreementTermsAndCondition, value);
                                  setState(() {});
                                }
                              },
                              activeColor: LikeWalletAppTheme.bule1,
                              checkColor: Colors.white,
                              side: const BorderSide(color: Colors.white),
                            ),
                            SizedBox(
                              width: MediaQuery.of(context).size.width * 0.70,
                              child: Text(
                                'Please Accept Terms and Conditions and Privacy Policy',
                                style: TextStyle(
                                  fontFamily: 'Proxima Nova',
                                  fontSize: 16.sp,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 15.h,
                      ),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ButtonBar(
                            alignment: MainAxisAlignment.center,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  InkWell(
                                    onTap: () => {
                                      if (agreementTermsAndCondition &&
                                          agreementPolicy)
                                        {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(builder: (
                                                builder) => const Signin()),
                                          )
                                        }
                                    },
                                    child: Container(
                                      width: MediaQuery.of(context).size.width * 0.85,
                                      height: 46.h,
                                      decoration: BoxDecoration(
                                        color: (!agreementTermsAndCondition || !agreementPolicy) ? const Color(0xff00F1E0).withOpacity(0.4) : const Color(0xff00F1E0),
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      alignment: Alignment.center,
                                      child: Text(
                                        'login_login'.tr,
                                        style: TextStyle(
                                          color: const Color(0xff000000),
                                          fontSize: 18.h,
                                          fontWeight: FontWeight.bold,
                                          fontFamily: 'Proxima Nova',
                                        ),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  _register(context),
                                ],
                              )
                            ],
                          )
                        ],
                      ),
                      SizedBox(
                        height: MediaQuery.of(context).size.height * 0.05,
                      ),
                    ],
                  ),
                )
              ],
            ) : const CustomLoading(),
          ],
        ),
    );
  }

  Widget _register(context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          "login_dont".tr,
          style: TextStyle(
            color: LikeWalletAppTheme.white.withOpacity(0.75),
            fontSize: 16.sp,
            fontWeight: FontWeight.normal,
            fontFamily: 'Proxima Nova',
          ),
        ),
        SizedBox(
          width: 10.w,
        ),
        GestureDetector(
          onTap: () {
            // if (agreementTermsAndCondition && agreementPolicy) {
            //   Navigator.push(
            //       context,
            //       EnterExitRoute(
            //           exitPage: IndexLike(), enterPage: CreateWallet()));
            // }
          },
          child:  Text(
            'login_create'.tr,
            style: TextStyle(
              letterSpacing: 0.5,
              color: (!agreementTermsAndCondition || !agreementPolicy)
                  ? LikeWalletAppTheme.bule1.withOpacity(0.40)
                  : LikeWalletAppTheme.bule1.withOpacity(0.75),
              fontSize: 16.sp,
              fontWeight: FontWeight.normal,
              fontFamily: 'Proxima Nova',
            ),
          ),
        ),
      ],
    );
  }

  Widget _language(context) {
    return GestureDetector(
        onTap: () {
          dialogContent();
        },
        child: Container(
            height: 32.h,
            width: 82.w,
            decoration: BoxDecoration(
                border: Border.all(
                    width: 1,
                    color: LikeWalletAppTheme.white.withOpacity(0.5)),
                color: LikeWalletAppTheme.bule2,
                borderRadius: BorderRadius.circular(10)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                    alignment: Alignment.center,
                    width: 41.w,
                    child: Text(
                      'language_title'.tr,
                      style: TextStyle(
                        color: LikeWalletAppTheme.white.withOpacity(0.6),
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Proxima Nova',
                      ),
                    )),
                Container(
                    alignment: Alignment.centerLeft,
                    width: 32.w,
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: LikeWalletAppTheme.white.withOpacity(0.6),
                      size: 21.sp,
                    )),
              ],
            )));
  }
}