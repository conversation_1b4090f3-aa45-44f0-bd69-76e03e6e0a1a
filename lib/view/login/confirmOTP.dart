import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/profile/loginCotroller.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/loading.dart';
import 'package:likewallet/view/login/passcode.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';

class Confirmotp extends StatefulWidget {
  const Confirmotp({super.key});

  @override
  State<Confirmotp> createState() => _ConfirmotpState();
}

class _ConfirmotpState extends State<Confirmotp> {

  int checkRound = 0;
  int passCodeColo = 0;
  List<String> otp = [];
  String passCode = '';
  bool isLoading = false;

  LoginController loginCtrl = Get.find<LoginController>();

  // === Timer state ===
  late Timer _timer;
  int _secondsLeft = 60;
  bool _isExpired = false;

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  void _startTimer() {
    _secondsLeft = 60;
    _isExpired = false;
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (_secondsLeft > 0) {
        setState(() {
          _secondsLeft--;
        });
      } else {
        setState(() {
          _isExpired = true;
        });
        _timer.cancel();
      }
    });
  }

  // Format timer display as MM:SS
  String _formatTimer() {
    if (_isExpired) return "00:00";
    int minutes = _secondsLeft ~/ 60;
    int seconds = _secondsLeft % 60;
    return "${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
  }
  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  Future<void> setCode(pass) async {
    if (pass.toString() == 'remove') {
      setState(() {
        if (passCodeColo <= 0) {
          passCodeColo = 0;
          passCode = '';
          setState(() {
            otp.clear();
          });
        } else {
          passCodeColo -= 1;
          passCode = passCode.substring(0, passCode.length - 1);
          setState(() {
            otp.removeLast();
          });
        }
      });
    } else {
      setState(() {
        if(passCodeColo < 6){
          passCodeColo += 1;
          passCode += pass.toString();
          setState(() {
            otp.add(pass.toString());
          });
        } else {
          passCodeColo = 6;
        }
      });
    }
    print(passCode);
    if (passCodeColo == 6) {
      if (_isExpired) {
        Get.snackbar(
          'OTP หมดอายุ',
          'รหัส OTP ของคุณหมดอายุ กรุณาขอรหัสใหม่',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.BOTTOM, // หรือ TOP ตามต้องการ
          margin: EdgeInsets.all(16), // ให้มันลอยขึ้นจากขอบจอ
        );
        setState(() {
          passCodeColo = 0;
          passCode = '';
          otp.clear();
        });
        return;
      }
      //login
      /// do login
      debugPrint('passCode: $passCode');
      setState(() {
        isLoading = true;
      });
      var checkOTP = await loginCtrl.verifyOTP(passCode);
      debugPrint('letSee: $checkOTP');
      if (checkOTP == 'success') {

        late final profileCtrl ;
        profileCtrl = Get.isRegistered<ProfileController>() ? Get.find<ProfileController>() : Get.put(ProfileController());
        await profileCtrl.getCurrentUser();

        var dataUser = profileCtrl.user;
        await loginCtrl.createWallet(dataUser);
        setState(() {
          isLoading = false;
        });
        Get.off(() => const PasscodePage());
      } else {
        setState(() {
          isLoading = false;
        });
        print('not success');
      }
    }
  }

  Future<void> _resendOtp() async {
    setState(() {
      isLoading = true;
    });

    // 1. รีเซ็ตช่องกรอกและนับถอยหลัง
    passCodeColo = 0;
    passCode = '';
    otp.clear();
    _timer.cancel();
    _startTimer();

    // 2. เรียกส่ง OTP ใหม่
    await loginCtrl.requestOTP(loginCtrl.usePhone);

    setState(() {
      isLoading = false;
    });

    // 3. แจ้งเตือนส่งรหัสใหม่สำเร็จ
    Get.snackbar(
      'ส่งรหัสใหม่สำเร็จ',
      'กรุณาตรวจสอบ SMS รหัส OTP ใหม่',
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }


  @override
  Widget build(BuildContext context) {
    return ModalProgressHUD(
      inAsyncCall: isLoading,
      opacity: 0.1,
      progressIndicator: CustomLoading(),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: LikeWalletAppTheme.bule2_4,
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment(-1.0, -0.5),
              end: Alignment(1.0, 1.0),
              colors: [LikeWalletAppTheme.bule2_7, LikeWalletAppTheme.bule2_7],
              stops: [0.0, 1.0],
            ),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: MediaQuery.of(context).size.width * 0.075,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              // ส่วนบน: ปุ่มย้อนกลับและปุ่ม Resend
              SizedBox(height: MediaQuery.of(context).size.height * 0.075,),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  backButton(context, Colors.grey),
                  const SizedBox(height: 20),
                  Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'otp_n_code'.tr,
                            style: TextStyle(
                              color: LikeWalletAppTheme.gray7.withOpacity(0.3),
                              fontFamily: 'Proxima Nova',
                              fontSize: 14.sp,
                            ),
                          ),
                          const SizedBox(width: 8),
                          GestureDetector(
                            onTap: _isExpired ? _resendOtp : null,
                            child: Container(
                              width: 60.w,
                              height: 40.h,
                              decoration: BoxDecoration(
                                color: _isExpired
                                    ? LikeWalletAppTheme.gray7.withOpacity(0.7)
                                    : const Color(0xff000000).withOpacity(0.2),
                                borderRadius: BorderRadius.circular(5),
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                'otp_resend'.tr,
                                style: TextStyle(
                                  fontFamily: 'Proxima Nova',
                                  color: _isExpired
                                      ? LikeWalletAppTheme.black
                                      : Colors.white.withOpacity(0.2),
                                  fontSize: 13.sp,
                                  fontWeight: _isExpired ? FontWeight.w500 : FontWeight.normal,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _formatTimer(),
                        style: TextStyle(
                          color: _isExpired
                              ? Colors.red.withOpacity(0.7)
                              : LikeWalletAppTheme.gray7.withOpacity(0.6),
                          fontFamily: 'Proxima Nova',
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              // ส่วนกลาง: ข้อความและช่อง OTP
              Column(
                children: [
                  SizedBox(height: MediaQuery.of(context).size.height * 0.075,),
                  Text(
                    'otp_code'.tr,
                    style: TextStyle(
                      color: LikeWalletAppTheme.white,
                      fontFamily: 'Proxima Nova',
                      fontSize: 20.sp,
                    ),
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.01,),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(6, (index) {
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: 4.w),
                        child: Container(
                          width: 45.w,
                          height: 45.w,
                          decoration: const BoxDecoration(
                            color: LikeWalletAppTheme.bule2_6,
                            shape: BoxShape.circle,
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            index < otp.length ? otp[index] : '',
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontFamily: 'Proxima Nova',
                              color: Colors.white,
                            ),
                          ),
                        ),
                      );
                    }),
                  ),
                ],
              ),

              // ส่วนล่าง: ปุ่มตัวเลข
              Container(
                width: MediaQuery.of(context).size.width * 0.75,
                padding: EdgeInsets.symmetric(vertical: 20.h),
                child: GridView.count(
                  crossAxisCount: 3,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  childAspectRatio: 1.2,
                  mainAxisSpacing: 10.h,
                  crossAxisSpacing: 10.w,
                  children: [
                    // ปุ่ม 1-9
                    for (int i = 1; i <= 9; i++)
                      _buildNumberButton(i.toString()),
                    // ปุ่มว่าง (แทนเลข 0 ด้านซ้าย)
                    const SizedBox(),
                    // ปุ่ม 0
                    _buildNumberButton('0'),
                    // ปุ่มลบ
                    _buildClearButton(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNumberButton(String number) {
    return InkWell(
      onTap: () => setCode(number),
      borderRadius: BorderRadius.circular(20),
      child: Container(
        decoration: const BoxDecoration(
          color: LikeWalletAppTheme.bule2_6,
          shape: BoxShape.circle,
        ),
        alignment: Alignment.center,
        child: Text(
          number,
          style: TextStyle(
            fontSize: 30.sp,
            color: LikeWalletAppTheme.bule1,
            fontFamily: 'Nimbus Sans',
            fontWeight: FontWeight.w100,
          ),
        ),
      ),
    );
  }

  Widget _buildClearButton() {
    return InkWell(
      onTap: () => setCode('remove'),
      borderRadius: BorderRadius.circular(50),
      child: Container(
        alignment: Alignment.center,
        child: Image.asset(
          LikeWalletImage.icon_clear,
          color: LikeWalletAppTheme.bule1,
          height: 18.h,
        ),
      ),
    );
  }
}
