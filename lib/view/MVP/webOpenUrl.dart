import 'dart:async';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebOpenMVP extends StatefulWidget {
  final String url;
  const WebOpenMVP({Key? key, required this.url}) : super(key: key);

  @override
  State<WebOpenMVP> createState() => _WebOpenMVPState();
}

class _WebOpenMVPState extends State<WebOpenMVP> {
  final Completer<WebViewController> _controller = Completer<WebViewController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xff141322),
        leading: const SizedBox(),
        actions: [
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
      body: WebView(
        initialUrl: widget.url,
        javascriptMode: JavascriptMode.unrestricted,
        onWebViewCreated: (controller) {
          _controller.complete(controller);
        },
        navigationDelegate: (request) {
          // ตัวอย่าง block youtube (ถ้าอยากใช้)
          if (request.url.contains("youtube.com")) {
            return NavigationDecision.prevent;
          }
          return NavigationDecision.navigate;
        },
        onPageStarted: (url) => print("📥 Start loading: $url"),
        onPageFinished: (url) => print("✅ Finished loading: $url"),
        gestureNavigationEnabled: true,
      ),
    );
  }
}