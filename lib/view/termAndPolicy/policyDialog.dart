import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PolicyDialog extends StatefulWidget {
  const PolicyDialog({super.key});

  @override
  _PolicyDialog createState() => _PolicyDialog();
}

class _PolicyDialog extends State<PolicyDialog> {
  bool scrollEnd = false;

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async { return false; },
      child: Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.sp)),
        child: Column(
          children: [
            Expanded(
              child: NotificationListener<ScrollNotification>(
                onNotification: (scrollNotification) {
                  if (scrollNotification is ScrollUpdateNotification) {
                    if (scrollNotification.metrics.extentAfter <= 0.1 &&
                        !scrollEnd) {

                      setState(() {
                        scrollEnd = true;
                      });
                    }
                  }
                  return true;
                },
                child: FutureBuilder(
                  future:
                  Future.delayed(const Duration(milliseconds: 1500)).then((value) {
                    return rootBundle
                        .loadString('assets/markdown/privacy_policy.md');
                  }),
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      return Markdown(
                        data: snapshot.data.toString(),
                      );
                    }
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  },
                ),
              ),
            ),
            scrollEnd ? Column(
              children: [
                TextButton(
                  style: ButtonStyle(

                    shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(10.sp),
                          bottomRight: Radius.circular(10.sp),
                        ),
                      ),
                    ),
                  ),
                  onPressed: () => Navigator.of(context).pop(true),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(10.sp),
                        bottomRight: Radius.circular(10.sp),
                      ),
                    ),
                    alignment: Alignment.center,
                    height: 50,
                    width: double.infinity,
                    child: Text(
                      'accept',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 60.sp,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Proxima Nova',
                      ),
                    ),
                  ),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  style: ButtonStyle(
                    padding: WidgetStateProperty.all<EdgeInsetsGeometry>(EdgeInsets.zero),
                    backgroundColor: WidgetStateProperty.resolveWith<Color>(
                          (Set<WidgetState> states) {
                        return Colors.transparent;
                      },
                    ),
                    shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(10.sp),
                          bottomRight: Radius.circular(10.sp),
                        ),
                      ),
                    ),
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(10.sp),
                        bottomRight: Radius.circular(10.sp),
                      ),
                    ),
                    alignment: Alignment.center,
                    height: 50,
                    width: double.infinity,
                    child: Text(
                      'reject',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 60.sp,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Proxima Nova',
                      ),
                    ),
                  ),
                ),
              ],
            ) : Container(),
          ],
        ),
      ),
    );
  }
}
