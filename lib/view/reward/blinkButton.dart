
// Widget for blinking income button
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class MyBlinkingButton extends StatefulWidget {
  final double Income;

  const MyBlinkingButton({Key? key, required this.Income}) : super(key: key);

  @override
  _MyBlinkingButtonState createState() => _MyBlinkingButtonState();
}

class _MyBlinkingButtonState extends State<MyBlinkingButton> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final f = NumberFormat("#,##0.00", "en_US");

    return FadeTransition(
      opacity: _animationController,
      child: Container(
        width: 884.0,
        height: 132.0,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.0),
          gradient: LinearGradient(
            begin: Alignment(-1.0, -0.09),
            end: Alignment(1.01, 0.0),
            colors: [
              const Color(0xff485dfa),
              const Color(0xff7b75fd),
              const Color(0xffc28dff)
            ],
            stops: [0.0, 0.507, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0x59000000),
              offset: Offset(0, 3),
              blurRadius: 18,
            ),
          ],
        ),
        alignment: Alignment.center,
        child: Text(
          "Annual Income: ${f.format(widget.Income)} LIKE",
          style: TextStyle(
            fontSize: 18,
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontFamily: 'Proxima Nova',
          ),
        ),
      ),
    );
  }
}