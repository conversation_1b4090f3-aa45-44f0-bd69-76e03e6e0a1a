import 'package:flutter/material.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// CloseMaintenancePopup - A self-contained maintenance popup widget
///
/// This StatefulWidget displays a maintenance message popup that:
/// - Shows a maintenance notification to users
/// - Includes a close button for dismissal
/// - Is centered and visually distinct
/// - Contains all necessary data internally
/// - Prevents dismissal except via the close button
class CloseMaintenancePopup extends StatefulWidget {
  /// Constructor - No external parameters required
  const CloseMaintenancePopup({Key? key}) : super(key: key);

  @override
  State<CloseMaintenancePopup> createState() => _CloseMaintenancePopupState();
}

class _CloseMaintenancePopupState extends State<CloseMaintenancePopup>
    with SingleTickerProviderStateMixin {

  // Animation controller for smooth popup appearance
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  // Internal state for loading animation
  bool _isLoading = true;

  // Self-contained maintenance data
  static const String _maintenanceTitle = "Menu Maintenance";
  static const String _maintenanceMessage =
      "We're currently performing scheduled maintenance to enhance your experience. "
      "Please try again later.";
  static const String _estimatedTime = "Menu Maintenance is being set up";

  // Color scheme (self-contained)
  static const Color _primaryColor = Color(0xff00c5c2);
  static const Color _backgroundColor = Color(0xff141322);
  static const Color _cardColor = Color(0xff1e1e2e);
  static const Color _textColor = Colors.white;
  static const Color _buttonColor = Color(0xff00c5c2);

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _simulateLoadingDelay();
  }

  /// Initialize animations for smooth popup appearance
  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Start animation
    _animationController.forward();
  }

  /// Simulate loading delay for better UX
  void _simulateLoadingDelay() {
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  /// Handle close button tap with animation
  void _handleCloseButtonTap() {
    _animationController.reverse().then((_) {
      if (mounted) {
        Navigator.of(context).pop();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      // Prevent dismissal via back button or gestures
      canPop: false,
      child: Scaffold(
        backgroundColor: _backgroundColor.withOpacity(0.8),
        body: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Opacity(
              opacity: _opacityAnimation.value,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: _buildMaintenanceDialog(),
              ),
            );
          },
        ),
      ),
    );
  }

  /// Build the main maintenance dialog
  Widget _buildMaintenanceDialog() {
    return Center(
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 24.w),
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: _cardColor,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            SizedBox(height: 24.h),
            _buildContent(),
            SizedBox(height: 32.h),
            _buildCloseButton(),
          ],
        ),
      ),
    );
  }

  /// Build the header with icon and title
  Widget _buildHeader() {
    return Column(
      children: [
        // Maintenance icon
        Container(
          width: 80.w,
          height: 80.h,
          decoration: BoxDecoration(
            color: _primaryColor.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: _isLoading
              ? _buildLoadingIndicator()
              : Icon(
                  Icons.build_circle,
                  size: 40.sp,
                  color: _primaryColor,
                ),
        ),
        SizedBox(height: 16.h),
        // Title
        Text(
          _maintenanceTitle,
          style: TextStyle(
            fontSize: 24.sp,
            fontWeight: FontWeight.bold,
            color: _primaryColor,
            fontFamily: 'Proxima Nova',
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// Build the loading indicator
  Widget _buildLoadingIndicator() {
    return SizedBox(
      width: 40.w,
      height: 40.h,
      child: LoadingIndicator(
        indicatorType: Indicator.circleStrokeSpin,
        colors: [_primaryColor],
        strokeWidth: 3.0,
      ),
    );
  }

  /// Build the main content area
  Widget _buildContent() {
    return Column(
      children: [
        // Main message
        Text(
          _maintenanceMessage,
          style: TextStyle(
            fontSize: 16.sp,
            color: _textColor.withOpacity(0.8),
            height: 1.5,
            fontFamily: 'Proxima Nova',
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 16.h),
        // Estimated time
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 8.h,
          ),
          decoration: BoxDecoration(
            color: _primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: _primaryColor.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Text(
            _estimatedTime,
            style: TextStyle(
              fontSize: 14.sp,
              color: _primaryColor,
              fontWeight: FontWeight.w500,
              fontFamily: 'Proxima Nova',
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  /// Build the close button
  Widget _buildCloseButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _handleCloseButtonTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: _buttonColor,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 16.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          elevation: 4,
          shadowColor: _buttonColor.withOpacity(0.3),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.close,
              size: 20.sp,
            ),
            SizedBox(width: 8.w),
            Text(
              'Close',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                fontFamily: 'Proxima Nova',
              ),
            ),
          ],
        ),
      ),
    );
  }
}
