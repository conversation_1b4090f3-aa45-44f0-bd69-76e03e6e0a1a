import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/otherController/updatePatchController.dart';
import 'package:percent_indicator/flutter_percent_indicator.dart';
import 'package:restart_app/restart_app.dart';

class AlertUpdatePatchPage extends StatefulWidget {
  const AlertUpdatePatchPage({Key? key}) : super(key: key);

  @override
  State<AlertUpdatePatchPage> createState() => _AlertUpdatePatchPageState();
}

class _AlertUpdatePatchPageState extends State<AlertUpdatePatchPage> {

  // final centerCtl = Get.put(SettingController());
  final updatePatchCtl = Get.put(UpdatePatchController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff312F48).withOpacity(0.6),
      body: Stack(
        children: [
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 8.0, sigmaY: 8.0),
            child: SizedBox(
              width: 1.sw,
              height: 1.sh,
            ),
          ),
          SizedBox(
            width: 1.sw,
            height: 1.sh,
            child: Column(
              // mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 0.2.sh, right: 0.05.sw),
                  height: 0.1.sh,
                ),
                Image.asset(
                  "assets/icon/icon1.png",
                  width: 0.3.sw,
                ),
                SizedBox(
                  height: 0.02.sh,
                ),
                Obx(() => boldTextS(
                    context,
                    updatePatchCtl.restartApp.value == true
                        ? "อัปเดตแพทช์สำเร็จ!"
                        : "กำลังอัปเดตแพทช์...",
                    18.sp,
                    const Color(0xFF000000),
                    FontWeight.w500)),
                SizedBox(
                  height: 0.02.sh,
                ),
                Obx(() => updatePatchCtl.restartApp.value == true
                    ? Column(
                  children: [
                    boldTextS(
                        context,
                        "กดปุ่มรีสตาร์ทแอปด้านล่าง",
                        14.sp,
                        const Color(0xFFFFFFFF),
                        FontWeight.w400),
                    boldTextS(
                        context,
                        "และเข้าใช้งานแอปใหม่อีกครั้ง",
                        14.sp,
                        const Color(0xFFFFFFFF),
                        FontWeight.w400)
                  ],
                )
                    : Column(
                  children: [
                    boldTextS(
                        context,
                        "เพื่อเพิ่มประสิทธิภาพการใช้งาน",
                        14.sp,
                        const Color(0xFFFFFFFF),
                        FontWeight.w400),
                    boldTextS(
                        context,
                        "และรองรับสำหรบฟีเจอร์ใหม่",
                        14.sp,
                        const Color(0xFFFFFFFF),
                        FontWeight.w400)
                  ],
                )),
                SizedBox(
                  height: 0.04.sh,
                ),
                Obx(() => Container(
                  margin: EdgeInsets.only(
                    left: 0.06.sw,
                    right: 0.06.sw,
                  ),
                  child: updatePatchCtl.restartApp.value == true
                      ? InkWell(
                    onTap: () async {
                      /// In Web Platform, Fill webOrigin only when your new origin is different than the app's origin
                      Restart.restartApp();
                    },
                    child: Container(
                      width: 1.sw,
                      height: 0.06.sh,
                      margin: EdgeInsets.only(
                        left: 0.02.sw,
                        right: 0.02.sw,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(15),
                          topLeft: Radius.circular(15),
                          bottomRight: Radius.circular(15),
                          bottomLeft: Radius.circular(15),
                        ),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          stops: const [0.0, 1.0],
                          colors: [
                            const Color(0xFF000000).withOpacity(0.7),
                            const Color(0xFF000000),
                          ],
                        ),
                        border: Border.all(
                            width: 1, color: const Color(0xff146D6E)),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.2),
                            offset: const Offset(1, 2),
                            blurRadius: 3,
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                              "รีสตาร์ทแอป",
                              textAlign: TextAlign.left,
                              style: TextStyle(
                                fontFamily: 'Prompt-Medium',
                                fontSize: 15.sp,
                                color: Color(0xFFFFFFFF),
                                fontWeight: FontWeight.w400,
                              )
                          )
                        ],
                      ),
                    ),
                  )
                      : Column(
                    children: [
                      LinearPercentIndicator(
                        barRadius: const Radius.circular(10),
                        lineHeight: 8,
                        percent: updatePatchCtl
                            .downloadProgressNotifier.value /
                            100,
                        backgroundColor:
                        Colors.black.withOpacity(0.2),
                        progressColor: const Color(0xff0FE8D8),
                      ),
                      SizedBox(
                        height: 0.01.sh,
                      ),
                      Text(
                        "${updatePatchCtl.downloadProgressNotifier.value.round()}%",
                        textAlign: TextAlign.left,
                        style: TextStyle(
                          fontFamily: 'Prompt',
                          fontSize: 14.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.w400,
                        ),
                      )
                    ],
                  ),
                ))
              ],
            ),
          ),
        ],
      ),
    );
  }

  boldTextS(context, text, size, color, FontWeight weight){
    return Text(
        text,
        textAlign: TextAlign.left,
        style: TextStyle(
          fontFamily: 'Prompt-Medium',
          fontSize: size.toDouble(),
          color: color,
          fontWeight: weight,
          // letterSpacing: 0.2,
          shadows: <Shadow>[
            Shadow(
              offset: const Offset(0, 1),
              blurRadius: 5.0,
              color: const Color(0xFF000000).withOpacity(0.2),
            ),
          ],
        )
    );
  }
}