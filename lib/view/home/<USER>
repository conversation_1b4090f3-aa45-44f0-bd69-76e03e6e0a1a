import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/view/home/<USER>';
import 'package:likewallet/view/home/<USER>';
import 'package:likewallet/view/home/<USER>';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool permissionIncome = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      drawer: AppDrawer(),
      body: Stack(
        children: [
          _groupBody(context),
          Column(
            children: [
              _head(context),
              const Expanded(
                child: HomeBodyScreen(),
              )

            ],
          ),
        ],
      ),
    );
  }

  Widget _head(BuildContext context) {
    return Opacity(
      opacity: 0.9,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: double.infinity,
        height: 235.h,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment(-1.0, -0.94),
            end: Alignment(1.0, 1.0),
            colors: [
              Color(0xff52fcf0),
              Color(0xff33faec),
              Color(0xff22c4e6),
            ],
            stops: [0.0, 0.335, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: Color(0x29000000),
              offset: Offset(0, 12),
              blurRadius: 25,
            ),
          ],
        ),
        child: Column(
          children: [
            SizedBox(height: 30.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: (){
                    _scaffoldKey.currentState?.openDrawer();
                  },
                  child: Container(
                    // width: 150.w,
                    width: Get.width,
                    height: 50.h,
                    color: Colors.transparent,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.only(left: 20.w),
                    child: SizedBox(
                      height: 30.h,
                      child: Image.asset(
                        LikeWalletImage.icon_menu,
                        height: 15.h,
                        width: 20.w,
                      ),
                    ),
                  ),
                ),
                // if (permissionIncome == false)
                //   GestureDetector(
                //     onTap: () => print('click Info Screen'),
                //     child: Container(
                //       width: 100.w,
                //       padding: EdgeInsets.only(top: 6.h),
                //       alignment: Alignment.center,
                //       child: SvgPicture.string(
                //         '<svg viewBox="507.0 888.0 65.9 33.4"><path transform="translate(242.47, 484.07)" d="M 330.4030151367188 405.9169921875 C 330.1220092773438 404.5660095214844 328.7980041503906 403.697998046875 327.4460144042969 403.97900390625 L 317.4020080566406 406.0679931640625 C 316.8819885253906 406.1759948730469 316.4100036621094 406.4469909667969 316.0539855957031 406.8420104980469 C 315.1300048828125 407.8680114746094 315.2130126953125 409.4490051269531 316.239013671875 410.3729858398438 L 319.2720031738281 413.10400390625 C 319.1499938964844 413.1849975585938 319.0320129394531 413.2730102539062 318.9230041503906 413.3770141601562 L 309.3269958496094 422.5969848632812 L 293.0740051269531 409.2040100097656 L 265.406005859375 432.9039916992188 C 264.3569946289062 433.8030090332031 264.2349853515625 435.3810119628906 265.1340026855469 436.4289855957031 C 265.6279907226562 437.0069885253906 266.3290100097656 437.3030090332031 267.0329895019531 437.3030090332031 C 267.6090087890625 437.3030090332031 268.18701171875 437.1050109863281 268.6589965820312 436.7009887695312 L 293.135986328125 415.7340087890625 L 309.5830078125 429.2860107421875 L 322.3880004882812 416.9830017089844 C 322.5880126953125 416.7900085449219 322.7380065917969 416.5679931640625 322.8599853515625 416.3349914550781 L 325.2550048828125 418.4909973144531 C 325.6499938964844 418.8469848632812 326.1489868164062 419.0679931640625 326.6780090332031 419.1210021972656 C 328.052001953125 419.2590026855469 329.2770080566406 418.2569885253906 329.4150085449219 416.8829956054688 L 330.4419860839844 406.677001953125 C 330.4679870605469 406.4230041503906 330.4549865722656 406.1669921875 330.4030151367188 405.9169921875 Z" fill="#0fe8d8" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
                //         height: 15.h,
                //         color: Colors.black.withOpacity(0.6),
                //       ),
                //     ),
                //   ),
                // const Spacer(),
              ],
            ),
            const Expanded(
              child: HomeHeadScreen(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _bg(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          height: double.infinity,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment(0.0, -1.0),
              end: Alignment(0.0, 1.0),
              colors: [Color(0xff1f2840), Color(0xff1e1d34)],
              stops: [0.0, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Color(0x33000000),
                offset: Offset(-9, 0),
                blurRadius: 25,
              ),
            ],
          ),
        ),
        Container(
          width: 120.w,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment(0.0, -1.0),
              end: Alignment(0.0, 1.0),
              colors: [Color(0xff212b44), Color(0xff1e1d34)],
              stops: [0.0, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Color(0x40000000),
                offset: Offset(-9, 0),
                blurRadius: 25,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _groupBody(BuildContext context) {
    return Stack(
      children: [
        _bg(context),
        Positioned(
          height: 530.h,
          width: 330.w,
          top: 65.h,
          right: -130.w,
          child: Image.asset(LikeWalletImage.home_bg),
        ),
      ],
    );
  }
}
