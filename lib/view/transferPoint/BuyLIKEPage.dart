import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/buyLikepointController/buyLikepointController.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/view/transferPoint/buyLikepoint/usingQRCode.dart';
import 'package:qr_flutter/qr_flutter.dart';

class BuyLikepointPage extends StatefulWidget {


  const BuyLikepointPage({super.key});

  @override
  State<BuyLikepointPage> createState() => _BuyLikepointPageState();
}

class _BuyLikepointPageState extends State<BuyLikepointPage> {

  final String walletAddress = Storage.get(StorageKeys.addressETH) ?? '';
  late final BuyLikepointController buyLikepointCtrl;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if (!Get.isRegistered<BuyLikepointController>()) {
      buyLikepointCtrl = Get.put(BuyLikepointController());
    } else {
      buyLikepointCtrl = Get.find<BuyLikepointController>();
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          SizedBox(height: 250.h),
          _transferOption(
            iconPath: 'assets/image/banking/buylike/qrcode.png',
            title: 'buylike_qrcode1'.tr,
            subtitle: 'buylike_qrcode2'.tr,
            note: 'buylike_qrcode'.tr,
          ),
          _transferOption(
            iconPath: 'assets/image/banking/buylike/bank.png',
            title: 'buylike_banking1'.tr,
            subtitle: 'buylike_banking2'.tr,
            note: 'buylike_banking'.tr,
          ),
          _transferOption(
            iconPath: 'assets/image/banking/buylike/bank.png',
            title: '${'buylike_banking1'.tr} RPLC & RAFCO',
            subtitle: 'buylike_banking2'.tr,
            note: 'buylike_banking'.tr,
          ),
          SizedBox(height: 100.h),
        ],
      ),
    );
  }

  Widget _iconAction(String assetPath, String label) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: Colors.grey.shade300,
          child: Image.asset(
            assetPath,
            width: 20.w,
            height: 20.h,
            fit: BoxFit.contain,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontFamily: 'Proxima Nova',
            fontSize: 12.sp,
            color: Colors.grey.shade700,
          ),
        ),
      ],
    );
  }

  Widget _transferOption({
    required String iconPath,
    required String title,
    required String subtitle,
    required String note,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              if(title == 'buylike_qrcode1'.tr) {
                buyLikepointCtrl.changeSelectPage('QRCode');
              } else {
                buyLikepointCtrl.changeSelectPage('Upload');
              }

              Get.to(()=> BuyLikepointUsingQRCode());
            },
            child: Container(
              height: 120.h,
              padding: EdgeInsets.all(24.sp),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Image.asset(
                    iconPath,
                    width: 36.w,
                    height: 36.h,
                    fit: BoxFit.contain,
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            fontFamily: 'Proxima Nova',
                            fontSize: 14.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontFamily: 'Proxima Nova',
                            fontSize: 12.sp,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 6.h),
          Align(
            alignment: Alignment.center,
            child: Text(
              note,
              style: TextStyle(
                fontFamily: 'Proxima Nova',
                fontSize: 11.sp,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
