import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/cashOutController/cashOutController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/view/transferPoint/cashOut/confirmCash.dart';

class ChoiceTopay extends StatefulWidget {
  final double amount;
  final double fee;
  final double rate;
  final String symbol;
  final double totalSell;

  const ChoiceTopay({
    Key? key,
    required this.amount,
    required this.fee,
    required this.rate,
    required this.symbol,
    required this.totalSell,
  }) : super(key: key);

  @override
  State<ChoiceTopay> createState() => _ChoiceTopayState();
}

class _ChoiceTopayState extends State<ChoiceTopay> {
  final cashOutCtrl = Get.find<CashOutController>();
  
  // Bank selection
  int selectedBank = -1;
  List<Map<String, dynamic>> banks = [];
  bool isLoading = true;
  
  // Bank account details
  final TextEditingController nameController = TextEditingController();
  final TextEditingController accountNumberController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    loadBanks();
  }
  
  @override
  void dispose() {
    nameController.dispose();
    accountNumberController.dispose();
    super.dispose();
  }
  
  // Load available banks from Firestore
  Future<void> loadBanks() async {
    setState(() {
      isLoading = true;
    });
    
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('banks')
          .where('country', isEqualTo: widget.symbol)
          .get();
      
      banks = snapshot.docs.map((doc) {
        return {
          'id': doc.id,
          'name': doc.data()['name'],
          'image': doc.data()['image'],
        };
      }).toList();
      
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print('Error loading banks: $e');
      setState(() {
        isLoading = false;
      });
    }
  }
  
  // Proceed to confirmation page
  void proceedToConfirmation() {
    if (selectedBank == -1) {
      Get.snackbar(
        'Error',
        'Please select a bank',
         snackPosition: SnackPosition.TOP,
                  backgroundColor: const Color(0xFF8CF9F0)
      );
      return;
    }
    
    if (nameController.text.isEmpty) {
      Get.snackbar(
        'Error',
        'Please enter account name',
         snackPosition: SnackPosition.TOP,
                  backgroundColor: const Color(0xFF8CF9F0)
      );
      return;
    }
    
    if (accountNumberController.text.isEmpty) {
      Get.snackbar(
        'Error',
        'Please enter account number',
         snackPosition: SnackPosition.TOP,
                  backgroundColor: const Color(0xFF8CF9F0)
      );
      return;
    }
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ConfirmCash(
          amount: widget.amount,
          fee: widget.fee,
          rate: widget.rate,
          symbol: widget.symbol,
          totalSell: widget.totalSell,
          nameAccount: nameController.text,
          accountNumber: accountNumberController.text,
          typePay: banks[selectedBank]['name'],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Select Payment Method'),
        backgroundColor: LikeWalletAppTheme.bule1,
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.all(16.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Select Bank',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 16.h),
                    // Bank selection grid
                    GridView.builder(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        childAspectRatio: 1,
                        crossAxisSpacing: 10.w,
                        mainAxisSpacing: 10.h,
                      ),
                      itemCount: banks.length,
                      itemBuilder: (context, index) {
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              selectedBank = index;
                            });
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: selectedBank == index
                                    ? LikeWalletAppTheme.bule1
                                    : Colors.grey.withOpacity(0.3),
                                width: 2,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.network(
                                  banks[index]['image'],
                                  height: 40.h,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Icon(Icons.account_balance, size: 40.h);
                                  },
                                ),
                                SizedBox(height: 8.h),
                                Text(
                                  banks[index]['name'],
                                  style: TextStyle(fontSize: 12.sp),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                    SizedBox(height: 24.h),
                    Text(
                      'Account Details',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 16.h),
                    // Account name field
                    TextField(
                      controller: nameController,
                      decoration: InputDecoration(
                        labelText: 'Account Name',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    SizedBox(height: 16.h),
                    // Account number field
                    TextField(
                      controller: accountNumberController,
                      decoration: InputDecoration(
                        labelText: 'Account Number',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    SizedBox(height: 32.h),
                    // Continue button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: proceedToConfirmation,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: LikeWalletAppTheme.bule1,
                          padding: EdgeInsets.symmetric(vertical: 16.h),
                        ),
                        child: Text(
                          'Continue',
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
