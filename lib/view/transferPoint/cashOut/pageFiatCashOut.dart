import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/controller/cashOutController/cashOutController.dart';
import 'package:likewallet/controller/cashOutController/fiatCashOutController.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/controller/transferController/transferController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/view/contactUs/contactUsPage.dart';
import 'package:likewallet/view/navigationBar/mainNavigator.dart';
import 'package:likewallet/view/transferPoint/cashOut/confirmCash.dart';
// import 'package:likewallet/view/transferPoint/cashOut/listBanking.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'dart:io';

class BankCashOut extends StatefulWidget {

  const BankCashOut({Key? key,}) : super(key: key);

  @override
  State<BankCashOut> createState() => _BankCashOutState();
}

class _BankCashOutState extends State<BankCashOut> {

  late BankCashOutController bankCashOutCtrl;
  late CashOutController cashOutCtrl;
  late TransferController transferCtrl;
  late ProfileController profileCtrl;

  final NumberFormat f = NumberFormat("#,##0.00", "en_US");
  final selectedCurrencyKey = Storage.get(StorageKeys.selectedCurrency) ?? 'THB';


  @override
  void initState() {
    super.initState();

    bankCashOutCtrl = !Get.isRegistered<BankCashOutController>() ? Get.put(BankCashOutController()) : Get.find<BankCashOutController>();
    cashOutCtrl = !Get.isRegistered<CashOutController>() ? Get.put(CashOutController()) : Get.find<CashOutController>();
    transferCtrl = !Get.isRegistered<TransferController>() ? Get.put(TransferController()) : Get.find<TransferController>();
    profileCtrl = !Get.isRegistered<ProfileController>() ? Get.put(ProfileController()) : Get.find<ProfileController>();

  }

  // UI-specific method to show overlay
  void showOverlay(BuildContext context) {
    if (bankCashOutCtrl.overlayEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    bankCashOutCtrl.overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: Container(
            width: double.infinity,
            color: Colors.white,
            child: Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: const EdgeInsets.only(top: 4.0, bottom: 4.0),
                child: CupertinoButton(
                  padding: EdgeInsets.only(right: 24.0, top: 8.0, bottom: 8.0),
                  onPressed: () async {
                    FocusScope.of(context).requestFocus(FocusNode());
                    String? result = bankCashOutCtrl.onSubmit(bankCashOutCtrl.numberPromptpay.text, context);
                    if (result == null) {
                      // Navigator.push(
                      //   context,
                      //   MaterialPageRoute(
                      //       builder: (context) => ConfirmCash(
                      //           amount: bankCashOutCtrl.amount,
                      //           totalSell: bankCashOutCtrl.totalSell,
                      //           fee: bankCashOutCtrl.fee,
                      //           rate: bankCashOutCtrl.rate,
                      //           nameAccount: '',
                      //           accountNumber: bankCashOutCtrl.numberPromptpay.text.replaceAll('-', ''),
                      //           typePay: 'promptpay',
                      //           symbol: bankCashOutCtrl.symbol)),
                      // );
                    } else {
                      // showColoredToast(result);
                    }
                  },
                  child: Text("Done",
                      style: TextStyle(
                          fontFamily: 'Proxima Nova',
                          color: Colors.grey,
                          fontWeight: FontWeight.bold)),
                ),
              ),
            ),
          ));
    });

    overlayState!.insert(bankCashOutCtrl.overlayEntry!);
  }

  @override
  void dispose() {
    super.dispose();
  }

  buildForPhone(Orientation orientation) {
    return Scaffold(
      backgroundColor: LikeWalletAppTheme.white,
      body: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
          bankCashOutCtrl.togglePromptpay(false);
          bankCashOutCtrl.toggleTruemoney(false);
          bankCashOutCtrl.toggleShow(false);
        },
        child: Stack(
          alignment: Alignment.topCenter,
          children: <Widget>[
            Container(
              height: MediaQuery.of(context).size.height,
              decoration: BoxDecoration(
                color: Color(0xffF5F5F5),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: [0.0, 0.2, 0.5],
                  colors: [
                    Colors.white,
                    Colors.white,
                    LikeWalletAppTheme.white1
                  ],
                ),
              ),
            ),
            
            
            SingleChildScrollView(
              child: Container(
                height: 0.9.sh,
                child: Stack(alignment: Alignment.topCenter, children: <Widget>[
                  //if (selectedCurrencyKey == 'THB' || selectedCurrencyKey == 'LAK' || selectedCurrencyKey == 'USD')
                  if (selectedCurrencyKey == 'thb' || selectedCurrencyKey == 'lak' || selectedCurrencyKey == 'usd')

                    banking(),
                  text()
                ]),
              ),
            ),
            Container(
              alignment: Alignment.bottomCenter,
              padding: EdgeInsets.only(bottom: 12.h),
              color: LikeWalletAppTheme.bule2,
              height: 80.h,
              width: MediaQuery.of(context).size.height,
              child: Text(
                'choose_title'.tr,
                style: TextStyle(
                    letterSpacing: 0.5,
                    fontFamily: 'Proxima Nova',
                    color: Color(0xffFFFFFF).withOpacity(1),
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500),
              ),
            ),
            Positioned(
              left: 0,
              top: 60.h,
              child: GestureDetector(
                onTap: () => {Navigator.of(context).pop()},
                child: Container(
                  decoration: BoxDecoration(
                    color: Color(0xffB4E60D),
                    borderRadius: BorderRadius.only(
                        bottomRight: Radius.circular(40.0),
                        topRight: Radius.circular(40.0)),
                  ),
                  height: 40.h,
                  width: 70.w,
                  child: Icon(
                    Icons.arrow_back_ios,
                    size: 16.sp,
                  ),
                ),
              ),
            ),
            _addAccountBanking(context)
          ],
        ),
      ),
    );
  }

  Widget banking() {
    return Positioned(
      top: 140.h,
      child: GestureDetector(
        onTap: () {
          bankCashOutCtrl.toggleShow(true);
        },
        child: Container(
            height: 100.h,
            width: 350.w,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.0),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                BoxShadow(
                  color: LikeWalletAppTheme.black.withOpacity(0.1),
                  offset: Offset(0, 0),
                  blurRadius: 8,
                ),
              ],
            ),
            child: Text(
              'choose_bank'.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontFamily: 'Proxima Nova',
                fontSize: 16.sp,
                color: LikeWalletAppTheme.black,
                letterSpacing: 0.139,
                fontWeight: FontWeight.w600,
              ),
            )),
      ),
    );
  }

  Widget promptPay() {
    return bankCashOutCtrl.promptpay.isTrue
        ? Positioned(
      top: 250.h,
      width: 300.w,
      child: GestureDetector(
          onTap: () {
            // setState(() {
            //   promptpay = false;
            // });
          },
          child: Column(
            children: <Widget>[
              Container(
                  height: 100.h,
                  width: 350.w,
                  alignment: Alignment.center,
                  child: Image.asset(
                    LikeWalletImage.icon_promptpay,
                    height: 60.h,
                    width: 80.w,
                  )),
              Container(
                padding: EdgeInsets.only(
                  top: 0.h,
                ),
                child: TextFormField(
                  controller: bankCashOutCtrl.numberPromptpay,
                  focusNode: bankCashOutCtrl.promptPayFocusNode,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(13),
                  ],
                  onFieldSubmitted: (value) {
                    // onSubmit(value);
                  },
                  style: TextStyle(
                    color: LikeWalletAppTheme.gray1,
                    fontSize: 20.sp,
                    fontFamily: 'Proxima Nova',
                  ),
                  decoration: InputDecoration(
                      hintText: 'choose_promptpay'.tr,
                      helperStyle: TextStyle(
                          color: LikeWalletAppTheme.gray,
                          fontFamily: 'Proxima Nova',
                          fontSize: 16.sp,
                          fontWeight: FontWeight.normal),
                      enabledBorder: UnderlineInputBorder(
                          borderSide: BorderSide(
                              color: LikeWalletAppTheme.gray
                                  .withOpacity(0.5))),
                      contentPadding: EdgeInsets.only(
                        top: 15.h,
                      ),
                      focusedBorder: UnderlineInputBorder(
                          borderSide: BorderSide(
                              color: LikeWalletAppTheme.gray
                                  .withOpacity(0.5)))),
                ),
              ),
            ],
          )),
    )
        : Positioned(
      top: 250.h,
      width: 350.w,
      child: GestureDetector(
        onTap: () async {
          // setState(() {
          //   promptpay = true;
          //   truemoney = false;
          //   show = false;
          // });
        },
        child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.0),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                BoxShadow(
                  color: LikeWalletAppTheme.black.withOpacity(0.1),
                  offset: Offset(0, 0),
                  blurRadius: 8,
                ),
              ],
            ),
            height: 100.h,
            width: 350.w,
            alignment: Alignment.center,
            child: Image.asset(
              LikeWalletImage.icon_promptpay,
              height: 60.h,
              width: 80.w,
            )),
      ),
    );
  }

  Widget text() {
    return Positioned(
      top: 280.h,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 0.75.sw,
            height: 1.sh,
            child: Text.rich(
              TextSpan(
                style: TextStyle(
                  fontFamily: 'Proxima Nova',
                  fontSize: 14.sp,
                  color: const Color(0xff707a8a),
                ),
                children: [
                  TextSpan(
                    text: 'cash_text1'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  TextSpan(
                    text: 'cash_text2'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextSpan(text: '\n'),
                  TextSpan(text: '\n'),
                  TextSpan(
                    text: 'cash_text3'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  TextSpan(
                    text: 'cash_text4'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextSpan(text: '\n'),
                  TextSpan(text: '\n'),
                  TextSpan(
                    text: 'cash_text5'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  TextSpan(
                    text: 'cash_text6'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextSpan(text: '\n'),
                  TextSpan(text: '\n'),
                  TextSpan(
                    text: 'cash_text7'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  TextSpan(
                    text: 'cash_text8'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextSpan(text: '\n'),
                  TextSpan(text: '\n'),
                  TextSpan(
                    text: 'cash_text9'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  TextSpan(
                    text: 'cash_text10'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextSpan(text: '\n'),
                  TextSpan(text: '\n'),
                  TextSpan(
                    text: 'cash_text11'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 14.sp,
                      color: const Color(0xffff0000),
                    ),
                  ),
                  TextSpan(
                    text: 'cash_text12'.tr,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: 16.h,
                      color: const Color(0xffff0000),
                    ),
                  ),
                ],
              ),
              textHeightBehavior:
              TextHeightBehavior(applyHeightToFirstAscent: false),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget trueMoney() {
    return bankCashOutCtrl.truemoney.isTrue
        ? Positioned(
      top: 400.h,
      width: 300.w,
      child: GestureDetector(
          onTap: () {
            // setState(() {
            //   truemoney = false;
            // });
          },
          child: Column(
            children: <Widget>[
              Container(
                  height: 100.h,
                  width: 350.w,
                  alignment: Alignment.center,
                  child: Image.asset(
                    LikeWalletImage.icon_truemoney,
                    height: 70.h,
                    width: 80.w,
                  )),
              Container(
                padding: EdgeInsets.only(
                  top: 0.h,
                ),
                child: TextFormField(
                  controller: bankCashOutCtrl.numberTruemoney,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(10),
                  ],
                  onFieldSubmitted: (value) {
                    String pattern = r'(^(?:[0]9)?[0-9]{10,}$)';
                    RegExp regex = RegExp(pattern);
                    if (value.length == 0) {
                      // showShortToast(
                      //     'กรุณากรอกข้อมูลให้ครบ', Colors.cyan);
                      // return 'Please enter mobile number';
                    } else if (!regex.hasMatch(value)) {
                      print('not match');
                      // showShortToast(
                      //     'กรุณาใส่เบอร์โทร ตัวอย่าง **********',
                      //     Colors.red);
                      // return 'Please enter valid mobile number';
                    } else if (regex.hasMatch(value)) {
                      // value.length < 10
                      //     ? showColoredToast(AppLocalizations.of(context)!
                      //     .translate('check_truemoney'))
                      //     : Navigator.push(
                      //   context,
                      //   MaterialPageRoute(
                      //       builder: (context) => ConfirmCash(
                      //           amount: amount,
                      //           totalSell: totalSell,
                      //           fee: fee,
                      //           rate: rate,
                      //           nameAccount: '',
                      //           accountNumber: numberTruemoney.text,
                      //           typePay: 'truemoney',
                      //           symbol: symbol)),
                      // );
                    }
                  },
                  style: TextStyle(
                      color: LikeWalletAppTheme.gray1,
                      fontSize: 20.sp,
                      fontFamily: 'Proxima Nova'
                  ),
                  decoration: InputDecoration(
                      hintText: 'choose_truemoney'.tr,
                      helperStyle: TextStyle(
                          color: LikeWalletAppTheme.gray,
                          fontFamily: 'Proxima Nova',
                          fontSize: 16.sp,
                          fontWeight: FontWeight.normal),
                      enabledBorder: UnderlineInputBorder(
                          borderSide: BorderSide(
                              color: LikeWalletAppTheme.gray
                                  .withOpacity(0.5))),
                      contentPadding: EdgeInsets.only(
                        top: 15.h,
                      ),
                      focusedBorder: UnderlineInputBorder(
                          borderSide: BorderSide(
                              color: LikeWalletAppTheme.gray
                                  .withOpacity(0.5)))),
                ),
              ),
            ],
          )),
    )
        : Positioned(
      top: 400.h,
      height: 100.h,
      width: 350.w,
      child: GestureDetector(
        onTap: () async {
          // setState(() {
          //   promptpay = false;
          //   truemoney = true;
          //   show = false;
          // });
        },
        child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.0),
              color: LikeWalletAppTheme.white,
              boxShadow: [
                BoxShadow(
                  color: LikeWalletAppTheme.black.withOpacity(0.1),
                  offset: Offset(0, 0),
                  blurRadius: 8,
                ),
              ],
            ),
            alignment: Alignment.center,
            child: Image.asset(
              LikeWalletImage.icon_truemoney,
              height: 60.h,
              width: 80.w,
            )),
      ),
    );
  }

  Widget _addAccountBanking(context) {
    return Obx(() => AnimatedPositioned(
        top: bankCashOutCtrl.show.value ? 0 : -800.h,
        duration: Duration(milliseconds: 300),
        child: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                      bottomRight: Radius.circular(37.0),
                      bottomLeft: Radius.circular(37.0)),
                  color: LikeWalletAppTheme.white,
                ),
                child: AnimatedContainer(
                  duration: Duration(milliseconds: 300),
                  alignment: Alignment.topCenter,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: [0.4, 0.6, 0.7, 0.8, 0.85],
                      colors: [
                        LikeWalletAppTheme.bule2.withOpacity(1),
                        LikeWalletAppTheme.bule2.withOpacity(0.94),
                        LikeWalletAppTheme.bule2.withOpacity(0.92),
                        LikeWalletAppTheme.bule2.withOpacity(0.9),
                        LikeWalletAppTheme.bule2.withOpacity(0.9),
                      ],
                    ),
                    borderRadius: BorderRadius.only(
                        bottomRight: Radius.circular(35.0),
                        bottomLeft: Radius.circular(35.0)),
                    boxShadow: [
                      BoxShadow(
                          color: Colors.black.withOpacity(0.16),
                          offset: Offset(0, 3),
                          blurRadius: 3.0,
                          spreadRadius: 0.0),
                    ],
                    color: LikeWalletAppTheme.white,
                  ),
                  child: SingleChildScrollView(
                      child: Column(
                        children: <Widget>[
                          _head(),
                          _listAccount(),
                          _buttonAddAccount(),
                        ],
                      )),
                )))));
  }

  Widget _head() {
    return Container(
        margin: EdgeInsets.only(
          top: 70.h,
          bottom: 50.h,
        ),
        child: Text(
          'choose_bank_account'.tr,
          style: TextStyle(
              fontFamily: 'Proxima Nova',
              color: Color(0xffFFFFFF).withOpacity(1),
              fontSize: 16.sp,
              fontWeight: FontWeight.w500),
        ));
  }

  Widget _listAccount() {
    return profileCtrl.user?.uid != null
        ? StreamBuilder(
      stream: bankCashOutCtrl.fireStore
          .collection('kycCashOut')
          .doc('bookBank')
          .collection(FirebaseAuth.instance.currentUser!.uid)
          .snapshots(),
      builder: (BuildContext context,
          AsyncSnapshot<QuerySnapshot<Map<String, dynamic>>> snapshot) {
        if (snapshot.hasError)
          return Text('Error: ${snapshot.error}');
        switch (snapshot.connectionState) {
          case ConnectionState.waiting:
            return Container();
          default:
            return Column(
              children: snapshot.data!.docs
                  .map((DocumentSnapshot<Map<String, dynamic>> document) {
                print('status is : ' + document['status']);
                return GestureDetector(
                    onTap: () {
                      // document['status'] == 'pending'
                      //     ? null
                      //     :
                      document['status'] == 'verify'
                          ? Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => ConfirmCash(
                                amount: cashOutCtrl.amount.value * cashOutCtrl.rateCurrency.value / cashOutCtrl.rate.value - cashOutCtrl.fee.value,
                                totalSell: cashOutCtrl.amount.value,
                                fee: cashOutCtrl.fee.value,
                                rate: cashOutCtrl.rate.value,
                                nameAccount: document['nameBookBank'],
                                accountNumber: document['numberBookBank'].toString(),
                                typePay: document['nameBank'].toString(),
                                symbol: cashOutCtrl.symbol.value)),
                      )
                          // : document['status'] == 'reject'
                      //     ? Navigator.push(
                      //   context,
                      //   MaterialPageRoute(
                      //       builder: (context) =>
                      //           ListBanking(
                      //               symbol: bankCashOutCtrl.symbol)),
                      // )
                          : null;
                    },
                    child: Container(
                        padding: EdgeInsets.only(
                          left: 30.w,
                          bottom: 25.h,
                        ),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Image.network(
                                document['imageBank'],
                                height: 45.h,
                              ),
                              Row(
                                crossAxisAlignment:
                                CrossAxisAlignment.start,
                                children: <Widget>[
                                  SizedBox(width: 10.w),
                                  Column(
                                    crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                    children: <Widget>[
                                      Text(
                                        document['nameBookBank'],
                                        style: TextStyle(
                                            fontFamily: 'Proxima Nova',
                                            color:
                                            LikeWalletAppTheme.gray1,
                                            fontSize: 14.sp,
                                            fontWeight: FontWeight.w100),
                                      ),
                                      Text(
                                        document['numberBookBank']
                                            .toString()
                                            .substring(0, 3) +
                                            "-" +
                                            document['numberBookBank']
                                                .toString()
                                                .substring(3, 4) +
                                            "-" +
                                            document['numberBookBank']
                                                .toString()
                                                .substring(4, 9) +
                                            "-" +
                                            document['numberBookBank']
                                                .toString()
                                                .substring(9, 10),
                                        style: TextStyle(
                                            fontFamily: 'Proxima Nova',
                                            color:
                                            LikeWalletAppTheme.gray1,
                                            fontSize: 14.sp,
                                            fontWeight: FontWeight.w100),
                                      ),
                                    ],
                                  ),
                                  Expanded(child: Container()),
                                  Container(
                                    alignment: Alignment.center,
                                    width: 100.w,
                                    height: 30.h,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.all(
                                        Radius.circular(100.0),
                                      ),
                                      color: LikeWalletAppTheme.gray
                                          .withOpacity(0.4),
                                    ),
                                    child: Text(
                                      document['status'] == 'pending'
                                          ? 'add_account_alert_status_pending'.tr
                                          : document['status'] == 'verify'
                                          ? 'add_account_alert_status_verify'.tr
                                          : document['status'] ==
                                          'reject'
                                          ? 'add_account_alert_status_reject'.tr
                                          : "",
                                      style: TextStyle(
                                          fontFamily: 'Proxima Nova',
                                          color: document['status'] ==
                                              'pending'
                                              ? Colors.yellow
                                              : document['status'] ==
                                              'verify'
                                              ? LikeWalletAppTheme
                                              .bule1_3
                                              : document['status'] ==
                                              'reject'
                                              ? LikeWalletAppTheme
                                              .red
                                              : LikeWalletAppTheme
                                              .bule1_3,
                                          fontSize: 12.sp,
                                          fontWeight: FontWeight.w100),
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: () async {
                                      print('hello minus');
                                      await RemoveDialog(
                                          context,
                                          document['numberBookBank']
                                              .toString());
                                    },
                                    child: Container(
                                      margin: EdgeInsets.only(
                                          right: 25.w,
                                          bottom: 0),
                                      child: Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.end,
                                        children: <Widget>[
                                          SizedBox(width: 12.w),
                                          Container(
                                              width: 30.h,
                                              height: 30.h,
                                              decoration:
                                              BoxDecoration(
                                                color: LikeWalletAppTheme
                                                    .gray
                                                    .withOpacity(0.4),
                                                shape: BoxShape.circle,
                                              ),
                                              child: Icon(
                                                Icons.close,
                                                color: Colors.white
                                                    .withOpacity(0.4),
                                                size: 15.h,
                                              ))
                                        ],
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ])));
              }).toList(),
            );
        }
      },
    )
        : Container();
  }

  RemoveDialog(context, documentID) {
    Dialog simpleDialog = Dialog(
      elevation: 500,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30.0),
      ),
      child: Container(
        height: 200.h,
        width: 350.w,
        color: Colors.transparent,
        margin: EdgeInsets.only(bottom: 200.h),
        child: ClipRect(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
            child: Container(
              decoration: BoxDecoration(
                color: LikeWalletAppTheme.white.withOpacity(0.6),
                borderRadius: BorderRadius.all(Radius.circular(20.0)),
              ),
              height: 200.h,
              width: 350.w,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Text(
                    'remove_bank'.tr,
                    style: TextStyle(
                      letterSpacing: 0.3,
                      fontFamily: 'Proxima Nova',
                      color: LikeWalletAppTheme.black.withOpacity(1),
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                        bottom: 25.h),
                    width: 300.w,
                    child: Text(
                      'remove_bank_detail'.tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        letterSpacing: 0.3,
                        fontFamily: 'Proxima Nova',
                        color: LikeWalletAppTheme.black.withOpacity(1),
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Container(
                      width: 300.w,
                      decoration: BoxDecoration(
                        border: Border(
                          top: BorderSide(
                            color: LikeWalletAppTheme.black.withOpacity(0.4),
                            width: 0.5.w,
                          ),
                        ),
                      ),
                      child: Row(
                        children: <Widget>[
                          GestureDetector(
                            onTap: () async {
                              bankCashOutCtrl.removeBankAccount(documentID).then((value) {
                                print('remove it');
                                Navigator.of(context).pop();
                              });
                            },
                            child: Container(
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                border: Border(
                                  right: BorderSide(
                                    //                   <--- left side
                                    color: LikeWalletAppTheme.black
                                        .withOpacity(0.4),
                                    width: 0.5.w,
                                  ),
                                ),
                              ),
                              height: 45.h,
                              width: 150.w,
                              child: Text(
                                'logout_yes'.tr,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  letterSpacing: 0.3,
                                  fontFamily: 'Proxima Nova',
                                  color:
                                  LikeWalletAppTheme.bule1_7.withOpacity(1),
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              Navigator.of(context).pop();
                            },
                            child: Container(
                              alignment: Alignment.center,
                              height: 45.h,
                              width: 150.w,
                              child: Text(
                                'logout_no'.tr,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  letterSpacing: 0.3,
                                  fontFamily: 'Proxima Nova',
                                  color:
                                  LikeWalletAppTheme.bule1_7.withOpacity(1),
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      )),
                ],
              ),
            ),
          ),
        ),
      ),
    );
    showDialog(
        context: context, builder: (BuildContext context) => simpleDialog);
  }

  Widget _buttonAddAccount() {
    return GestureDetector(
      onTap: () {
        // Navigator.push(
        //   context,
        //   MaterialPageRoute(builder: (context) => ListBanking(symbol: bankCashOutCtrl.symbol)),
        // );
      },
      child: Container(
          margin: EdgeInsets.only(
              right: 20.w,
              bottom: 20.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: <Widget>[
              Text(
                'add_bank_account'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: 'Proxima Nova',
                  fontSize: 14.sp,
                  color: LikeWalletAppTheme.white.withOpacity(0.7),
                  letterSpacing: 0.139,
                  fontWeight: FontWeight.w100,
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Image.asset(
                LikeWalletImage.plus,
                height: 35.h,
                width: 35.w,
              ),
            ],
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    final double shortestSide = MediaQuery.of(context).size.shortestSide;
    final bool useMobileLayout = shortestSide < 600.0;
    final Orientation orientation = MediaQuery.of(context).orientation;
    return Scaffold(
      bottomNavigationBar: Container(
        height: 0.14.sh,
        width: 1.sw,
        color: const Color(0xffFFFFFF),
        padding: EdgeInsets.symmetric(
            horizontal: 0.05.sw,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TextButton.icon(
              onPressed: () {
                // AppRoutes.makeFirst(
                //     context, HomeLikewallet(selectPage: 0));
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 0)));
              },
              icon: Icon(IconHome.path_43609,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
            TextButton.icon(
              onPressed: () {
                // AppRoutes.makeFirst(
                //     context, HomeLikewallet(selectPage: 1));
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 1)));

              },
              icon: Icon(IconHome.path_43608,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
            TextButton.icon(
              onPressed: () {
                // AppRoutes.makeFirst(
                //     context, HomeLikewallet(selectPage: 2));
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 2)));

              },
              icon: Icon(IconHome.group_24548,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
            TextButton.icon(
              onPressed: () {
                /// ย้ายไปไลน์ โอ๋เอ๋

                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            ContactUsPage()));
              },
              icon: Icon(IconHome.path_58781,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
          ],
        ),
      ),
      body: useMobileLayout
          ? buildForPhone(orientation)
          : buildForPhone(orientation),
    );
  }
}
