import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:qr_flutter/qr_flutter.dart';

import 'dart:ui' as ui;
import 'dart:typed_data';
import 'dart:io';

import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';


class ReceiveLIKEPage extends StatelessWidget {
  final String walletAddress = Storage.get(StorageKeys.addressETH);

  final GlobalKey QRKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 300.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 70.w),
          child: Text(
            walletAddress,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              fontSize: 14.sp,
              color: Colors.grey,
            ),
          ),
        ),
        SizedBox(height: 24.h),
        RepaintBoundary(
          key: QRKey,
          child: Container(
            padding: EdgeInsets.all(8.sp),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: QrImageView(
              data: walletAddress,
              size: 230.h,
              embeddedImage:
              AssetImage(LikeWalletImage.qr_likepoint),
              embeddedImageStyle: QrEmbeddedImageStyle(
                size: Size(
                  55.h,
                  55.h,
                ),
              ),
              errorCorrectionLevel: QrErrorCorrectLevel.Q,
              gapless: false,
              version: 9,
            ),
          ),
        ),
        SizedBox(height: 36.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            GestureDetector(
              onTap: () async {
                saveQRImage();
              },
                child: _iconAction('assets/image/Save_receipt.png', 'button_qr_save'.tr),
            ),
            GestureDetector(
              onTap: () async {
                await Clipboard.setData(ClipboardData(text: walletAddress));
                Get.snackbar(
                  'success'.tr,
                  'copiedAddress'.tr,
                  snackPosition: SnackPosition.TOP,
                  backgroundColor: const Color(0xFF8CF9F0)
                );
              },
                child: _iconAction('assets/image/receive/copy_address.png', 'bankingreceive_button_copy'.tr),
            ),
            
            GestureDetector(
              onTap: _shareQRImage,
                child: _iconAction('assets/image/Share_receipt.png', 'bankingreceive_button_address'.tr),
            ),
          ],
        ),
      ],
    );
  }

  Widget _iconAction(String assetPath, String label) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: Colors.white,
          child: Image.asset(
            assetPath,
            fit: BoxFit.contain,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontFamily: 'Proxima Nova',
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade700,
          ),
        ),
      ],
    );
  }

  Future<void> saveQRImage() async {
    // 1. Request storage permission.
    // var status = await Permission.storage.request();
    // if (!status.isGranted) {
    //   print("❌ Storage permission not granted.");
    //   Get.snackbar(
    //     'permissionDenied'.tr,
    //     'requestPermission'.tr,
    //     snackPosition: SnackPosition.BOTTOM,
    //     backgroundColor: Colors.orange,
    //     colorText: Colors.white,
    //   );
    //   return;
    // }

    try {
      // 2. Find the boundary and convert to image.
      final boundary = QRKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) {
        print("❌ QRKey not found or not rendered yet.");
        return;
      }

      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List pngBytes = byteData!.buffer.asUint8List();

      // 3. Save the image to the public gallery.
      final result = await ImageGallerySaver.saveImage(
        pngBytes,
        quality: 100,
        name: "qr_code_${DateTime.now().millisecondsSinceEpoch}",
      );

      if (result['isSuccess']) {
        print("✅ QR image saved successfully. Path: ${result['filePath']}");
        Get.snackbar(
          'success'.tr,
          'saveQRSuccess'.tr,
          snackPosition: SnackPosition.TOP,
                  backgroundColor: const Color(0xFF8CF9F0)
        );
      } else {
        throw Exception('Failed to save image to gallery.');
      }

    } catch (e) {
      print("❌ Failed to save QR image: $e");
      Get.snackbar(
        'failed'.tr,
        '${'saveQRFailed'.trParams({'error': e.toString()})}',
        snackPosition: SnackPosition.TOP,
                  backgroundColor: const Color(0xFF8CF9F0)
      );
    }
  }

  Future<void> _shareQRImage() async {
    try {
      // 1. Find the boundary and convert to image.
      final boundary = QRKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) {
        print("❌ QRKey not found or not rendered yet.");
        return;
      }

      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List pngBytes = byteData!.buffer.asUint8List();

      // 2. Save the image to a temporary file.
      final tempDir = await getTemporaryDirectory();
      final file = await File('${tempDir.path}/qr_code.png').create();
      await file.writeAsBytes(pngBytes);

      // 3. Use the share_plus plugin to share the file and text.
      final xFile = XFile(file.path);
      await Share.shareXFiles(
        [xFile],
        text: 'Wallet Address:\n$walletAddress',
        subject: 'Wallet QR Code',
      );

    } catch (e) {
      print("❌ Failed to share QR image: $e");
      Get.snackbar(
        'failed'.tr,
        'Failed to share QR code: $e',
        snackPosition: SnackPosition.TOP,
                  backgroundColor: const Color(0xFF8CF9F0)
      );
    }
  }
}
