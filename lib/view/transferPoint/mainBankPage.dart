import 'package:delayed_display/delayed_display.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/controller/transferController/transferController.dart';
import 'package:likewallet/controller/transferController/onContractController.dart';
import 'package:likewallet/controller/walletController/walletDataController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/loading.dart';
import 'package:likewallet/service/scrolling_text.dart';
import 'package:likewallet/view/transferPoint/BuyLIKEPage.dart';
import 'package:likewallet/view/transferPoint/ReceivePage.dart';
import 'package:likewallet/view/transferPoint/cashOutPage.dart';
import 'package:likewallet/view/transferPoint/transferPage.dart';
import 'package:likewallet/view/navigationBar/NavigationBar.dart' as nav;
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';

class MainBankPage extends StatefulWidget {
  const MainBankPage({Key? key}) : super(key: key);

  @override
  State<MainBankPage> createState() => _MainBankPageState();
}

class _MainBankPageState extends State<MainBankPage>
    with TickerProviderStateMixin {

  late final WalletDataController walletController;
  late final TransferController transferCtrl;
  late final ContactController contactCtrl;

  List<Widget> tabBar = [
    Container(
        alignment: Alignment.center,
        height: 75.h,
        width: 120.w,
        child: Text(
          'banking_send'.tr,
          maxLines: 1,
        )),
    Container(
        alignment: Alignment.center,
        height: 75.h,
        width: 120.w,
        child: Text(
          'banking_receive'.tr,
          maxLines: 1,
        )),
    Container(
        alignment: Alignment.center,
        height: 75.h,
        width: 120.w,
        child: Text(
          'banking_buy'.tr,
          maxLines: 1,
        )),
    Container(
        alignment: Alignment.center,
        height: 75.h,
        width: 120.w,
        child: Text(
          'banking_cash'.tr,
          maxLines: 1,
        ))
  ];

  List<Widget> tabBarBody = [
    DelayedDisplay(
      fadingDuration: const Duration(milliseconds: 1000),
      slidingBeginOffset: const Offset(-1.0, 0.0),
      child: TransferPage(),
    ),
    DelayedDisplay(
      slidingBeginOffset: const Offset(1.0, 0.0),
      fadingDuration: const Duration(milliseconds: 800),
      // child: Receive(),
      child: ReceiveLIKEPage(),
    ),
    DelayedDisplay(
      slidingBeginOffset: const Offset(1.0, 0.0),
      fadingDuration: const Duration(milliseconds: 800),
      child: BuyLikepointPage(),
    ),
    DelayedDisplay(
      slidingBeginOffset: const Offset(1.0, 0.0),
      fadingDuration: const Duration(milliseconds: 800),
      child: CashOutPage(),
    ),
  ];

  late TabController _tabController =
  TabController(vsync: this, length: tabBar.length);

  @override
  void initState() {
    super.initState();
    // Initialize controllers
    // transferCtrl.contact = contactCtrl;
    _tabController = TabController(vsync: this, length: tabBar.length);
    _tabController.addListener(_handleTabSelection);


    // ตรวจสอบและใช้ Get.find หากมีอยู่แล้ว
    if (Get.isRegistered<WalletDataController>()) {
      walletController = Get.find<WalletDataController>();
    } else {
      walletController = Get.put(WalletDataController());
    }

    if (Get.isRegistered<TransferController>()) {
      transferCtrl = Get.find<TransferController>();
    } else {
      transferCtrl = Get.put(TransferController());
    }

    if (Get.isRegistered<ContactController>()) {
      contactCtrl = Get.find<ContactController>();
    } else {
      contactCtrl = Get.put(ContactController());
    }
  }

  void _handleTabSelection() {
    if (!mounted) return;
    setState(() {});
  }

//เส้นเเนวตั้งเมนู
  Widget border() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.025,
      decoration: BoxDecoration(
          border: Border(
            right: BorderSide(
              color: Color(0xff789613),
              width: MediaQuery.of(context).size.width * 0.00185185185,
            ),
          )),
    );
  }

  buildForPhone(Orientation orientation) {
    return DefaultTabController(
      length: tabBar.length,
      child: Scaffold(
        // drawer: tabslide(),
        // key: _scaffoldKey,
          resizeToAvoidBottomInset: false,
          backgroundColor: LikeWalletAppTheme.white1,
          body: Stack(
            children: <Widget>[
              TabBarView(
                controller: _tabController,
                children: List<Widget>.generate(tabBarBody.length, (int index) {
                  return tabBarBody[index];
                }),
              ),
              // _appBar(),
              _newAppBar(),
//              _menu(),
              Positioned(
                top: (MediaQuery.of(context).size.height * 0.25) - (32.5.h),
                child: Container(
                  // padding: EdgeInsets.only(
                  //   top: mediaQuery(context, 'height', 9),
                  //   bottom: mediaQuery(context, 'height', 9),
                  //   left: mediaQuery(context, 'width', 36),
                  //   right: mediaQuery(context, 'width', 36),
                  // ),
                    alignment: Alignment.center,
                    width: MediaQuery.of(context).size.width * 1,
                    height: 60.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(84.0),
                      color: LikeWalletAppTheme.bule2_4,
                      boxShadow: [
                        BoxShadow(
                          color: LikeWalletAppTheme.black.withOpacity(0.35),
                          offset: Offset(0, -1),
                          spreadRadius: 2,
                          blurRadius: 5,
                        ),
                      ],
                    ),
                    child: TabBar(
                      controller: _tabController,
                      isScrollable: false,
                      indicatorColor: Colors.yellow,
                      indicatorSize: TabBarIndicatorSize.tab,
                      dividerColor: Colors.transparent,
                      labelStyle: TextStyle(
                        fontSize: 16.sp,
                        fontFamily: "Proxima Nova",
                        fontWeight: FontWeight.bold,
                      ),
                      //For Selected tab
                      unselectedLabelStyle: TextStyle(
                        fontSize: 16.sp,
                        fontFamily: "Proxima Nova",
                        fontWeight: FontWeight.normal,
                        color: LikeWalletAppTheme.white,
                      ),
                      labelColor: LikeWalletAppTheme.white,
                      indicator: BoxDecoration(
                          color: LikeWalletAppTheme.lemon,
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            stops: [0.0, 1],
                            colors: [
                              // Colors are easy thanks to Flutter's Colors class.
                              LikeWalletAppTheme.lemon.withOpacity(1),
                              LikeWalletAppTheme.lemon.withOpacity(0.7),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(30)),
                      tabs: List<Widget>.generate(tabBar.length, (int index) {
                        return tabBar[index];
                      }),
                    )),
              ),
            ],
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    final Orientation orientation = MediaQuery.of(context).orientation;
    return GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
            //   showfavorite = false;
          }
        },
        child: Obx(() => ModalProgressHUD(
            opacity: 0.1,
            inAsyncCall: transferCtrl.isLoading.value,
            progressIndicator: CustomLoading(),
            child: Scaffold(
              bottomNavigationBar: nav.NavigationBar(),
              body: GestureDetector(
                onTap: () {
                  FocusScopeNode currentFocus = FocusScope.of(context);
                  if (!currentFocus.hasPrimaryFocus) {
                    currentFocus.unfocus();
                  }
                },
                child: buildForPhone(orientation),
              ),
            ))));
  }

  Widget _newAppBar() {

    return Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height * 0.25,
        decoration: BoxDecoration(
          color: Color(0xFF2B2A38),
          // borderRadius: BorderRadius.only(
          //   bottomLeft: Radius.circular(30),
          //   bottomRight: Radius.circular(30),
          // ),
        ),
        alignment: Alignment.center,
        child: Column(
          children: [
            SizedBox(height: 27.h),
            GetBuilder<ProfileController>(builder: (profileCtrl) {
              return Padding(
                padding: EdgeInsets.only(top: 20),
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: <Widget>[
                      Padding(
                        padding: EdgeInsets.only(left: 35.w),
                        child: Image.asset(LikeWalletImage.icon_title_name,
                            height: 25.h),
                      ),
                      SizedBox(width: 10.w),
                      Text(
                        "${profileCtrl.resProfile["firstName"]} ${profileCtrl.resProfile["lastName"]}",
                        style: TextStyle(
                          fontFamily: 'Proxima Nova',
                          fontSize: 13.sp,
                          color: const Color(0x4dffffff),
                          // letterSpacing: 1.17.,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ]),
              );
            }),
            Container(
              width: MediaQuery.of(context).size.width * 0.88,
              margin: EdgeInsets.only(top: 25.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: MediaQuery.of(context).size.width * 0.50,
                    height: MediaQuery.of(context).size.height * 0.07,
                    // color: Colors.green.withOpacity(0.5),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Container(
                              width: MediaQuery.of(context).size.width * 0.385,
                              child: Text(
                                formatNumber(walletController.totalBalance.value),
                                style: TextStyle(
                                  fontFamily: 'Proxima Nova',
                                  fontSize: 20.sp,
                                  color: Colors.white,
                                  overflow: TextOverflow.ellipsis,
                                  // letterSpacing: 1.17,
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ),
                            SizedBox(width: 10.w),
                            Text(
                              'LIKE',
                              style: TextStyle(
                                fontFamily: 'Proxima Nova',
                                fontSize: 16.sp,
                                color: Colors.white,
                                // letterSpacing: 1.17,
                              ),
                              textAlign: TextAlign.left,
                            ),
                          ],
                        ),
                        Text(
                          '= ${formatNumber(walletController.totalBalanceTHB.value)} THB',
                          style: TextStyle(
                            fontFamily: 'Proxima Nova',
                            fontSize: 16.sp,
                            color: Colors.white.withOpacity(0.5),
                            // letterSpacing: 1.17,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ],
                    ),
                  ),
                  Container(
                    width: MediaQuery.of(context).size.width * 0.05,
                    height: MediaQuery.of(context).size.height * 0.06,
                    child: VerticalDivider(
                      color: Colors.white.withOpacity(0.5),
                      thickness: 1,
                      width: 1,
                      indent: 0,
                      endIndent: 0,
                    ),
                  ),
                  Container(
                    width: MediaQuery.of(context).size.width * 0.3,
                    height: MediaQuery.of(context).size.height * 0.065,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              width: 55.w, // ปรับความกว้างเพื่อให้เห็นการเลื่อน
                              height: 20.h,
                              child: ScrollingText(
                                text: _formatBTC(walletController.total_BTC.value),
                                textStyle: TextStyle(
                                  fontFamily: 'Proxima Nova',
                                  fontSize: 16,
                                  color: Colors.white.withOpacity(0.5),
                                ),
                              ),
                            ),
                            Text(
                              'BTC',
                              style: TextStyle(
                                fontFamily: 'Proxima Nova',
                                fontSize: 16.sp,
                                color: Colors.white.withOpacity(0.5),
                              ),
                              textAlign: TextAlign.left,
                            ),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              width: 55.w, // ปรับความกว้างเพื่อให้เห็นการเลื่อน
                              height: 20.h,
                              child: ScrollingText(
                                text: _formatBTC(walletController.total_GOLD.value),
                                textStyle: TextStyle(
                                  fontFamily: 'Proxima Nova',
                                  fontSize: 16,
                                  color: Colors.white.withOpacity(0.5),
                                ),
                              ),
                            ),
                            Text(
                              'G(gram)',
                              style: TextStyle(
                                fontFamily: 'Proxima Nova',
                                fontSize: 16.sp,
                                color: Colors.white.withOpacity(0.5),
                              ),
                              textAlign: TextAlign.left,
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ));
  }
  String _formatBTC(dynamic value) {
    if (value == null) return '0.00000000';
    try {
      final double parsed = double.tryParse(value.toString()) ?? 0.0;
      return parsed.toStringAsFixed(9); // BTC นิยมใช้ 8 ตำแหน่ง
    } catch (_) {
      return value.toString(); // fallback เผื่อ parse ไม่ได้
    }
  }

  String formatNumber(double number) {
    return NumberFormat('#,##0.00').format(number);
  }
}
