import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:likewallet/controller/buyLikepointController/buyLikepointController.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/view/contactUs/contactUsPage.dart';
import 'package:likewallet/view/navigationBar/mainNavigator.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';

class QRCodePromptpay extends StatefulWidget {
  const QRCodePromptpay({super.key});

  @override
  State<QRCodePromptpay> createState() => _QRCodePromptpayState();
}

class _QRCodePromptpayState extends State<QRCodePromptpay> {
  late final buyLikepointCtrl;
  late final profileCtrl;

  // For saving QR code
  GlobalKey _globalKey = new GlobalKey();
  RxBool isLoading = false.obs;

  @override
  void initState() {
    super.initState();
    if (Get.isRegistered<BuyLikepointController>()) {
      buyLikepointCtrl = Get.find<BuyLikepointController>();
    } else {
      buyLikepointCtrl = Get.put(BuyLikepointController());
    }
    if (Get.isRegistered<ProfileController>()) {
      profileCtrl = Get.find<ProfileController>();
    } else {
      profileCtrl = Get.put(ProfileController());
    }

    // Check if QR code is already expired
    checkQRExpiration();
  }

  // Check if QR code is expired
  void checkQRExpiration() {
    if (buyLikepointCtrl.timerOut.value <= 0 && buyLikepointCtrl.timerString.value == "00:00") {
      // Show message that QR is expired
      Get.snackbar(
        'warning'.tr,
        'qr_expired'.tr,
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        duration: Duration(seconds: 3),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: Container(
        height: 0.14.sh,
        width: 1.sw,
        color: const Color(0xffFFFFFF),
        padding: EdgeInsets.symmetric(
          horizontal: 0.05.sw,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TextButton.icon(
              onPressed: () {
                // AppRoutes.makeFirst(
                //     context, HomeLikewallet(selectPage: 0));
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 0)));
              },
              icon: Icon(IconHome.path_43609,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
            TextButton.icon(
              onPressed: () {
                // AppRoutes.makeFirst(
                //     context, HomeLikewallet(selectPage: 1));
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 1)));

              },
              icon: Icon(IconHome.path_43608,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
            TextButton.icon(
              onPressed: () {
                // AppRoutes.makeFirst(
                //     context, HomeLikewallet(selectPage: 2));
                Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen(selectPage: 2)));

              },
              icon: Icon(IconHome.group_24548,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
            TextButton.icon(
              onPressed: () {
                /// ย้ายไปไลน์ โอ๋เอ๋

                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            ContactUsPage()));
              },
              icon: Icon(IconHome.path_58781,
                  size: mediaQuery(context, 'height', 60),
                  color: Color(0xffB3B3B4)),
              label: Container(),
            ),
          ],
        ),
      ),
      body: SingleChildScrollView(
        child: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
              buyLikepointCtrl.hideSymbolDropdown();
            },
            child: Stack(
              alignment: Alignment.topLeft,
              clipBehavior: Clip.none,
              fit: StackFit.passthrough,
              children: [
                _body(),
                _head(context),
              ],
            )),
      ),
    );
  }

  Widget _head(context) {
    return Stack(
      children: [
        Container(
          color: const Color(0xff141322),
          height: 97.h,
          child: Container(
            padding: EdgeInsets.only(bottom: 12.5.h),
            alignment: Alignment.bottomCenter,
            width: 1.sw,
            child: Text(
              'buylike_promptpay4'.tr,
              style: TextStyle(
                  letterSpacing: 0.5,
                  fontFamily: 'Proxima Nova',
                  color: const Color(0xffFFFFFF).withOpacity(1),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.normal),
            ),
          ),
        ),
        Container(
          height: 1.sh,
        ),
        Positioned(
          top: 0.086.sh,
          child: GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
                height: 0.05.sh,
                width: 0.16.sw,
                decoration: BoxDecoration(
                  color: const Color(0xffB4E60D),
                  borderRadius: const BorderRadius.only(
                      bottomRight: Radius.circular(40.0),
                      topRight: Radius.circular(40.0)),
                  boxShadow: [
                    BoxShadow(
                      spreadRadius: 0,
                      blurRadius: 9,
                      color: const Color(0xff707071).withOpacity(0.1),
                      offset: const Offset(
                        0.0,
                        3.0,
                      ),
                    ),
                  ],
                ),
                alignment: Alignment.centerLeft,
                // margin: EdgeInsets.only(left: ),
                padding: EdgeInsets.symmetric(
                    vertical: mediaQuery(context, 'height', 32.2),
                    horizontal: mediaQuery(context, 'width', 71.3)),
                child: Image.asset(
                  height: 0.08.sh,
                  width: 0.17.sw,
                  LikeWalletImage.icon_back_button,
                )),
          ),
        ),
      ],
    );
  }

  // Save and share QR code
  Future<void> _saveQRCode() async {
    isLoading.value = true;

    try {
      // Request storage permission
      final status = await Permission.storage.request();
      if (!status.isGranted) {
        isLoading.value = false;
        Get.snackbar(
          'error'.tr,
          'permission_denied'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // Capture the widget as an image
      RenderRepaintBoundary boundary = _globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List pngBytes = byteData!.buffer.asUint8List();

      // Save to gallery
      final result = await ImageGallerySaver.saveImage(pngBytes);

      // Save to temporary file for sharing
      final tempDir = await getTemporaryDirectory();
      final file = await File('${tempDir.path}/qr_code.png').create();
      await file.writeAsBytes(pngBytes);

      // Share the file
      await Share.shareFiles(
        ['${tempDir.path}/qr_code.png'],
        subject: 'LIKE Wallet QR Code',
        text: 'My LIKE Wallet QR Code for payment',
      );

      // Show success message
      Get.snackbar(
        'success'.tr,
        'qr_saved'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      isLoading.value = false;
    } catch (e) {
      print('Error saving/sharing QR code: $e');
      Get.snackbar(
        'error'.tr,
        'save_failed'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      isLoading.value = false;
    }
  }

  Widget _body(){
    return Obx(() => Stack(
        children: [
          Container(
        height: 1.sh,
        width: 1.sw,
        color: Color(0xffF5F5F5),
        child: Column(
          children: [
            SizedBox(height: 0.17.sh),
            Container(
              width: 0.8.sw,
              child: Text(
                'buylike_promptpay1'.tr + ' ${buyLikepointCtrl.timerString.value} ' + 'buylike_promptpay1_2'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  letterSpacing: 0,
                  fontWeight: FontWeight.w500,
                  fontSize: mediaQuery(context, 'height', 42),
                  fontFamily: 'Proxima Nova',
                  color: Color(0xff3C3C43),
                ),
              ),
            ),
            SizedBox(height: 20.h),
            // QR Code with shadow
            GestureDetector(
              onTap: () {
                // Only refresh if expired
                if (buyLikepointCtrl.timerString.value == "00:00") {
                  // Show loading indicator
                  isLoading.value = true;

                  // Wait a moment to show loading indicator
                  Future.delayed(Duration(milliseconds: 300), () {
                    buyLikepointCtrl.generateQR();

                    // Hide loading after a short delay
                    Future.delayed(Duration(seconds: 1), () {
                      isLoading.value = false;
                    });
                  });
                }
              },
              child: Stack(
                alignment: Alignment.center,
                children: [
                // Shadow container
                Container(
                  height: 250.h,
                  width: 250.h,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.16),
                        offset: Offset(0, 35.h),
                        blurRadius: 75.h,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                ),
                // QR Code
                Container(
                  height: 240.h,
                  width: 240.h,
                  child: RepaintBoundary(
                    key: _globalKey,
                    child: QrImageView(
                      embeddedImage: AssetImage(LikeWalletImage.icon_buylike),
                      embeddedImageStyle: QrEmbeddedImageStyle(
                        size: Size(45.sp, 45.sp),
                      ),
                      data: buyLikepointCtrl.qrCode.value,
                      version: QrVersions.auto,
                      backgroundColor: LikeWalletAppTheme.white,
                      gapless: false,
                    ),
                  ),
                ),
                // Expired overlay
                if (buyLikepointCtrl.timerString.value == "00:00")
                  Container(
                    height: 240.h,
                    width: 240.h,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.refresh_rounded,
                          color: Colors.white,
                          size: 40.h,
                        ),
                        SizedBox(height: 10.h),
                        Text(
                          'qr_expired_tap'.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 40.h),
            // Buttons for refresh and save
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'buylike_promptpay2'.tr,
                  style: TextStyle(
                    fontWeight: FontWeight.normal,
                    fontSize: 14.sp,
                    fontFamily: 'Proxima Nova',
                    color: LikeWalletAppTheme.gray.withOpacity(0.3),
                  ),
                ),
                SizedBox(width: 10.w),
                // Refresh QR button
                GestureDetector(
                  onTap: () {
                    // Show loading indicator
                    isLoading.value = true;

                    // Wait a moment to show loading indicator
                    Future.delayed(Duration(milliseconds: 300), () {
                      buyLikepointCtrl.generateQR();

                      // Hide loading after a short delay
                      Future.delayed(Duration(seconds: 1), () {
                        isLoading.value = false;
                      });
                    });
                  },
                  child: Container(
                    alignment: Alignment.center,
                    width: 110.w,
                    height: 36.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20.0),
                      border: Border.all(
                        width: 1.w,
                        color: LikeWalletAppTheme.gray4.withOpacity(0.1),
                      ),
                    ),
                    child: Text(
                      'buylike_promptpay3'.tr,
                      style: TextStyle(
                        fontWeight: FontWeight.normal,
                        fontSize: 14.h,
                        fontFamily: 'Proxima Nova',
                        color: LikeWalletAppTheme.gray4.withOpacity(0.7),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 10.w),

              ],
            ),
            SizedBox(height: 25.h),
            _buttonSave(),

          ],
        ),
      ),
          // Loading overlay
          if (isLoading.value)
            Container(
              height: 1.sh,
              width: 1.sw,
              color: Colors.black.withOpacity(0.5),
              child: Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(LikeWalletAppTheme.bule1),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buttonSave() {
    return Container(
      width: 0.9.sw,
      height: 100.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50.w),
        color: LikeWalletAppTheme.white,
        boxShadow: [
          BoxShadow(
            color: LikeWalletAppTheme.gray.withOpacity(0.2),
            offset: Offset(0, 2),
            blurRadius: 5,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
              padding: EdgeInsets.only(
                left: 42.w,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            'buylike_promptpay4'.tr,
                            style: TextStyle(
                              letterSpacing: 0,
                              fontWeight: FontWeight.bold,
                              fontSize: mediaQuery(context, 'height', 42),
                              fontFamily: 'Proxima Nova',
                              color: LikeWalletAppTheme.gray.withOpacity(1),
                            ),
                          ),
                          SizedBox(
                            width: mediaQuery(context, 'width', 20),
                          ),
                          Text(
                            'buylike_promptpay5'.tr,
                            style: TextStyle(
                              letterSpacing: 0,
                              fontWeight: FontWeight.normal,
                              fontSize: mediaQuery(context, 'height', 42),
                              fontFamily: 'Proxima Nova',
                              color: LikeWalletAppTheme.gray.withOpacity(0.3),
                            ),
                          ),
                        ],
                      ),
                      Text(
                        'buylike_promptpay6'.tr,
                        style: TextStyle(
                          letterSpacing: 0,
                          fontWeight: FontWeight.normal,
                          fontSize: mediaQuery(context, 'height', 42),
                          fontFamily: 'Proxima Nova',
                          color: LikeWalletAppTheme.gray.withOpacity(0.3),
                        ),
                      ),
                    ],
                  ),

                ],
              )),
          GestureDetector(
            onTap: _saveQRCode,
            child: Container(
              margin: EdgeInsets.only(right: 25.w),
              alignment: Alignment.center,
              height: 60.h,
              width: 60.h,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: LikeWalletAppTheme.white,
                boxShadow: [
                  BoxShadow(
                    color: LikeWalletAppTheme.gray.withOpacity(0.1),
                    offset: Offset(1, 3),
                    spreadRadius: 0,
                    blurRadius: 3,
                  ),
                ],
              ),
              child: Image.asset(
                LikeWalletImage.icon_save_alt,
                height: 26.h,
              ),
            ),
          ),
        ],
      )
    );
  }
}
