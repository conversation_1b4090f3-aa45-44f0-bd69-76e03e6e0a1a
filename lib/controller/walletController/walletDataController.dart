import 'package:get/get.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/service/httpRequest.dart';


class WalletDataController extends GetxController {

  RxString address = "".obs;
  RxDouble totalBalance = 0.0.obs;
  RxDouble availableBalance = 0.0.obs;
  RxDouble lockedBalance = 0.0.obs;
  RxDouble totalBalanceTHB = 0.0.obs;
  RxDouble availableBalanceTHB = 0.0.obs;
  RxDouble lockedBalanceTHB = 0.0.obs;
  RxDouble total_BTC = 0.0.obs;
  RxDouble total_GOLD = 0.0.obs;

  Future<void> getDataWallet(phone) async {
    try {
      Map data = {
        "phone": phone,
      };

      print(data);
      print("https://likeapi-dev-611544412675.asia-south1.run.app/member/balance-likewallet");

      var response = await AppApi.post(
        "https://likeapi-dev-611544412675.asia-south1.run.app/member/balance-likewallet",
        data,
        "841JEGYD93NOCDXWKx6ra8NMDw00Pzvu"
      );

      print("response: $response");
      if (response['data'] == "not found phoneNumber in document") {
        print("found phoneNumber in document");
      } else {
        address.value = response['data']['address'];
        totalBalance.value = double.parse(response['data']['totalBalance'].toString());
        availableBalance.value = double.parse(response['data']['availableBalance'].toString());
        lockedBalance.value = double.parse(response['data']['lockedBalance'].toString());
        total_BTC.value = double.parse(response['data']['swapData']['total_BTC'].toString());
        total_GOLD.value = double.parse(response['data']['swapData']['total_GOLD'].toString());

        Storage.save(StorageKeys.addressETH, address.value);
        updateTotalBalanceTHB();
      }

      update();
    } catch (e) {
      print("Exception: $e");
    }
  }

  Future<String> getAddressByPhone(phone) async {
    try {
      var usePhone = phone;
      if(phone.startsWith("0")) {
        usePhone = phone.replaceFirst("0", "+66");
      }

      Map data = {
        "phone": usePhone,
      };

      print(data);

      var response = await AppApi.post(
        "${AppEnv.likeapi}/member/balance-likewallet",
          data,
        "841JEGYD93NOCDXWKx6ra8NMDw00Pzvu"
      );

      print(response);
      if (response['data'] == "not found phoneNumber in document") {
        print("found phoneNumber in document");
      } else {
        return response['data']['address'];
      }
      return "";
    } catch (e) {
      print("Exception: $e");
      return "";
    }
}

  double parseToDouble2(dynamic value) {
    return double.parse(double.parse(value.toString()).toStringAsFixed(2));
  }

  void updateTotalBalanceTHB() {
    print("updateTotalBalanceTHB");
    print(totalBalance.value);
    totalBalanceTHB.value = totalBalance.value / 100;
    availableBalanceTHB.value = availableBalance.value / 100;
    lockedBalanceTHB.value = lockedBalance.value / 100;

    update();
  }
}

/// ลข้างง่างนี้ไม่ใช้ แต่อย่าเพิ่งลบ

// class WalletDataController extends GetxController {
//   // final fireStore = FirebaseFirestore.instance;
//   final f = NumberFormat("#,##0.00", "en_US");
//
//   // ใช้ RxString สำหรับให้ UI listen และ react
//   var balanceTHB = ''.obs;
//   var fiat = 'THB-THB'.obs;
//
//   RxString locked_balance = 'Loading..'.obs;
//
//   RxDouble lockNew = 0.0.obs;
//   RxDouble dept = 0.0.obs;
//   RxDouble lpcu = 0.0.obs;
//   RxDouble lockAuto = 0.0.obs;
//   RxDouble lockAll = 0.0.obs;
//
//   RxDouble totalBalance = 0.00.obs;
//   RxDouble balanceLIKELock = 0.00.obs;
//
//   /// web3dart
//   final EthereumAddress contractAddrLike =
//   EthereumAddress.fromHex(env.contractLike);
//   final EthereumAddress contractLock =
//   EthereumAddress.fromHex(env.contractLock);
//   final EthereumAddress contractAirdrop =
//   EthereumAddress.fromHex(env.contractAirdrop);
//   final EthereumAddress contractSlot =
//   EthereumAddress.fromHex(env.contractSlotMachine);
//   final EthereumAddress contractMessage =
//   EthereumAddress.fromHex(env.contractMessage);
//   final EthereumAddress contractLoan =
//   EthereumAddress.fromHex(env.contractLoan);
//
//   final EthereumAddress c_erc20_addr =
//   EthereumAddress.fromHex(env.contractLike);
//   final EthereumAddress c_lock_addr =
//   EthereumAddress.fromHex(env.contractLotteryLock);
//   final EthereumAddress c_NFT_addr = EthereumAddress.fromHex(env.contractNFT);
//   final EthereumAddress lottery_addr = EthereumAddress.fromHex(env.lottery);
//
//   static int chainId = int.parse(env.chainId);
//   static String abiContractLock = env.abiContractLock;
//   static String abiContract = env.abiContractLike;
//   static String abiContractAirdrop = env.abiContractAirdrop;
//   static String abiContractSlot = env.abiContractSlot;
//   static int gasClaim = int.parse(env.gasClaim);
//   static String abiContractMessage = env.abiContractMessage;
//   static String abiContractLoan = env.abiContractLoan;
//
//   static String abi_contractLotteryLock = env.abi_contractLotteryLock;
//   static String abi_erc20 = env.abiContractLike;
//   static String abi_contractNFT = env.abi_contractNFT;
//   static String abi_lottery = env.abi_lottery;
//
//   String rpcUrl = env.rpcUrl;
//   String wsUrl = env.wsUrl;
//
//   Future reloadBalance() async {
//     try{
//       var address = await Storage.get(StorageKeys.addressETH);
//       print(address);
//
//       final balance = await getBalance(address: address);
//       final balanceLockAuto = await getBalanceLockAuto(address: address);
//       final balanceLock = await getBalanceLock(address: address);
//       totalAmount();
//
//       print('await');
//       await getLoanV2(address: address).then((loan) {
//         if (loan[11].toString() == "1") {
//           dept.value = double.parse(
//               EtherAmount.inWei(loan[6] as BigInt).getInEther.toString());
//           print("เงินต้น" +
//               EtherAmount.inWei(loan[6] as BigInt).getInEther.toString());
//         }
//       });
//       var response = await AppApi.post(
//           "https://" + env.apiUrl + "/getAutocompound",
//           {"address": address});
//
//       print(response);
//       if (response["statusCode"] == 200) {
//         print("lockNew:" + response["result"].toString());
//         lockNew.value = double.parse(response["result"].toString());
//       }
//
//       print("https://" + env.apiUrl + "/getLPCU");
//       var resultLPCU = await AppApi.post(
//           "https://" + env.apiUrl + "/getLPCU",
//           {"address": address});
//       if (resultLPCU["statusCode"] == 200) {
//         print("lpcu:" + resultLPCU["result"].toString());
//         lpcu.value = double.parse(resultLPCU["result"].toString());
//       }
//       print("balanceLock" + balanceLock.toString());
//
//       totalBalance.value = balance.toDouble() + balanceLock.toDouble();
//       balanceLIKELock.value = balanceLock.toDouble();
//       print("Loading..");
//
//       locked_balance.value = f.format(balanceLock + dept.value).toString();
//       lockAuto.value = balanceLockAuto.toDouble() - (balanceLock + dept.value);
//
//       print(locked_balance.value);
//       print(lockAuto.value);
//
//       update();
//       // if (!mounted) return;
//       // context.read(totalAmount).state = context.read(amount).state +
//       //     lockNew +
//       //     lockAuto +
//       //     lpcu +
//       //     double.parse(locked_balance.replaceAll(',', ''));
//
//       totalBalance.value = balance.toDouble() +
//           lockNew.toDouble() +
//           lockAuto.toDouble() +
//           lpcu.toDouble() +
//           double.tryParse(locked_balance.replaceAll(',', ''))!;
//
//       // lockAll = lockNew +
//       //     lockAuto +
//       //     lpcu +
//       //     double.parse(locked_balance.replaceAll(',', ''));
//
//       // streamTotalBalance.sink
//       //     .add(f.format(context.read(totalAmount).state));
//       // streamBalanceLock.sink.add(balanceLIKELock);
//       // streamBalanceLocked.sink.add(f.format(lockAll));
//       // streamController.sink.add(context.read(amount).state);
//       //
//       // if (numberReload == 0) {
//       //   print(numberReload);
//       //   streamCurrency(context.read(totalAmount).state.toDouble());
//       // } else if (numberReload == 1) {
//       //   print(numberReload);
//       //   streamCurrency(lockAll);
//       // } else if (numberReload == 2) {
//       //   print(numberReload);
//       //   streamCurrency(balance.toDouble());
//       // }
//
//     }catch (e) {
//       print("reloadBalance error: $e");
//     }
//   }
//
//   /// ต้องใช้นะอันนี้ แต่ยังไม่ต้อง
//   // Future<void> streamCurrency(double balance) async {
//   //   print("balance: $balance");
//   //
//   //   final targetFiat = fiat.value == 'none' ? "THB" : fiat.value;
//   //   final path = "THB-$targetFiat";
//   //
//   //   try {
//   //     final ds = await fireStore.collection('exchangeFiat').doc(path).get();
//   //     final data = ds.data();
//   //     if (data == null) return;
//   //
//   //     print("rate: ${data["rate"]}");
//   //     print("path: $path");
//   //
//   //     double result;
//   //
//   //     if (fiat.value == 'THB') {
//   //       result = (balance / 100).floorToDouble();
//   //     } else if (fiat.value == 'LIKE') {
//   //       result = (balance).floorToDouble();
//   //     } else if (fiat.value == 'GOLD') {
//   //       result = (balance / 100 / data["rate"]).floorToDouble();
//   //     } else {
//   //       result = (balance / 100 * data["rate"]).floorToDouble();
//   //     }
//   //
//   //     balanceTHB.value = f.format(result);
//   //   } catch (e) {
//   //     print("streamCurrency error: $e");
//   //   }
//   // }
//
//   Future<num> totalAmount() async {
//     final client = Web3Client(rpcUrl, Client(), socketConnector: () {
//       return IOWebSocketChannel.connect(wsUrl).cast<String>();
//     });
//
//     final c_NFT = DeployedContract(
//         ContractAbi.fromJson(abi_contractNFT.toString(), 'LotteryNFT'),
//         c_NFT_addr);
//     final c_Lock = DeployedContract(
//         ContractAbi.fromJson(
//             abi_contractLotteryLock.toString(), 'LockupLikepoint'),
//         c_lock_addr);
//     final c_erc20 = DeployedContract(
//         ContractAbi.fromJson(abi_erc20.toString(), 'LIKEPOINT'), c_erc20_addr);
//     final c_lottery = DeployedContract(
//         ContractAbi.fromJson(abi_lottery.toString(), 'LotteryLikepoint'),
//         lottery_addr);
//
//     final functionCall = c_lottery.function('totalAmount');
//     final balanceLikepoint = await client
//         .call(contract: c_lottery, function: functionCall, params: []);
//     var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
//     return newBalance.getValueInUnit(EtherUnit.ether);
//   }
//
//   Future<num> getNativeBalance({required String address}) async {
//     final client = Web3Client(rpcUrl, Client(), socketConnector: () {
//       return IOWebSocketChannel.connect(wsUrl).cast<String>();
//     });
//     final balance =
//     await client.getBalance(EthereumAddress.fromHex(address));
//
//     print('We have ${balance.getInEther} Native');
//
//     return balance.getValueInUnit(EtherUnit.ether);
//   }
//
//   Future<num> getBalance({required String address}) async {
//     final contract = DeployedContract(
//         ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
//         contractAddrLike);
//
//     final balanceOf = contract.function('balanceOf');
//     final client = Web3Client(rpcUrl, Client(), socketConnector: () {
//       return IOWebSocketChannel.connect(wsUrl).cast<String>();
//     });
//     final balanceLikepoint = await client.call(
//         contract: contract,
//         function: balanceOf,
//         params: [EthereumAddress.fromHex(address)]);
//     var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
//
//     print('We have ${newBalance.getInEther} LIKE');
//
//     return newBalance.getValueInUnit(EtherUnit.ether);
//   }
//
//   Future<num> getBalanceLock({required String address}) async {
//     final contract = DeployedContract(
//         ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
//         contractLock);
//
//     final balanceOf = contract.function('getLock');
//     final client = Web3Client(rpcUrl, Client(), socketConnector: () {
//       return IOWebSocketChannel.connect(wsUrl).cast<String>();
//     });
//     try {
//       final balanceLikepoint = await client.call(
//           contract: contract,
//           function: balanceOf,
//           params: [contractAddrLike, EthereumAddress.fromHex(address)]);
//
//       var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
//
//       print('We have lock ${newBalance.getInEther} LIKE');
//
//       return newBalance.getValueInUnit(EtherUnit.ether);
//     } catch (e) {
//       print(e);
//       return 0;
//     }
//   }
//
//   Future<num> getBalanceLockAuto({required String address}) async {
//     final contract = DeployedContract(
//         ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
//         contractLock);
//
//     final balanceOf = contract.function('getAmount');
//     final client = Web3Client(rpcUrl, Client(), socketConnector: () {
//       return IOWebSocketChannel.connect(wsUrl).cast<String>();
//     });
//     try {
//       final balanceLikepoint = await client.call(
//           contract: contract,
//           function: balanceOf,
//           params: [contractAddrLike, EthereumAddress.fromHex(address)]);
//
//       var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);
//
//       print('We have lock ${newBalance.getInEther} LIKE');
//
//       return newBalance.getValueInUnit(EtherUnit.ether);
//     } catch (e) {
//       print(e);
//       return 0;
//     }
//   }
//
//   Future<List<dynamic>> getLoanV2({required String address}) async {
//     print(address);
//     print(address.runtimeType);
//
//     var whatKindIsIt = EthereumAddress.fromHex(address);
//
//     print(whatKindIsIt);
//     print(whatKindIsIt.runtimeType);
//
//     final client = Web3Client(rpcUrl, Client(), socketConnector: () {
//       return IOWebSocketChannel.connect(wsUrl).cast<String>();
//     });
//
//     //เรียก contract Loan
//     final loan = DeployedContract(
//         ContractAbi.fromJson(abiContractLoan.toString(), 'Loan'), contractLoan);
//     //เรียกฟังก์ชั่น maxumumBorrow
//     final loanContract = loan.function('contractLoans');
//     //สั่งเรียก maximumBorrow และได้ผลลัพธ์กลับมา
//     final callLoanContract = await client.call(
//         contract: loan,
//         function: loanContract,
//         params: [EthereumAddress.fromHex(address)]);
//
//     print(callLoanContract);
//
//     //callback //array 10 ตำแหน่ง ["0address คนยืม", "1.วลายืม", "2.อัตราดอกเบี้ย", "3.เวลาสิ้นสุดสัญญา", "4หนี้คงเหลือ", "5. ดอกเบี้ยที่จ่ายแล้ว", "6. จำนวนเงินต้น", "7. หลักประกัน", "8. จ่ายเงินต้นมาแล้วทั้งหมด", "9. จ่ายดอกเบี้ยมาแล้วทั้งหมด", "10. สถานะการกู้ยืม 1 ยังกู้อยู่ 0 หมดสัญญาแล้ว"]
//     return callLoanContract;
//   }
//
//   @override
//   Future<String> sendTransaction({required String pk, required String to, required String value}) async {
//     // TODO: implement signTransaction
//     try {
//       final EthereumAddress receiver = EthereumAddress.fromHex(to);
//       // final amount = EtherAmount.fromUnitAndValue(EtherUnit.ether, value.substring(0, valueSend).toString());
//       EtherAmount amount = EtherAmount.fromUnitAndValue(
//           EtherUnit.ether, int.parse(value.split(".")[0]).toString());
//       final client = Web3Client(rpcUrl, Client(), socketConnector: () {
//         return IOWebSocketChannel.connect(wsUrl).cast<String>();
//       });
//       final credentials = await client.credentialsFromPrivateKey(pk);
//       final ownAddress = await credentials.extractAddress();
//       int nonce = await client.getTransactionCount(ownAddress);
//       final contract = DeployedContract(
//           ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
//           contractAddrLike);
//
//       final transfer = contract.function('transfer');
//
//       final transaction = await client.sendTransaction(
//         credentials,
//         Transaction.callContract(
//           from: ownAddress,
//           contract: contract,
//           function: transfer,
//           nonce: nonce,
//           maxGas: gasClaim,
//           gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
//           parameters: [receiver, amount.getInWei],
//         ),
//         chainId: chainId,
//       );
//       return transaction;
//     } catch (e) {
//       print(e);
//       return '0';
//     }
//   }
//
//   @override
//   Future<String> lockLikePoint({required String pk, required String value}) async {
//     // TODO: implement signTransaction
//     EtherAmount amount = EtherAmount.fromUnitAndValue(
//         EtherUnit.ether, int.parse(value.split(".")[0]));
//     final client = Web3Client(rpcUrl, Client(), socketConnector: () {
//       return IOWebSocketChannel.connect(wsUrl).cast<String>();
//     });
//     final credentials = await client.credentialsFromPrivateKey(pk);
//     final ownAddress = await credentials.extractAddress();
//     print(ownAddress);
//     final contract = DeployedContract(
//         ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
//         contractLock);
//     print(amount.getInWei);
//     final transfer = contract.function('depositToken');
//     int nonce = await client.getTransactionCount(ownAddress);
//     final contractApprove = DeployedContract(
//         ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
//         contractAddrLike);
//     print(amount.getInWei);
//
//     //ตรวจสอบการ allowance ก่อนว่า มีการ allowance  ค้างไว้ไหม
//     final allower = contractApprove.function('allowance');
//     final balanceApprove = await client
//         .call(contract: contractApprove, function: allower, params: [
//       EthereumAddress.fromHex(ownAddress.toString()),
//       EthereumAddress.fromHex(contractLock.toString())
//     ]);
//     var approved = EtherAmount.inWei(balanceApprove.first as BigInt);
//     print('get in wei : ' + approved.getInWei.toString());
//     print('get in wei 2: ' +
//         EtherAmount.fromUnitAndValue(EtherUnit.wei, '1000000000000000000')
//             .getInWei
//             .toString());
//
//     if (approved.getInWei ==
//         EtherAmount.fromUnitAndValue(EtherUnit.wei, '0').getInWei) {
//       final transferApprove = contractApprove.function('approve');
//
//       final transactionApprove = await client.sendTransaction(
//         credentials,
//         Transaction.callContract(
//             from: ownAddress,
//             contract: contractApprove,
//             function: transferApprove,
//             maxGas: gasClaim,
//             nonce: nonce,
//             gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
//             parameters: [
//               contractLock,
//               EtherAmount.inWei(BigInt.parse(
//                   '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
//                   .getInWei
//             ]
// //            parameters: [contractLock, amount.getInWei],
//         ),
//         chainId: chainId,
//       );
//
//       final transactionLock = await client.sendTransaction(
//         credentials,
//         Transaction.callContract(
//           from: ownAddress,
//           contract: contract,
//           function: transfer,
//           maxGas: gasClaim,
//           nonce: nonce + 1,
//           gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
//           parameters: [contractAddrLike, amount.getInWei, BigInt.from(10)],
//         ),
//         chainId: chainId,
//       );
// //  String transactionApprove='';
//       return transactionApprove + ":" + transactionLock;
//     } else {
//       if (amount.getInWei > approved.getInWei) {
//         BigInt secondAmount = amount.getInWei - approved.getInWei;
//         print(secondAmount);
//         amount = approved;
//         final transactionLock = await client.sendTransaction(
//           credentials,
//           Transaction.callContract(
//             from: ownAddress,
//             contract: contract,
//             function: transfer,
//             maxGas: gasClaim,
//             nonce: nonce,
//             gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
//             parameters: [contractAddrLike, amount.getInWei, BigInt.from(10)],
//           ),
//           chainId: chainId,
//         );
//
//         final transferApprove = contractApprove.function('approve');
//
//         final transactionApprove = await client.sendTransaction(
//           credentials,
//           Transaction.callContract(
//             from: ownAddress,
//             contract: contractApprove,
//             function: transferApprove,
//             maxGas: gasClaim,
//             nonce: nonce + 1,
//             gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
//             parameters: [
//               contractMessage,
//               EtherAmount.fromUnitAndValue(
//                   EtherUnit.wei, secondAmount.toString())
//                   .getInWei
//             ],
//           ),
//           chainId: chainId,
//         );
//
//         final transactionLock2 = await client.sendTransaction(
//           credentials,
//           Transaction.callContract(
//             from: ownAddress,
//             contract: contract,
//             function: transfer,
//             maxGas: gasClaim,
//             nonce: nonce + 2,
//             gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
//             parameters: [
//               contractAddrLike,
//               EtherAmount.fromUnitAndValue(
//                   EtherUnit.wei, secondAmount.toString())
//                   .getInWei,
//               BigInt.from(10)
//             ],
//           ),
//           chainId: chainId,
//         );
//         return transactionApprove + ":" + transactionLock2;
//       } else {
//         //ลองลบ approve
//         final transactionLock = await client.sendTransaction(
//           credentials,
//           Transaction.callContract(
//             from: ownAddress,
//             contract: contract,
//             function: transfer,
//             maxGas: gasClaim,
//             nonce: nonce,
//             gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
//             parameters: [contractAddrLike, amount.getInWei, BigInt.from(10)],
//           ),
//           chainId: chainId,
//         );
//         return 'no' + ":" + transactionLock;
//       }
//     }
//   }
//
//   @override
//   Future<String> requestUnlock({required String pk}) async {
//     final client = Web3Client(rpcUrl, Client(), socketConnector: () {
//       return IOWebSocketChannel.connect(wsUrl).cast<String>();
//     });
//     final credentials = await client.credentialsFromPrivateKey(pk);
//     final ownAddress = await credentials.extractAddress();
//     print(ownAddress);
//     final contract = DeployedContract(
//         ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
//         contractLock);
//     final transfer = contract.function('requestWithdraw');
//     final transaction = await client.sendTransaction(
//       credentials,
//       Transaction.callContract(
//         from: ownAddress,
//         contract: contract,
//         function: transfer,
//         maxGas: gasClaim,
//         gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
//         parameters: [contractAddrLike],
//       ),
//       chainId: chainId,
//     );
//     return transaction;
//   }
//
//   @override
//   Future<String> unlockLikePoint({required String pk, required String value}) async {
//     final amount = EtherAmount.fromUnitAndValue(
//         EtherUnit.ether, int.parse(value.split(".")[0]));
//     print("amount: $amount");
//     final client = Web3Client(rpcUrl, Client(), socketConnector: () {
//       return IOWebSocketChannel.connect(wsUrl).cast<String>();
//     });
//     final credentials = await client.credentialsFromPrivateKey(pk);
//     final ownAddress = await credentials.extractAddress();
//     print(ownAddress);
//     final contract = DeployedContract(
//         ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
//         contractLock);
//     print(amount.getInWei);
//     final transfer = contract.function('withdrawToken');
//     final transaction = await client.sendTransaction(
//       credentials,
//       Transaction.callContract(
//         from: ownAddress,
//         contract: contract,
//         function: transfer,
//         maxGas: gasClaim,
//         gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
//         parameters: [contractAddrLike, amount.getInWei],
//       ),
//       chainId: chainId,
//     );
//     return transaction;
//   }
// }
