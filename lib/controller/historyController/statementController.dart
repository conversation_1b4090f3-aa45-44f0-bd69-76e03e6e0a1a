import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/view/statement/statementWebView.dart';

class StatementController extends GetxController {
  var selectedRadio = 'All'.obs;
  var selectedDate = Rxn<DateTime>();

  // Additional variables for statement functionality
  var day = ''.obs;
  var sendDate = ''.obs;
  var launched = false.obs;
  var type = 'All'.obs;
  var address = ''.obs;

  // Date formatter
  final DateFormat f = DateFormat('yyyy-MM-dd');

  @override
  void onInit() {
    super.onInit();
    initializeData();
  }

  void initializeData() {
    // Initialize with current date
    selectedDate.value = DateTime.now();

    // Get address from storage
    address.value = Storage.get(StorageKeys.addressETH) ?? '';

    // Set type based on selected radio
    type.value = selectedRadio.value;
  }

  String get formattedDate {
    if (selectedDate.value == null) return '';
    return DateFormat('d MMM yyyy').format(selectedDate.value!);
  }

  void pickDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      selectedDate.value = picked;
    }
  }

  // Handle period selection and generate statement URL
  Future<void> handlePeriodSelection(String periodValue) async {
    day.value = periodValue;

    if (selectedDate.value == null) {
      selectedDate.value = DateTime.now();
    }

    DateTime targetDate;

    switch (day.value) {
      case '30': // 1 month ago
        targetDate = DateTime(
          selectedDate.value!.year,
          selectedDate.value!.month - 1,
          selectedDate.value!.day,
        );
        sendDate.value = f.format(targetDate);
        break;

      case '3': // 3 months ago
        targetDate = DateTime(
          selectedDate.value!.year,
          selectedDate.value!.month - 3,
          selectedDate.value!.day,
        );
        sendDate.value = f.format(targetDate);
        break;

      case '6': // 6 months ago
        targetDate = DateTime(
          selectedDate.value!.year,
          selectedDate.value!.month - 6,
          selectedDate.value!.day,
        );
        sendDate.value = f.format(targetDate);
        break;

      case '1': // 1 year ago
        targetDate = DateTime(
          selectedDate.value!.year - 1,
          selectedDate.value!.month,
          selectedDate.value!.day,
        );
        sendDate.value = f.format(targetDate);
        break;

      default:
        sendDate.value = '';
    }

    print('Send Date: ${sendDate.value}');

    if (sendDate.value.isNotEmpty) {
      await launchStatementUrl();
    }
  }

  // Launch statement URL in WebView
  Future<void> launchStatementUrl() async {
    String url;

    if (type.value == 'Lock history') {
      url = 'https://statement.likewallet.io/generateStatementLikeWallet.php?address=${address.value}&start=${sendDate.value}&type=lock';
    } else {
      url = 'https://statement.likewallet.io/generateStatementLikeWallet.php?address=${address.value}&start=${sendDate.value}';
    }

    print('Opening statement URL in WebView: $url');

    try {
      // Navigate to custom Statement WebView page
      String title = type.value == 'Lock history' ? 'Lock History Statement' : 'Transaction Statement';
      Get.to(() => StatementWebView(url: url, title: title));
      launched.value = true;

      // Show success message
      Get.snackbar(
        'Statement',
        'Opening statement...',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: Duration(seconds: 1),
      );

    } catch (e) {
      launched.value = false;
      print('Error opening WebView: $e');

      Get.snackbar(
        'Error',
        'Failed to open statement: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Launch URL in WebView (alternative method for compatibility)
  Future<bool> launchInBrowser(String url) async {
    try {
      print('Opening URL in WebView: $url');
      String title = type.value == 'Lock history' ? 'Lock History Statement' : 'Transaction Statement';
      Get.to(() => StatementWebView(url: url, title: title));
      return true;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to open WebView: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Update selected radio and type
  void updateSelectedRadio(String value) {
    selectedRadio.value = value;
    type.value = value;
  }
}