import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:http/http.dart' as http;
import 'package:likewallet/controller/otherController/configurationController.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/model/pageMaintenanceModel/pageMaintenanceModel.dart';
import 'package:likewallet/model/ppp7/whiteListPPP7Model.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'dart:convert';

import 'package:likewallet/service/httpRequest.dart';

// class CollectionReference {
//   final String path;
//   CollectionReference(this.path);
//   DocumentReference doc(String docName) => DocumentReference('$path/$docName');
//   CollectionReference collection(String subPath) => CollectionReference('$path/$subPath');
//   Future<List<QueryDocumentSnapshot>> get() async => [];
// }
//
// class DocumentReference {
//   final String path;
//   DocumentReference(this.path);
//   CollectionReference collection(String subPath) => CollectionReference('$path/$subPath');
//   Future<DocumentSnapshot> get() async => DocumentSnapshot();
// }
//
// class DocumentSnapshot {
//   Map<String, dynamic> data() => {'status': 'active', 'address': 'mock_address'};
// }
//
// class QueryDocumentSnapshot {
//   String id;
//   Map<String, dynamic> data;
//   QueryDocumentSnapshot({required this.id, required this.data});
// }
//


// // Mock CloseMaintenance widget
class CloseMaintenance extends StatelessWidget {
  final String docName;

  const CloseMaintenance({super.key, required this.docName});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Text(
          'Maintenance: $docName',
          style: TextStyle(
            fontFamily: 'ProximaNova',
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}

// enum StorageKeys { tokenLine }

class CheckAboutController extends GetxController {
  // Dependencies
  final _firestore = FirebaseFirestore.instance;
  // late final configETH = ConfigurationService();

  // State
  var role = 'normal'.obs;
  var permissionMenu = false.obs;
  var statusUpdateVersion = false;
  var detailMaintenance = '';


  var blacklist;
  var blacklistLDX;
  var blacklistFIN;

  Future<void> initializeCheckBlackList() async {
    // Initialize any necessary data or state here
    final profileCtrl = Get.find<ProfileController>();

    if(profileCtrl.user != null) {
      blacklist = await checkBlackList(type: 'blackList', phone: profileCtrl.user!.phoneNumber);
      blacklistLDX = await checkBlackList(type: 'blackList_LDX', phone: profileCtrl.user!.phoneNumber);
      blacklistFIN = await checkBlackList(type: 'blackList_FIN', phone: profileCtrl.user!.phoneNumber);
    }
  }

  Future<bool> firstStep({String? docNameMain, String? docNameCon, String? contract}) async {
    try {
      // Check maintenance status
      final isActive = await checkMaintenance(docName: docNameMain);
      if (!isActive) {
        // Navigate to maintenance page
        final route = docNameMain == 'borrow'
            ? MaterialPageRoute(builder: (_) => CloseMaintenance(docName: docNameMain!))
            : MaterialPageRoute(builder: (_) => CloseMaintenance(docName: docNameMain!));
        // docNameMain == 'borrow'
        //     ? Navigator.push(context, route)
        //     : Navigator.pushReplacement(context, route);
        return false;
      }

      // Check contract if provided
      if (docNameCon == null || docNameCon.isEmpty) {
        return true;
      }

      final isContractUpdated = await contractUpdate(
        docName: docNameCon,
        contract: contract,

      );

      if (!isContractUpdated) {
        // Show dialog for outdated contract
        // showDialog(
        //   context: context,
        //   barrierColor: Colors.transparent,
        //   builder: (context) => WillPopScope(
        //     onWillPop: () async => true,
        //     child: Scaffold(
        //       backgroundColor: Colors.transparent,
        //       body: Container(
        //         color: Colors.black54,
        //         child: Center(
        //           child: Text(
        //             'Contract is outdated',
        //             style: TextStyle(
        //               fontFamily: 'ProximaNova',
        //               fontSize: 16.sp,
        //               color: Colors.white,
        //             ),
        //           ),
        //         ),
        //       ),
        //     ),
        //   ),
        // );
        return false;
      }

      return true;
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> contractUpdate({String? docName, String? contract, BuildContext? context,}) async {
    try {
      final doc = await _firestore
          .collection('smartcontract')
          .doc(docName)
          .get();
      final isUpdated = doc.data()!['address'].toString().toLowerCase() ==
          contract?.toLowerCase();
      statusUpdateVersion = isUpdated;
      return isUpdated;
    } catch (e) {
      statusUpdateVersion = false;
      rethrow;
    }
  }

  Future<bool> checkMaintenance({String? docName}) async {
    try {
      final doc = await _firestore
          .collection('maintenance')
          .doc(docName)
          .get();
      return doc.data()!['status'] == 'active';
    } catch (e) {
      rethrow;
    }
  }

  Future<String> checkTier({String? phone, String? email, String? list,}) async {
    try {
      if (phone == null || phone.isEmpty) {
        var configCtrl = Get.find<ConfigurationController>();
        final address = configCtrl.getAddress();
        final response = await AppApi.post(
          '${AppEnv.apiUrl}/getTierBUfromRDS',
          {'address': address},
        );

        role.value = response['status'] == 200 ? 'tier1' : 'normal';
      } else {
        final data = await checkWhiteListPPP7(phoneNumber: phone);
        if (data != null) {
          role.value = data.tier;
          // Assuming userLevel is a Provider or similar, mock it
          // context.read(userLevel).state = data.level;
        } else {

          var collections = await _firestore.collection('sandbox').get();
          for (var doc in collections.docs) {
            final tier = await _firestore
                .collection('sandbox')
                .doc(doc.id)
                .collection(list!)
                .where('phone', isEqualTo: phone)
                .limit(1)
                .get();
            for (var element in tier.docs) {
              print('พบเบอร์ ใน sandbox');
              print(element.id);
              // context.read(userLevel).state = element['level'];
            }
            if (tier.docs.isNotEmpty) {
              role.value = doc.id;
              // context.read(userLevel).state = tier.first['level'];
              break;
            } else {
              role.value = 'normal';
              // context.read(userLevel).state = '1';
            }
          }
        }
      }
      return role.value;
    } catch (e) {
      role.value = 'normal';
      rethrow;
    }
  }

  Future<WhiteListPPP7?> checkWhiteListPPP7({String? phoneNumber,}) async {
    try {
      final response = await http.post(
        Uri.parse('https://new.likepoint.io/getWhitelistPKG'),
        body: {'phoneNumber': phoneNumber},
      );
      if (response.statusCode == 200) {
        final body = json.decode(response.body);
        if (body['status'] == 200) {
          final result = WhiteListPPP7.fromJson(body);
          role.value = result.tier;
          Storage.save(StorageKeys.tokenLine, result.tokenLine);
          return result;
        }
      }
      return null;
    } catch (e) {
      rethrow;
    }
  }

  Future<RxString> checkBlackList({String? type, String? phone}) async {
    //หาจำนวน tier
    if (phone == null) {
      role.value = 'no';
    } else {
      final tier = await FirebaseFirestore.instance
          .collection('sandbox')
          .doc(type)
          .collection('blacklist')
          .where("phone", isEqualTo: phone)
          .limit(1)
          .get();
      if (tier.docs.isNotEmpty) {
        print(tier.docs.isEmpty);
        role.value = type.toString();
      } else {
        role.value = 'no';
      }
    }

    print("role : $role");
    return role;
  }

  Future<PageMaintenance> checkTierPermission({String? tierLevel, String? page}) async {
    try {
      final response = await _firestore
          .collection('tierController')
          .doc('controller')
          .collection(tierLevel!)
          .doc(page)
          .get();

      debugPrint('checkTierPermission: $response');
      return PageMaintenance.fromJson(response.data()!);
    } catch (e) {
      rethrow;
    }
  }

  Future<String> selectLanguage({String? language, detail}) async {
    /// unknown Doing???
    try {
      final langMap = {'th': 0, 'en': 1, 'lo': 2, 'km': 3, 'vi': 4};
      detailMaintenance = detail?[langMap[language] ?? 1] ?? '';
      return detailMaintenance;
    } catch (e) {
      detailMaintenance = '';
      rethrow;
    }
  }

  Future<bool> checkPermissionMenu({String? tierLevel, String? page}) async {
    try {
      final doc = await _firestore
          .collection('tierController')
          .doc('controller')
          .collection(tierLevel!)
          .doc(page)
          .get();
      permissionMenu.value = doc.data()!['permission'] ?? false;
      return permissionMenu.value;
    } catch (e) {
      permissionMenu.value = false;
      rethrow;
    }
  }
}