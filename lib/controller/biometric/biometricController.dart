import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:likewallet/service/biometricService.dart';
import 'package:likewallet/service/getStorage.dart';

class BiometricController extends GetxController {
  // Observable variables
  final RxBool isAvailable = false.obs;
  final RxBool isEnabled = false.obs;
  final RxBool isAuthenticating = false.obs;
  final RxList<BiometricType> availableBiometrics = <BiometricType>[].obs;
  final RxString biometricTypeName = ''.obs;

  @override
  void onInit() {
    super.onInit();
    initializeBiometric();
  }

  /// Initialize biometric settings
  Future<void> initializeBiometric() async {
    try {
      // Check if biometric is available on device
      final available = await BiometricService.isAvailable();
      isAvailable.value = available;

      if (available) {
        // Get available biometric types
        final biometrics = await BiometricService.getAvailableBiometrics();
        availableBiometrics.value = biometrics;

        // Set biometric type name for display
        biometricTypeName.value = BiometricService.getBiometricTypeName(biometrics);

        // Check if biometric is enabled in app settings
        isEnabled.value = getBiometricSetting();
      }
    } catch (e) {
      print('Error initializing biometric: $e');
      isAvailable.value = false;
      isEnabled.value = false;
    }
  }

  /// Authenticate using biometric
  Future<bool> authenticate({String? reason}) async {
    if (!isAvailable.value || !isEnabled.value) {
      return false;
    }

    try {
      isAuthenticating.value = true;

      final result = await BiometricService.authenticate(
        localizedReason: reason ?? getDefaultReason(),
        useErrorDialogs: true,
        stickyAuth: true,
      );

      return result;
    } catch (e) {
      print('Authentication error: $e');
      return false;
    } finally {
      isAuthenticating.value = false;
    }
  }

  /// Get default authentication reason
  String getDefaultReason() {
    if (availableBiometrics.contains(BiometricType.face)) {
      return 'authenticate_face_id'.tr;
    } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
      return 'authenticate_touch_id'.tr;
    }
    return 'authenticate_biometric'.tr;
  }

  /// Enable/disable biometric authentication
  void setBiometricEnabled(bool enabled) {
    isEnabled.value = enabled;
    Storage.save(StorageKeys.biometricEnabled, enabled);
  }

  /// Get biometric setting from storage
  bool getBiometricSetting() {
    return Storage.get(StorageKeys.biometricEnabled) ?? true;
  }

  /// Check if biometric should be shown (available and enabled)
  bool shouldShowBiometric() {
    return isAvailable.value && isEnabled.value && availableBiometrics.isNotEmpty;
  }

  /// Get biometric icon based on available types
  String getBiometricIcon() {
    if (availableBiometrics.contains(BiometricType.face)) {
      return 'assets/image/pin_code/face_id.png'; // You'll need to add this asset
    } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
      return 'assets/image/pin_code/fingerprint.png'; // This already exists
    }
    return 'assets/image/pin_code/fingerprint.png';
  }

  /// Refresh biometric status
  Future<void> refresh() async {
    await initializeBiometric();
  }
}
