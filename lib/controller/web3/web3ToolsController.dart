import 'package:get/get.dart';
import 'package:http/http.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:web3dart/web3dart.dart';
import 'package:web_socket_channel/io.dart';

class Web3toolsController extends GetxController {
  /// web3
  final EthereumAddress contractAddrLike =
  EthereumAddress.fromHex(AppEnv.contractLike);

  final EthereumAddress contractLock =
  EthereumAddress.fromHex(AppEnv.contractLock);
  final EthereumAddress contractAirdrop =
  EthereumAddress.fromHex(AppEnv.contractAirdrop);
  final EthereumAddress contractSlot =
  EthereumAddress.fromHex(AppEnv.contractSlotMachine);
  final EthereumAddress contractMessage =
  EthereumAddress.fromHex(AppEnv.contractMessage);
  final EthereumAddress contractLoan =
  EthereumAddress.fromHex(AppEnv.contractLoan);

  static int chainId = int.parse(AppEnv.chainId);
  static String abiContractLock = AppEnv.abiContractLock;
  static String abiContract = AppEnv.abiContractLike;
  static String abiContractAirdrop = AppEnv.abiContractAirdrop;
  static String abiContractSlot = AppEnv.abiContractSlot;
  static String abiContractLoan = AppEnv.abiContractLoan;
  static int gasClaim = int.parse(AppEnv.gasClaim.toString());
  static String abiContractMessage = AppEnv.abiContractMessage;
  String rpcUrl = AppEnv.rpcUrl;
  String wsUrl = AppEnv.wsUrl;
  ///

  Future<String> ClaimRewards() async {
    // TODO: implement signTransaction
    try{
      var pk = Storage.get<String>(StorageKeys.privateKey) ?? "";
      print(pk);

      final client = Web3Client(rpcUrl, Client(), socketConnector: () {
        return IOWebSocketChannel.connect(wsUrl).cast<String>();
      });
      final credentials = await client.credentialsFromPrivateKey(pk);
      final ownAddress = await credentials.extractAddress();
      print(ownAddress);
      final contractApprove = DeployedContract(
          ContractAbi.fromJson(abiContractAirdrop.toString(), 'Airdrop'),
          contractAirdrop);

      final transactionGetRewards = contractApprove.function('getRewards');
      print(contractAirdrop);
      int nonce = await client.getTransactionCount(ownAddress);
      final transaction = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contractApprove,
          function: transactionGetRewards,
          maxGas: gasClaim,
          nonce: nonce,
          gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
          parameters: [contractAddrLike, contractLock],
        ),
        chainId: chainId,
      );
      return transaction;
    }catch(e){
      print("Error in ClaimRewards: $e");
      return "error";
    }
  }

  Future<num> getBalance({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
        contractAddrLike);
    final balanceOf = contract.function('balanceOf');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final balanceLikepoint = await client.call(
        contract: contract,
        function: balanceOf,
        params: [new EthereumAddress.fromHex(address)]);

    var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);

    print('We have ${newBalance.getInEther} LIKE');

    return newBalance.getValueInUnit(EtherUnit.ether);
  }

  Future<String> requestUnlock(
      {required String pk, required String value, required String all}) async {
    EtherAmount amount;
    if (all == 'yes') {
      amount = EtherAmount.fromUnitAndValue(EtherUnit.wei, value);
    } else {
      amount = EtherAmount.fromUnitAndValue(
          EtherUnit.ether, int.parse(value.split(".")[0]));
    }
    print("amount $amount");
    print("getInWei" + amount.getInWei.toString());

    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);
    print("contract $contract");
    int nonce = await client.getTransactionCount(ownAddress);
    final transfer = contract.function('requestWithdraw');
    try {
      final transaction = await client.sendTransaction(
        credentials,
        // Transaction.callContract(
        //   from: ownAddress,
        //   contract: contract,
        //   function: transfer,
        //   maxGas: gasClaim,
        //   nonce: nonce,
        //   gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
        //   parameters: [contractAddrLike, amount.getInWei, BigInt.from(10)],
        // ),
        Transaction.callContract(
          from: ownAddress,
          contract: contract,
          function: transfer,
          maxGas: gasClaim,
          nonce: nonce,
          gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
          parameters: [contractAddrLike, amount.getInWei],
        ),
        chainId: chainId,
      );
      return transaction;
    } catch (e) {
      return 'Transaction Error [$e]';
    }
  }

  Future<String> sendTransaction(
      {required String pk, required String to, required String value}) async {
    // TODO: implement signTransaction

    try {
      final EthereumAddress receiver = EthereumAddress.fromHex(to);
      // final amount = EtherAmount.fromUnitAndValue(EtherUnit.ether, value.substring(0, valueSend).toString());
      EtherAmount amount = EtherAmount.fromUnitAndValue(
          EtherUnit.ether, int.parse(value.split(".")[0]).toString());
      final client = Web3Client(rpcUrl, Client(), socketConnector: () {
        return IOWebSocketChannel.connect(wsUrl).cast<String>();
      });
      final credentials = await client.credentialsFromPrivateKey(pk);
      final ownAddress = await credentials.extractAddress();
      int nonce = await client.getTransactionCount(ownAddress);
      final contract = DeployedContract(
          ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
          contractAddrLike);

      final transfer = contract.function('transfer');

      final transaction = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contract,
          function: transfer,
          nonce: nonce,
          maxGas: gasClaim,
          gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
          parameters: [receiver, amount.getInWei],
        ),
        chainId: chainId,
      );
      return transaction;
    } catch (e) {
      print(e);
      return '0';
    }
  }

  Future<dynamic> checkRewards() async {
    try{
      var address = Storage.get<String>(StorageKeys.addressETH) ?? "";

      print("address " + address.toString());
      final contract = DeployedContract(
          ContractAbi.fromJson(abiContractAirdrop.toString(), 'Airdrop'),
          contractAirdrop);

      print("contractAddrLike " + contractAddrLike.toString());
      final checkRewards = contract.function('checkRewards');
      print("checkRewards");
      final client = Web3Client(rpcUrl, Client(), socketConnector: () {
        return IOWebSocketChannel.connect(wsUrl).cast<String>();
      });
      print("go here");
      final balanceClaim = await client.call(
          contract: contract,
          function: checkRewards,
          params: [
            contractAddrLike,
            contractLock,
            new EthereumAddress.fromHex(address)
          ]);

      var newBalance = EtherAmount.inWei(balanceClaim.first as BigInt);

      print('We have chaim rewards ${newBalance.getInEther} LIKE');

      return newBalance.getValueInUnit(EtherUnit.ether);
    }catch(e){
      print("Error in checkRewards: $e");
      return false;
    }
  }

  Future<String> unlockLikePointV2({required String pk}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);
    int nonce = await client.getTransactionCount(ownAddress);
    final transfer = contract.function('withdrawToken');
    final transaction = await client.sendTransaction(
      credentials,
      Transaction.callContract(
        from: ownAddress,
        contract: contract,
        function: transfer,
        maxGas: gasClaim,
        nonce: nonce,
        gasPrice: EtherAmount.inWei(BigInt.from(10 * 1e9)),
        parameters: [contractAddrLike],
      ),
      chainId: chainId,
    );
    return transaction;
  }

  Future<String> checkClaim({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractAirdrop.toString(), 'Airdrop'),
        contractAirdrop);

    final checkClaim = contract.function('Claim');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final dataClaimer = await client.call(
        contract: contract,
        function: checkClaim,
        params: [contractAddrLike, new EthereumAddress.fromHex(address)]);

    print("dataClaimer " + dataClaimer[0].toString());
    return dataClaimer[1].toString();
  }

  Future<String> getRound() async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractAirdrop.toString(), 'Airdrop'),
        contractAirdrop);

    final checkClaim = contract.function('Round');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final dataClaimer = await client.call(
        contract: contract, function: checkClaim, params: [contractAddrLike]);

    print("getRound " + dataClaimer[0].toString());
    return dataClaimer[0].toString();
  }

  Future<num> getBalanceLock({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);
    try {
      final balanceOf = contract.function('getLock');
      final client = Web3Client(rpcUrl, Client(), socketConnector: () {
        return IOWebSocketChannel.connect(wsUrl).cast<String>();
      });
      print(new EthereumAddress.fromHex(address));
      print(contractAddrLike);

      final balanceLikepoint = await client.call(
          contract: contract,
          function: balanceOf,
          params: [contractAddrLike, new EthereumAddress.fromHex(address)]);

      var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);

      print('We have lock ${newBalance.getInEther} LIKE');

      return newBalance.getValueInUnit(EtherUnit.ether);
    } catch (e) {
      print(e);
      return 0.0;
    }
  }

  Future<String> getDepositTime({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);

    final getDepositTime = contract.function('getDepositTime');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final dataClaimer = await client.call(
        contract: contract,
        function: getDepositTime,
        params: [contractAddrLike, new EthereumAddress.fromHex(address)]);

    print("dataClaimer " + dataClaimer[0].toString());
    return dataClaimer[0].toString();
  }

  Future<int> isWithdraw({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);

    final balanceOf = contract.function('tokens');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    print('isWithdraw');
    try {
      final dataLock = await client.call(
          contract: contract,
          function: balanceOf,
          params: [contractAddrLike, new EthereumAddress.fromHex(address)]);
      var withdrawStatus = dataLock[5];

     print('withdrawStatus '+ withdrawStatus.toString());
      return int.parse(withdrawStatus.toString());
    } catch (e) {
      print(e);
      return 0;
    }

//    return newBalance.getValueInUnit(EtherUnit.ether);
  }

  Future<List<String>> getUnlockDate({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);

    final balanceOf = contract.function('tokens');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    try {
      final dataLock = await client.call(
          contract: contract,
          function: balanceOf,
          params: [contractAddrLike, new EthereumAddress.fromHex(address)]);

//    print('Data Lock ${dataLock[1]} ');
//    print(new DateTime.now().millisecondsSinceEpoch/1000);
      //check time unlock
      double nextUnlock = dataLock[1].toDouble();
      double currentTime = new DateTime.now().millisecondsSinceEpoch / 1000;
      double diffUnlock = nextUnlock - currentTime;

      double minute;
      double hour;
      int Hour;
      int Min;

      if (diffUnlock > 0) {
        minute = diffUnlock / 60 % 60;
        hour = diffUnlock / 3600;
        Hour = int.parse(hour.toString().split(".")[0]);
        Min = int.parse(minute.toString().split(".")[0]);
        print(Hour.toString());
        print(Min.toString());
      } else {
        Hour = 0;
        Min = 0;
        diffUnlock = 0;
      }

      print('dataLock4' + dataLock.toString());
      return [
        Hour.toString(),
        Min.toString(),
        diffUnlock.toString(),
        dataLock[7].toString(),
        dataLock[5].toString()
      ];
    } catch (e) {
      return ['0', '0', '0', '0', '0'];
    }
  }

  Future<String> transferMessage({required String pk, required String to, required String value, required String message}) async {
    // TODO: implement signTransaction
    try {
      EtherAmount amount = EtherAmount.fromUnitAndValue(
          EtherUnit.ether, int.parse(value.split(".")[0]).toString());

      final client = Web3Client(rpcUrl, Client(), socketConnector: () {
        return IOWebSocketChannel.connect(wsUrl).cast<String>();
      });
      final credentials = await client.credentialsFromPrivateKey(pk);
      final ownAddress = await credentials.extractAddress();
      print(ownAddress);

      final contract = DeployedContract(
          ContractAbi.fromJson(
              abiContractMessage.toString(), 'TransferWithMessageTRC20'),
          contractMessage);
      final transfer = contract.function('transferMessage');
      int nonce = await client.getTransactionCount(ownAddress);
      final contractApprove = DeployedContract(
          ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
          contractAddrLike);

      final allower = contractApprove.function('allowance');
      final balanceApprove = await client
          .call(contract: contractApprove, function: allower, params: [
        new EthereumAddress.fromHex(ownAddress.toString()),
        new EthereumAddress.fromHex(contractMessage.toString())
      ]);

      var approved = EtherAmount.inWei(balanceApprove.first as BigInt);

      print('get in wei : ' + approved.getInWei.toString());
      print('get in wei 2: ' +
          EtherAmount.fromUnitAndValue(EtherUnit.wei, '1000000000000000000')
              .getInWei
              .toString());

      //ถ้า approve == 0 ให้ทำการ approve ปกติ
      if (approved.getInWei ==
          EtherAmount.fromUnitAndValue(EtherUnit.wei, '0').getInWei) {
        final transferApprove = contractApprove.function('approve');
//      final appv = EtherAmount.inWei(BigInt.parse('0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'));
//      print(appv.getInWei);
        final transactionApprove = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contractApprove,
            function: transferApprove,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractMessage,
              EtherAmount.inWei(BigInt.parse(
                  '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
                  .getInWei
            ],
          ),
          chainId: chainId,
        );

        // print(message);
        final transferMessage = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractAddrLike,
              EthereumAddress.fromHex(to),
              amount.getInWei,
              message
            ],
          ),
          chainId: chainId,
        );
        return transferMessage;
      } else {
        // ถ้า approve ไว้แล้วน้อยกว่าค่าจะที่โอน ให้ทำการ ส่งก้อนแรกแล้ว approve เพิ่มแล้วส่งที่เหลือ
        if (amount.getInWei > approved.getInWei) {
          BigInt secondAmount = amount.getInWei - approved.getInWei;
          print(secondAmount);
          amount = approved;
          final transferMessage = await client.sendTransaction(
            credentials,
            Transaction.callContract(
              from: ownAddress,
              contract: contract,
              function: transfer,
              maxGas: gasClaim,
              nonce: nonce,
              gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
              parameters: [
                contractAddrLike,
                EthereumAddress.fromHex(to),
                amount.getInWei,
                message
              ],
            ),
            chainId: chainId,
          );

          final transferApprove = contractApprove.function('approve');

          final transactionApprove = await client.sendTransaction(
            credentials,
            Transaction.callContract(
              from: ownAddress,
              contract: contractApprove,
              function: transferApprove,
              maxGas: gasClaim,
              nonce: nonce + 1,
              gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
              parameters: [
                contractMessage,
                EtherAmount.fromUnitAndValue(
                    EtherUnit.wei, secondAmount.toString())
                    .getInWei
              ],
            ),
            chainId: chainId,
          );
          final transferMessage2 = await client.sendTransaction(
            credentials,
            Transaction.callContract(
              from: ownAddress,
              contract: contract,
              function: transfer,
              maxGas: gasClaim,
              nonce: nonce + 2,
              gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
              parameters: [
                contractAddrLike,
                EthereumAddress.fromHex(to),
                EtherAmount.fromUnitAndValue(
                    EtherUnit.wei, secondAmount.toString())
                    .getInWei,
                message
              ],
            ),
            chainId: chainId,
          );
          return transferMessage2;
        } else {
          double etherValue = double.parse(value);
          BigInt weiValue = BigInt.from(etherValue * 1e18);
           //print('เข้าโอน 1 : 1 ,  value=>$value   and ...$weiValue ...$message' );
          final transferMessage = await client.sendTransaction(
            credentials,
            Transaction.callContract(
              from: ownAddress,
              contract: contract,
              function: transfer,
              maxGas: gasClaim,
              nonce: nonce,
              gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
              parameters: [
                contractAddrLike,
                EthereumAddress.fromHex(to),
                //amount.getInWei,
                weiValue,
                message
              ],
            ),
            chainId: chainId,
          );
//  String transactionApprove='';
          return transferMessage;
        }
      }
    } catch (e) {
      print(e);
      return 'e';
    }
  }

  Future<String> dynamicSendContract({required String pk, required String contractAddress, required String abi, required String callFunction, required String to, required String value, required String message}) async {
    // TODO: implement signTransaction

    EtherAmount amount = EtherAmount.fromUnitAndValue(
        EtherUnit.ether, int.parse(value.split(".")[0]));
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    final credentials = await client.credentialsFromPrivateKey(pk);
    final ownAddress = await credentials.extractAddress();
    print(ownAddress);
    final contract = DeployedContract(
        ContractAbi.fromJson(abi.toString(), callFunction),
        EthereumAddress.fromHex(contractAddress));
    print(amount.getInWei);
    final transfer = contract.function(callFunction);
    int nonce = await client.getTransactionCount(ownAddress);
    final contractApprove = DeployedContract(
        ContractAbi.fromJson(abiContract.toString(), 'LIKEPOINT'),
        contractAddrLike);
    print(amount.getInWei);
    final transferApprove = contractApprove.function('approve');

    final allower = contractApprove.function('allowance');
    final balanceApprove = await client
        .call(contract: contractApprove, function: allower, params: [
      new EthereumAddress.fromHex(ownAddress.toString()),
      new EthereumAddress.fromHex(contractAddress.toString())
    ]);
    var approved = EtherAmount.inWei(balanceApprove.first as BigInt);
    print(approved.getInWei);
    if (approved.getInWei ==
        EtherAmount.fromUnitAndValue(EtherUnit.wei, '0').getInWei) {
      final transactionApprove = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contractApprove,
          function: transferApprove,
          maxGas: gasClaim,
          nonce: nonce,
          gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
          parameters: [
            EthereumAddress.fromHex(contractAddress),
            EtherAmount.inWei(BigInt.parse(
                '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
                .getInWei
          ],
        ),
        chainId: chainId,
      );

      // print(message);
      final transferMessage = await client.sendTransaction(
        credentials,
        Transaction.callContract(
          from: ownAddress,
          contract: contract,
          function: transfer,
          maxGas: gasClaim,
          nonce: nonce + 1,
          gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
          parameters: [
            contractAddrLike,
            EthereumAddress.fromHex(to),
            amount.getInWei,
            message
          ],
        ),
        chainId: chainId,
      );
//  String transactionApprove='';
      return transferMessage;
    } else {
      if (amount.getInWei > approved.getInWei) {
        BigInt secondAmount = amount.getInWei - approved.getInWei;
        amount = approved;
        final transferMessage = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractAddrLike,
              EthereumAddress.fromHex(to),
              amount.getInWei,
              message
            ],
          ),
          chainId: chainId,
        );

        final transferApprove = contractApprove.function('approve');
        final transactionApprove = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contractApprove,
            function: transferApprove,
            maxGas: gasClaim,
            nonce: nonce + 1,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              EthereumAddress.fromHex(contractAddress),
              EtherAmount.inWei(BigInt.parse(
                  '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'))
                  .getInWei
            ],
          ),
          chainId: chainId,
        );

        final transferMessage2 = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce + 2,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractAddrLike,
              EthereumAddress.fromHex(to),
              EtherAmount.fromUnitAndValue(
                  EtherUnit.wei, secondAmount.toString())
                  .getInWei,
              message
            ],
          ),
          chainId: chainId,
        );
        return transferMessage2;
      } else {
        final transferMessage = await client.sendTransaction(
          credentials,
          Transaction.callContract(
            from: ownAddress,
            contract: contract,
            function: transfer,
            maxGas: gasClaim,
            nonce: nonce,
            gasPrice: EtherAmount.inWei(BigInt.from(4 * 1e9)),
            parameters: [
              contractAddrLike,
              EthereumAddress.fromHex(to),
              amount.getInWei,
              message
            ],
          ),
          chainId: chainId,
        );
//  String transactionApprove='';
        return transferMessage;
      }
    }
  }

  Future<String> getBalanceLockWei({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);
    try {
      final balanceOf = contract.function('getLock');
      final client = Web3Client(rpcUrl, Client(), socketConnector: () {
        return IOWebSocketChannel.connect(wsUrl).cast<String>();
      });
      print(new EthereumAddress.fromHex(address));
      print(contractAddrLike);

      final balanceLikepoint = await client.call(
          contract: contract,
          function: balanceOf,
          params: [contractAddrLike, new EthereumAddress.fromHex(address)]);

      var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);

      print('We have lock ${newBalance.getInEther} LIKE');

      return newBalance.getInWei.toString();
    } catch (e) {
      print(e);
      return "0";
    }
  }

  Future<num> getBalanceLockAuto({required String address}) async {
    final contract = DeployedContract(
        ContractAbi.fromJson(abiContractLock.toString(), 'LockERC20'),
        contractLock);

    final balanceOf = contract.function('getAmount');
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });
    print(new EthereumAddress.fromHex(address));
    print(contractAddrLike);
    try {
      final balanceLikepoint = await client.call(
          contract: contract,
          function: balanceOf,
          params: [contractAddrLike, new EthereumAddress.fromHex(address)]);

      var newBalance = EtherAmount.inWei(balanceLikepoint.first as BigInt);

      print('We have lock ${newBalance.getInEther} LIKE');

      return newBalance.getValueInUnit(EtherUnit.ether);
    } catch (e) {
      print(e);
      return 0;
    }
  }

  Future<List<dynamic>> getLoanV2({required String address}) async {
    final client = Web3Client(rpcUrl, Client(), socketConnector: () {
      return IOWebSocketChannel.connect(wsUrl).cast<String>();
    });

    //เรียก contract Loan
    final loan = DeployedContract(
        ContractAbi.fromJson(abiContractLoan.toString(), 'Loan'), contractLoan);
    //เรียกฟังก์ชั่น maxumumBorrow
    final loanContract = loan.function('contractLoans');
    //สั่งเรียก maximumBorrow และได้ผลลัพธ์กลับมา
    final callLoanContract = await client.call(
//        sender: new EthereumAddress.fromHex(address),
        contract: loan,
        function: loanContract,
        params: [new EthereumAddress.fromHex(address)]);

//    print(callLoanContract);

    //callback //array 10 ตำแหน่ง ["0address คนยืม", "1.วลายืม", "2.อัตราดอกเบี้ย", "3.เวลาสิ้นสุดสัญญา", "4หนี้คงเหลือ", "5. ดอกเบี้ยที่จ่ายแล้ว", "6. จำนวนเงินต้น", "7. หลักประกัน", "8. จ่ายเงินต้นมาแล้วทั้งหมด", "9. จ่ายดอกเบี้ยมาแล้วทั้งหมด", "10. สถานะการกู้ยืม 1 ยังกู้อยู่ 0 หมดสัญญาแล้ว"]
    return callLoanContract;
  }
}