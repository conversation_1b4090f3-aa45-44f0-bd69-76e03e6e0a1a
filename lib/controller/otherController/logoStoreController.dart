import 'package:get/get.dart';
import 'package:likewallet/model/components/logoModel.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/service/httpRequest.dart';


class LogoStoreController extends GetxController {
  var group = <dynamic>[].obs;
  var search = <Logo>[].obs;
  var list = <Logo>[].obs;

  @override
  onInit() async {
    super.onInit();
    await listSpendkpayShop();
  }

  Future<void> listSpendkpayShop() async {
    print('Fetching listSpendkpayShop...');
    try {
      var response = await AppApi.post('${AppEnv.apiUrl}/listQucikpayShop', {
        "apiKey": AppEnv.APIKEY,
        "secretKey": AppEnv.SECRETKEY,
      });

      print(response);
      // เคลียร์ก่อนเพิ่มใหม่
      list.clear();
      group.clear();

      // ดึง result
      for (var user in response["result"]) {
        list.add(Logo.from<PERSON><PERSON>(user));
      }
      search.assignAll(list);

      // ดึง group
      group.assignAll(response["group"]);
    } catch (e) {
      print('Exception: $e');
    }
  }
}