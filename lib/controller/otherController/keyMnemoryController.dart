import 'package:encrypt/encrypt.dart';
import 'dart:math';

import 'package:get/get.dart';
import 'package:likewallet/service/getStorage.dart';

class KeyMNemoryController extends GetxController {

  String keyEncrypt = "";
  String ivEncrypt = "";

  Future<void> initializeEncryptionKeys() async {
    keyEncrypt = Storage.get(StorageKeys.keyEncrypt) ?? "";
    ivEncrypt = Storage.get(StorageKeys.ivEncrypt) ?? "";

    // Generate new keys only if they don't exist in storage
    if (keyEncrypt.isEmpty || ivEncrypt.isEmpty) {
      await generateKey();
    }
  }

  Future<String> decrypt(String text) async {
    List<String> getKey = await getKeyEncrypt();
    keyEncrypt = getKey[0];
    ivEncrypt = getKey[1];

    final key = Key.fromUtf8(keyEncrypt);
    final iv = IV.fromUtf8(ivEncrypt);
    final encrypter = Encrypter(AES(key, mode: AESMode.cbc));
    final decrypted = encrypter.decrypt(Encrypted.fromBase64(text), iv: iv);
    return decrypted;
  }

  // Generates and stores a new encryption key and IV
  Future<bool> generateKey() async {
    keyEncrypt = _randomString(32);
    ivEncrypt = _randomString(16);

    Storage.save(StorageKeys.keyEncrypt, keyEncrypt);
    Storage.save(StorageKeys.ivEncrypt, ivEncrypt);

    return true;
  }

  // Retrieves the encryption key and IV from storage
  Future<List<String>> getKeyEncrypt() async {
    keyEncrypt = Storage.get(StorageKeys.keyEncrypt);
    ivEncrypt = Storage.get(StorageKeys.ivEncrypt);

    return [keyEncrypt, ivEncrypt];
  }

  // Generates a random string of specified length
  String _randomString(int length) {
    const String chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    Random random = Random();
    return String.fromCharCodes(
      Iterable.generate(
        length,
            (_) => chars.codeUnitAt(random.nextInt(chars.length)),
      ),
    );
  }
}