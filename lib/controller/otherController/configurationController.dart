import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/service/httpRequest.dart';
import 'package:lock_to_win/library/crypto.dart';
import "package:hex/hex.dart";
import 'package:web3dart/credentials.dart';
import 'package:bip32/bip32.dart' as bip32;
import 'package:bip39/bip39.dart' as bip39;

class ConfigurationController extends GetxController {
  // Dependencies
  late final _encrypt = CryptoEncrypt();

  Future<void> setMnemonic(String value, phoneNumber, secret) async {
    try {
      await _encrypt.generateKey();
      final dataKey = await _encrypt.getKeyEncrypt();

      Map dataBody = {
        'phone_number': phoneNumber,
        'secret': secret,
        'keyEncrypt': dataKey[0],
        'ivEncrypt': dataKey[1],
      };
      var response = await AppApi.post("${AppEnv.apiUrl}/verifySecret", dataBody);
      if (response.statusCode == 200) {
        final mnemonic = await _encrypt.decrypt(value);
        Storage.save(StorageKeys.mnemonic, mnemonic);
      } else {
        throw Exception('Failed to verify secret');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<void> setPrivateKey(String value) async {
    try {
      Storage.save(StorageKeys.privateKey, value);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> setAddress(String value) async {
    try {
      Storage.save(StorageKeys.addressETH, value);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> setupDone(bool value) async {
    try {
      Storage.save(StorageKeys.didSetupWallet, value);
    } catch (e) {
      rethrow;
    }
  }

  Future<String> getMnemonic() async {
    try {
      return Storage.get<String>(StorageKeys.mnemonic) ?? 'no';
    } catch (e) {
      rethrow;
    }
  }

  Future<String> getPrivateKey() async {
    try {
      // String? privateKeyStore = Storage.get<String>(StorageKeys.privateKey) ?? '';

      // if (privateKeyStore == '') {
        var mnemonic = await Storage.get(StorageKeys.mnemonic);
        if (mnemonic == null) {
          throw Exception('Mnemonic is required when private key is not stored');
        }
        String seed = bip39.mnemonicToSeedHex(mnemonic);
        final root = bip32.BIP32.fromSeed(HEX.decode(seed) as Uint8List);
        final string = root.toBase58();
        final restored = bip32.BIP32.fromBase58(string);
        final child1 = restored.derivePath("m/44'/60'/0'/0/0");
        final privateKey = HEX.encode(child1.privateKey as List<int>);

        print(privateKey);
        Storage.save(StorageKeys.privateKey, privateKey);
        return privateKey;
      // } else {
      //   return privateKeyStore;
      // }
    } catch (e) {
      rethrow;
    }
  }

  String getAddress() {
    try {
      return Storage.get<String>(StorageKeys.addressETH) ?? '';
    } catch (e) {
      rethrow;
    }
  }

  bool didSetupWallet() {
    try {
      return Storage.get<bool>(StorageKeys.didSetupWallet) ?? false;
    } catch (e) {
      rethrow;
    }
  }
}