import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/controller/walletController/walletDataController.dart';
import 'package:likewallet/controller/web3/web3ToolsController.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:intl/intl.dart' as formatIntl;
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/service/httpRequest.dart';
import 'package:web3dart/web3dart.dart';

class LockNEarnController extends GetxController {
  // Observable variables
  final selected = true.obs;
  final isLoading = false.obs;
  final isUnlock = false.obs;
  final success = false.obs;
  final unlockAll = false.obs;
  final awaitUnlock = false.obs;
  final statusBorrow = false.obs;
  final remainingTime = '00:00:00'.obs;
  final amountUnlock = '500'.obs;
  final lockedBalance = '0'.obs;
  final totalLock = '0'.obs;
  final totalLike = '0'.obs;
  final totalShowUnlock = '0'.obs;
  final awaitShowUnlock = '0'.obs;
  final showConfirmButtons = false.obs;
  final showAllButton = true.obs;
  final onTapUnlock = false.obs;
  final unlockPreviewText = ''.obs;
  final dept = 0.0.obs;
  final lockNew = 0.0.obs;
  final lockAuto = 0.0.obs;
  final lpcu = 0.0.obs;
  var unlockHour;
  var unlockMin;
  var diffTime;

  final f = new formatIntl.NumberFormat("###,###.##");

  // Text controllers
  final amountLockEditor = TextEditingController();
  final amountUnLockEditor = TextEditingController();

  final amountUnLockText = ''.obs;

  late Web3toolsController web3Ctrl;

  // Carousel images
  final List<String> carouselImages = [
    'assets/image/locklike/1.png',
    'assets/image/locklike/2.png',
    'assets/image/locklike/3.png',
  ];

  @override
  void onInit() {
    super.onInit();

    // Initialize web3Ctrl
    web3Ctrl = Get.find<Web3toolsController>();

    // เพิ่ม listener
    amountUnLockEditor.addListener(() {
      amountUnLockText.value = amountUnLockEditor.text;
      print("📝 ใน controller: ${amountUnLockEditor.text}");
    });

  }

  // // Initialize data
  Future initializeData() async {
    // Load initial data here
    await getLockNewN_LPCU();
    await setData();
  }

  void changeOnTapUnlock() {
    onTapUnlock.value = !onTapUnlock.value;
    print("onTapUnlock: ${onTapUnlock.value}");
  }

  Future setData() async {
    try{
      var addressETH = Storage.get(StorageKeys.addressETH);
      if (addressETH == null || addressETH.isEmpty) {
      //print('❌ addressETH is null or empty');
      return;
    }
      // addressETH = '******************************************';
      // var mnemonic = Storage.get(StorageKeys.mnemonic);
      // var privateKey = Storage.get(StorageKeys.privateKey);
      web3Ctrl.getBalanceLockWei(address: addressETH).then((amount) {

          totalShowUnlock.value = amount.toString();
          print("totalShowUnlock :$totalShowUnlock");

      });
      //ดึงเวลาปลดล็อค
      web3Ctrl.getUnlockDate(address: addressETH).then((data) {
        print('unlock' + data.toString());
        print('unlock' + data[3]);
        if (data[3] == '1') {


            awaitShowUnlock.value = (double.parse(data[4]) / 10e17).toString();
            print(" awaitShowUnlock :$awaitShowUnlock");
            awaitUnlock.value = true;
            // isWithdraw = statusWithdraw.ACTIVE;

        }
        if (int.parse(data[0]) >= 0 || int.parse(data[1]) >= 0) {

            isUnlock.value = true;
            unlockHour = data[0];
            unlockMin = data[1];
            //ถ้าหมดเวลาและถอนได้แล้ว
            if (int.parse(unlockHour) == 0 &&
                int.parse(unlockMin) == 0 &&
                data[3] == '1') {
              //คำสั่งถอน
              _unlockLikePoint();
            }
            print(unlockHour);
            print('manzer');
            print(double.parse(data[2]));
            diffTime = double.parse(data[2]);
        } else {

            isUnlock.value = false;
            diffTime = double.parse(data[2]);

          print(diffTime);
        }
      });
      //เช็คยอด
      web3Ctrl.getBalance(address: addressETH).then((balance) {
        amountUnlock.value = balance.toString();
        // streamAmountUnlock.sink.add(amountUnlock);
      });
      //เช็คยอดการล็อค
      web3Ctrl.getBalanceLockAuto(address: addressETH).then((balanceLockAuto) async {
        web3Ctrl.getBalanceLock(address: addressETH).then((balanceLock) async {
          await web3Ctrl.getLoanV2(address: addressETH).then((loan) {
            if (loan[11].toString() == '1') {
              statusBorrow.value = true;
            }
            dept.value = double.parse(
                EtherAmount.inWei(loan[6] as BigInt).getInEther.toString());
            print("เงินต้น" +
                EtherAmount.inWei(loan[6] as BigInt).getInEther.toString());
          });
          lockedBalance.value = f.format(balanceLock + dept.value).toString();
          double total = 0.00 +
              double.parse(lockedBalance.value.replaceAll(',', ''));
          print(balanceLock);
          totalLock.value = balanceLock.toString();

          // streamAmountLock.sink.add(lockedBalance.value);
          lockAuto.value = balanceLockAuto.toDouble() - (balanceLock + dept.value);
          // streamAmountAutoLock.sink.add(lockAuto.toString());
          Future.delayed(Duration(seconds: 1)).then((value) {
            totalLike.value =
                (double.parse(amountUnlock.value) + double.parse(totalLock.value) + lockAuto.value + lockNew.value).toString();

            // streamTotalLike.sink.add(context.read(totalAmount).state.toString());
          });
          //get lockedBalance
          isLoading.value = false;
        });
      });
    }catch(e) {
      print('Error in setData: $e');
    }
  }

  // Load balance data
  void loadBalanceData() async {

    var addressETH = Storage.get(StorageKeys.addressETH);

    // User has locked tokens, calculate rewards
    final balanceLock = await web3Ctrl.getBalanceLock(address: addressETH);

    lockedBalance.value = f.format(balanceLock + dept.value).toString();

    await Future.delayed(Duration(seconds: 3));
    isLoading.value = false;
  }

  // Toggle between lock and unlock mode
  void toggleMode(bool isLockMode) {
    selected.value = isLockMode;
    // Clear input when switching modes
    if (isLockMode) {
      amountUnLockEditor.clear();
    } else {
      amountLockEditor.clear();
    }
  }

  // Set all amount for locking

  void cancelAction() {
    amountLockEditor.clear();
    amountUnLockEditor.clear();
    unlockPreviewText.value = ''; // ✅ ซ่อนข้อความ
    showConfirmButtons.value = false;
  }

  Future getLockNewN_LPCU() async {
    try{
      var address = Storage.get(StorageKeys.addressETH);
      var res1 = await AppApi.post("${AppEnv.apiUrl}/getAutocompound",
        {"address": address},
      );
      print(res1);
      if (res1.data["statusCode"] == 200) {
        print("lockNew:" + res1.data["result"].toString());
        lockNew.value = double.parse(res1.data["result"].toString());
      }

      var res2 = await AppApi.post("${AppEnv.apiUrl}/getLPCU",
        {"address": address},
      );
      print(res2);
      if (res2.data["statusCode"] == 200) {
        print("lpcu:" + res2.data["result"].toString());
        lpcu.value = double.parse(res2.data["result"].toString());
      }
    }catch(e) {
      print('Error in getLockNewN_LPCU: $e');
    }
  }

  // Validate lock amount
  // bool validateLockAmount() {
  //   if (amountLock.text.isEmpty) return false;
  //
  //   try {
  //     final lockAmount = int.parse(amountLock.text.replaceAll(',', ''));
  //     final availableAmount = int.parse(getAvailableBalance().replaceAll(',', ''));
  //
  //     return lockAmount > 0 && lockAmount <= availableAmount;
  //   } catch (e) {
  //     return false;
  //   }
  // }

  // Validate unlock amount
  bool validateUnlockAmount() {
    if (amountUnLockEditor.text.isEmpty) return false;

    try {
      final unlockAmount = int.parse(amountUnLockEditor.text.replaceAll(',', ''));
      final lockedAmount = int.parse(lockedBalance.value.replaceAll(',', ''));

      return unlockAmount > 0 && unlockAmount <= lockedAmount;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _unlockLikePoint() async {

    var privateKey = Storage.get(StorageKeys.privateKey);

    var transaction = await web3Ctrl.unlockLikePointV2(pk: privateKey);
    print('withdraw tx : $transaction');
    await new Future.delayed(new Duration(seconds: 5));
    setData();
    return true;
  }

  // Execute lock operation
  Future<void> executeLock() async {
    // if (!validateLockAmount()) {
    //   Get.snackbar(
    //     'Error',
    //     'Invalid lock amount',
    //     snackPosition: SnackPosition.BOTTOM,
    //     backgroundColor: Colors.red,
    //     colorText: Colors.white,
    //   );
    //   return;
    // }

    isLoading.value = true;

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Update balances
      final lockAmount = int.parse(amountLockEditor.text.replaceAll(',', ''));
      final currentLocked = int.parse(lockedBalance.value.replaceAll(',', ''));
      final newLockedBalance = currentLocked + lockAmount;

      lockedBalance.value = NumberFormat("#,###").format(newLockedBalance);
      amountLockEditor.clear();

      success.value = true;

      Get.snackbar(
        'Success',
        'Successfully locked $lockAmount LIKE',
        snackPosition: SnackPosition.TOP,
            backgroundColor: const Color(0xFF8CF9F0)
      );

      // Hide success message after 3 seconds
      Future.delayed(const Duration(seconds: 3), () {
        success.value = false;
      });

    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to lock amount: $e',
        snackPosition: SnackPosition.TOP,
            backgroundColor: const Color(0xFF8CF9F0)
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Execute unlock operation
  Future<void> executeUnlock() async {
    if (!validateUnlockAmount()) {
      Get.snackbar(
        'Error',
        'Invalid unlock amount',
        snackPosition: SnackPosition.TOP,
            backgroundColor: const Color(0xFF8CF9F0)
      );
      return;
    }

    isLoading.value = true;

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Update balances
      final unlockAmount = int.parse(amountUnLockEditor.text.replaceAll(',', ''));
      final currentLocked = int.parse(lockedBalance.value.replaceAll(',', ''));
      final newLockedBalance = currentLocked - unlockAmount;

      lockedBalance.value = NumberFormat("#,###").format(newLockedBalance);
      amountUnLockEditor.clear();

      success.value = true;

      Get.snackbar(
        'Success',
        'Successfully unlocked $unlockAmount LIKE',
        snackPosition: SnackPosition.TOP,
            backgroundColor: const Color(0xFF8CF9F0)
      );

      // Hide success message after 3 seconds
      Future.delayed(const Duration(seconds: 3), () {
        success.value = false;
      });

    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to unlock amount: $e',
        snackPosition: SnackPosition.TOP,
            backgroundColor: const Color(0xFF8CF9F0)
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Handle action button press
  void handleActionButton() {
    if (selected.value) {
      executeLock();
    } else {
      executeUnlock();
    }
  }

  Future checkRewards() async {
    //check status claim
    //check รอบที่ผู้ใช้รับปัจจุบัน
    try {

      var addressETH = Storage.get(StorageKeys.addressETH);


      var balanceRewards = await web3Ctrl.checkRewards();
      print('balanceLock ' + balanceRewards.toString());
      var lastTime = await web3Ctrl.checkClaim(address: addressETH);
      print('lastTime ' + lastTime.toString());
      //check current round airdrop
      //เช็ครอบที่มีการแจกรางวัลจากทีมงาน
      var roundAirdrop = await web3Ctrl.getRound();
      print('roundAirdrop ' + roundAirdrop.toString());
      //ถ้ารอบที่ผู้รับมากกว่าหรือเท่ากับรอบปัจจุบันให้ทำการฝากเหรียญเพิ่มได้
      if (int.parse(lastTime) >= int.parse(roundAirdrop) ||
          balanceRewards == 0.0) {
        //claim ไปแล้ว
        return false;
      } else {
        //ยังไม่ claim
        return true;
      }
    } catch (e) {
      return false;
    }
  }

  Future<bool> checkUnlockConditions() async {
    if (!awaitUnlock.value) {
      final text = amountUnLockEditor.text;

      print("text: $text");
      if (text.isEmpty || text == '0') {

        print("go here");
        Get.snackbar(
          'Error',
          'alert_unlock_limit_0'.tr,
          snackPosition: SnackPosition.TOP,
            backgroundColor: const Color(0xFF8CF9F0)
        );
        return false;
      }

      final locked = double.tryParse(lockedBalance.value.replaceAll(',', '')) ?? 0;
      final input = double.tryParse(text.replaceAll(',', '')) ?? 0;

      if (locked <= input) {
        Get.snackbar(
          'Error',
          'alert_unlock_limit'.tr,
          snackPosition: SnackPosition.TOP,
            backgroundColor: const Color(0xFF8CF9F0)
        );
        return false;
      }

      if (statusBorrow.value) {
        // _showBorrowDialog(context);
        return true;
      } else {
        // Proceed with unlock logic
        isUnlock.value = true;
        // amountUnlock.value = text;
        unlockAll.value = false;
      }
    }
    update();
    return false;
  }

  Future<void> handleUnlockProcess() async {
    try{
      isLoading.value = true;

      // var mnemonic = Storage.get(StorageKeys.mnemonic);
      var privateKey = Storage.get(StorageKeys.privateKey);

      final resultClaim = await checkRewards();

      if (resultClaim) {
        isLoading.value = false;
        // await GetRewardsPopup(context);
      } else {
        final data = await _requestUnlock(privateKey, amountUnLockEditor.text);

        success.value = true;

        Future.delayed(const Duration(milliseconds: 1000), () async {
          onTapUnlock.value = false;
          success.value = false;
          awaitUnlock.value = false;
          amountUnLockEditor.text = '';
        });

        final walletCtrl = Get.find<WalletDataController>();

        print('unlock');
        // getBalanceLock();
        var phone = Storage.get(StorageKeys.phoneNumber);
        walletCtrl.getDataWallet(phone);
        print('กำลังทำงานใหม่');
      }
    }catch(e) {
      print('Error in handleUnlockProcess: $e');
    }
  }

  Future<bool> _requestUnlock(getPK, amount, {String? onTapUnlockAll}) async {
    final unlockAll = (onTapUnlockAll == null || onTapUnlockAll.isEmpty)
        ? 'no'
        : onTapUnlockAll;

    String transaction = await web3Ctrl.requestUnlock(
        pk: getPK, value: amount.replaceAll(',', ''), all: unlockAll);
    print('withdraw tx : $transaction');
    await new Future.delayed(new Duration(seconds: 5));

    // Refresh data after unlocking
    isLoading.value = false;

    initializeData();
    return true;
  }

  // Handle confirm action (similar to your old code pattern)
  Future<void> handleConfirmAction() async {
    try {
      isLoading.value = true;

      final resultClaim = await checkRewards();

      if (resultClaim) {
        isLoading.value = false;
        // Show rewards popup - you'll need to implement GetRewardsPopup
        // await GetRewardsPopup(context);
        Get.snackbar(
          'Rewards Available',
          'You have rewards to claim first!',
          snackPosition: SnackPosition.TOP,
            backgroundColor: const Color(0xFF8CF9F0)
        );
      } else {
        // Proceed with unlock
        var privateKey = Storage.get(StorageKeys.privateKey);
        final sendAmountUnLock = amountUnLockEditor.text;
        final onTapUnlockAllValue = unlockAll.value ? 'yes' : 'no';

        await _requestUnlock(privateKey, sendAmountUnLock, onTapUnlockAll: onTapUnlockAllValue)
            .then((data) {
          success.value = true;

          Future.delayed(const Duration(milliseconds: 1000), () async {
            onTapUnlock.value = false;
            success.value = false;
            awaitUnlock.value = false;
            amountUnLockEditor.clear();
          });

          print('unlock completed');
        });

        print('กำลังทำงานใหม่');
        // Refresh balance data
        loadBalanceData();
      }
    } catch (e) {
      print('Error in handleConfirmAction: $e');
      Get.snackbar(
        'Error',
        'Failed to process unlock: $e',
        snackPosition: SnackPosition.TOP,
            backgroundColor: const Color(0xFF8CF9F0)
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Method to handle the main unlock button tap
  Future<void> handleUnlockButtonTap() async {
    final checkShow = await checkUnlockConditions();
    if (checkShow) {
      // Show confirmation dialog or proceed with unlock
      await handleConfirmAction();
    }
  }

}