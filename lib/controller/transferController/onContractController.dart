import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:get/get.dart';
import 'package:likewallet/model/contacts/contactsModel.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

abstract class OnContact {
  Future<bool> permissionContact();
  Future<List<Contacts>> getContacts({BuildContext? context});
}

// GetX Controller implementing OnContact
class ContactController extends GetxController implements OnContact {
  // Reactive list for contacts
  List<Contacts> contacts = <Contacts>[];

  @override
  void onInit() {
    super.onInit();
    // Initialize with mock contacts
    // contacts.assignAll(mockContacts);
  }

  @override
  Future<bool> permissionContact() async {
    final PermissionStatus permission = await Permission.contacts.status;
    if (permission != PermissionStatus.granted) {
      final Map<Permission, PermissionStatus> permissionStatus =
      await [Permission.contacts].request();
      if (permissionStatus[Permission.contacts] == PermissionStatus.granted) {
        return true;
      } else {
        // Handle permission denied case
        print('Permission denied');
        return false;
      }

    } else {
      return true;
    }
  }

  @override
  Future<List<Contacts>> getContacts({BuildContext? context}) async {
    List<String> phoneContact = [];
    List<Contacts> getContactMobile = [];

    // Check permission first
    bool hasPermission = await permissionContact();

    if (hasPermission) {
      // Get contacts from device
      final deviceContacts = await FlutterContacts.getContacts(withProperties: true);

      // Process each contact's phone numbers
      for (var contact in deviceContacts) {
        for (var phone in contact.phones) {
          if (phone.number.isNotEmpty && phone.number.toString().startsWith('0')) {
            final p = phone.number.toString();
            // Add different country code variants
            phoneContact.add(p
                .replaceFirst('0', '+66')
                .replaceAll(' ', '')
                .replaceAll('-', ''));
            phoneContact.add(p
                .replaceFirst('0', '+885')
                .replaceAll(' ', '')
                .replaceAll('-', ''));
            phoneContact.add(p
                .replaceFirst('0', '+888')
                .replaceAll(' ', '')
                .replaceAll('-', ''));
          }
        }
      }

      // Query Firestore for matching phone numbers
      for (var phoneNumber in phoneContact) {
        try {
          QuerySnapshot<Map<String, dynamic>> queryResult = await FirebaseFirestore.instance
              .collection('addressDNS')
              .where('phoneNumber', isEqualTo: phoneNumber)
              .get();

          for (var doc in queryResult.docs) {
            getContactMobile.add(Contacts.fromJson(doc.data()));
          }
        } catch (e) {
          print('Error querying Firestore: $e');
        }
      }

      print('Found ${getContactMobile.length} contacts in Firestore');
    } else if (context != null) {
      // Show permission dialog if context is provided
      showDialog(
          context: context,
          builder: (BuildContext context) => CupertinoAlertDialog(
            title: Text('Permissions error'),
            content: Text('Please enable contacts access '
                'permission in system settings'),
            actions: <Widget>[
              CupertinoDialogAction(
                child: Text('OK'),
                onPressed: () => Navigator.of(context).pop(),
              )
            ],
          ));
    }

    return getContactMobile;
  }
}