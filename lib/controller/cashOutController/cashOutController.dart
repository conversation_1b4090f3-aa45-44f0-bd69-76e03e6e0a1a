import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/service/httpRequest.dart';
import 'package:likewallet/view/transferPoint/cashOut/choiceTopay.dart';

class CashOutController extends GetxController {
  // Text controllers
  final TextEditingController amountController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController bahtController = TextEditingController();
  final TextEditingController btcController = TextEditingController();
  final TextEditingController amountGoldController = TextEditingController();

  // UI state variables
  RxBool assetSelect = false.obs;
  RxBool currencySelect = false.obs;
  RxInt selectCurrency = 0.obs;
  RxInt selectPageCashOut = 0.obs;
  RxInt selectGoldWeight = 0.obs;
  RxBool isLoading = false.obs;

  // Transaction variables
  RxString symbol = "THB".obs;
  RxDouble rate = 100.0.obs;
  RxDouble rateCurrency = 0.0.obs;
  RxDouble amount = 0.0.obs;
  RxString accountNumber = "".obs;
  RxDouble totalSell = 0.0.obs;
  RxDouble fee = 0.0.obs;
  RxDouble btc = 0.0.obs;

  final NumberFormat btcFormat = NumberFormat("###,###.####");


  // Currency options
  final List<String> currencies = ['THB', 'USD', 'LAK'];
  final List<String> currencyNames = ['Thai Baht', 'US Dollar', 'Lao Kip'];

  // Gold weight options in grams
  final List<String> goldWeights = ['1.00', '3.811', '7.622', '11.433', '15.244'];

  // Dio for API calls
  final Dio dio = Dio();

  @override
  // void onInit() {
  //   super.onInit();
  //
  //   // Initialize with default values
  //   amountGoldController.text = "1.00";
  //
  //   // Add listeners to text controllers
  //   amountController.addListener(updateAmount);
  //   bahtController.addListener(updateBaht);
  //
  //   // Load initial data
  //   loadExchangeRates();
  //   loadFees();
  //
  //   isLoading.value = false;
  // }
  @override
  void onInit() {
    super.onInit();

    amountGoldController.text = "1.00";

    amountController.addListener(updateAmount);
    bahtController.addListener(updateBaht);

    loadInitialData(); // ✅ โหลดข้อมูลรวมและจัดการ isLoading ให้อยู่ที่เดียว
  }


  @override
  void onClose() {
    // Dispose controllers
    amountController.dispose();
    addressController.dispose();
    bahtController.dispose();
    amountGoldController.dispose();
    isLoading.value =  false; // Reset loading state
    super.onClose();
  }

  // Update amount when LIKE amount changes
  void updateAmount() {
    if (amountController.text.isNotEmpty) {
      try {
        print("rate: ${rate.value}");
        double likeAmount = double.parse(amountController.text.replaceAll(',', ''));
        // Convert LIKE to selected currency
        double currencyAmount = likeAmount / rate.value;
        bahtController.text = currencyAmount.toStringAsFixed(2);
        amount.value = likeAmount;
        update();
      } catch (e) {
        print('Error parsing amount: $e');
        bahtController.text = '';
        amount.value = 0.0;
        update();
      }
    } else {
      bahtController.text = '';
      amount.value = 0.0;
      update();
    }
  }

  // Update LIKE amount when currency amount changes
  void updateBaht() {
    // This is intentionally left empty as we handle this in the UI
    update();
  }

  void updateBTC() {
    double parsed = double.tryParse(btcController.text.replaceAll(',', '')) ?? 0.0;

    double updated = parsed - 0.0010;
    if (updated < 0) updated = 0.0;

    btc.value = updated; // btc เป็น RxDouble

    update();
  }
  Future<void> loadExchangeRates() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('exchangeFiat')
          .get();

      for (var doc in snapshot.docs) {
        Map<String, dynamic> data = doc.data();
        if (data['to'] == currencies[selectCurrency.value]) {
          rateCurrency.value = double.parse(data['rate'].toString());
          break;
        }
      }
      print('[loadExchangeRates] rateCurrency: ${rateCurrency.value}');
    } catch (e) {
      print('Error loading exchange rates: $e');
      rateCurrency.value = 0.0; // fallback
    }
  }

  Future<void> loadFees() async {
    try {
      final response = await AppApi.get(
        '${AppEnv.apiUrl}/getCurrentFeeNew?currency=${currencies[selectCurrency.value]}',
      );

      if (response != null && response['status'] == 200) {
        final feeFromApi = response['fee'];
        if (feeFromApi != null) {
          fee.value = double.parse(feeFromApi.toString());
          print('[loadFees] fee loaded: ${fee.value}');
        } else {
          print('[loadFees] feeFromApi is null');
          fee.value = 0.0; // fallback
        }
      } else {
        print('[loadFees] response null or status != 200');
        fee.value = 0.0; // fallback
      }
    } catch (e) {
      print('Error loading fees: $e');
      fee.value = 0.0; // fallback on error
    }
  }

  Future<void> loadInitialData() async {
    print('[loadInitialData] start');
    try {
      isLoading.value = true;
      await Future.wait([
        loadExchangeRates().then((_) => print('[loadInitialData] loadExchangeRates done')),
        loadFees().then((_) => print('[loadInitialData] loadFees done')),
      ]);
    } catch (e) {
      print("Error loading initial data: $e");
    } finally {
      isLoading.value = false;
      update();
      print('[loadInitialData] finished, isLoading=${isLoading.value}');
    }
  }

  // Future<void> loadInitialData() async {
  //   print('[loadInitialData] start');
  //   try {
  //     isLoading.value = true;
  //     await Future.wait([
  //       loadExchangeRates().then((_) => print('[loadInitialData] loadExchangeRates done')),
  //       loadFees().then((_) => print('[loadInitialData] loadFees done')),
  //     ]);
  //   } catch (e) {
  //     print("Error loading initial data: $e");
  //   } finally {
  //     isLoading.value = false;
  //     print('[loadInitialData] finished');
  //   }
  // }



  // Load exchange rates from Firestore
  // Future<void> loadExchangeRates() async {
  //   try {
  //     isLoading.value = true;
  //     print("loadExchangeRates");
  //
  //     final snapshot = await FirebaseFirestore.instance
  //         .collection('exchangeFiat')
  //         .get();
  //
  //     for (var doc in snapshot.docs) {
  //       Map<String, dynamic> data = doc.data();
  //       print("data: $data");
  //       print(data['to']);
  //       if (data['to'] == currencies[selectCurrency.value]) {
  //         // rate.value = double.parse(data['rate'].toString());
  //         rateCurrency.value = double.parse(data['rate'].toString());
  //         break;
  //       }
  //     }
  //
  //     isLoading.value = false;
  //   } catch (e) {
  //     print('Error loading exchange rates: $e');
  //     isLoading.value = false;
  //   }
  // }
  // Future<void> loadExchangeRates() async {
  //   try {
  //     final snapshot = await FirebaseFirestore.instance
  //         .collection('exchangeFiat')
  //         .get();
  //
  //     for (var doc in snapshot.docs) {
  //       Map<String, dynamic> data = doc.data();
  //       if (data['to'] == currencies[selectCurrency.value]) {
  //         rateCurrency.value = double.parse(data['rate'].toString());
  //         break;
  //       }
  //     }
  //   } catch (e) {
  //     print('Error loading exchange rates: $e');
  //   }
  // }


  // Load fees from API
  // Future<void> loadFees() async {
  //   try {
  //     isLoading.value = true;
  //
  //     final response = await AppApi.get(
  //       '${AppEnv.apiUrl}/getCurrentFeeNew?currency=${currencies[selectCurrency.value]}',
  //     );
  //
  //     if (response != null && response['status'] == 200) {
  //       final feeFromApi = response['fee'];
  //       if (feeFromApi != null) {
  //         fee.value = double.parse(feeFromApi.toString());
  //       }
  //     }
  //
  //     isLoading.value = false;
  //   } catch (e) {
  //     print('Error loading fees: $e');
  //     isLoading.value = false;
  //   }
  // }
  // Future<void> loadFees() async {
  //   try {
  //     final response = await AppApi.get(
  //       '${AppEnv.apiUrl}/getCurrentFeeNew?currency=${currencies[selectCurrency.value]}',
  //     );
  //
  //     if (response != null && response['status'] == 200) {
  //       final feeFromApi = response['fee'];
  //       if (feeFromApi != null) {
  //         fee.value = double.parse(feeFromApi.toString());
  //       }
  //     }
  //   } catch (e) {
  //     print('Error loading fees: $e');
  //   }
  // }


  // Change selected currency
  void changeCurrency(int index) {
    selectCurrency.value = index;
    symbol.value = currencies[index];
    loadExchangeRates();
    loadFees();
    update();
  }

  // Change selected asset type
  void changeAssetType(int index) {
    selectPageCashOut.value = index;
    assetSelect.value = false;
    update();
  }

  // Change selected gold weight
  void changeGoldWeight(int index) {
    selectGoldWeight.value = index;
    amountGoldController.text = goldWeights[index];
    update();
  }

  // Toggle asset selection dropdown
  void toggleAssetSelect() {
    assetSelect.value = !assetSelect.value;
    update();
  }

  // Toggle currency selection dropdown
  void toggleCurrencySelect() {
    currencySelect.value = !currencySelect.value;
    update();
  }

  // Proceed to next step based on selected asset type
  Future<void> proceedToNextStep(BuildContext context) async {
    if (selectPageCashOut.value == 0) {
      // LIKE to currency
      if (amountController.text.isEmpty) {
        Get.snackbar(
          'Error',
          'Please enter an amount',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      double likeAmount = double.parse(amountController.text.replaceAll(',', ''));

      // Check minimum withdrawal amount
      if ((symbol.value == 'USD' && amount.value < 1) ||
          (symbol.value == 'THB' && amount.value < 10) ||
          (symbol.value == 'LAK' && amount.value < 10)) {
        Get.snackbar(
          'Error',
          'Minimum withdrawal amount not met',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // Calculate total sell amount
      totalSell.value = likeAmount;

      // Navigate to payment method selection
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChoiceTopay(
            amount: amount.value,
            fee: fee.value,
            rate: rate.value,
            symbol: symbol.value,
            totalSell: totalSell.value,
          ),
        ),
      );
    }
  }
}
