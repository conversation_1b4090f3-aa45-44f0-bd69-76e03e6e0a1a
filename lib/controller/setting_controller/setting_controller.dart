import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'dart:io' as io;

import 'package:permission_handler/permission_handler.dart';

class SettingController extends GetxController {
  RxString os = "".obs;
  RxString currentVersion = "".obs;
  RxString versionInStore = "".obs;
  RxString updateUrl = "".obs;
  RxBool closeHead = false.obs;

  @override
  void onInit() async {
    super.onInit();
    await Permission.notification.request();
    await Permission.camera.request();
    await getOS();
    // await getVersion();
  }

  Future<dynamic> getOS() async {
    try {
      os.value = io.Platform.operatingSystem;
      update();
    }catch(e){
      if (kDebugMode) {
        print("error getFeelWell =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }


  // getVersion(context) async {
  //   try {
  //     print("getVersion");
  //     var version = await FireStore.getCurrentVersion();
  //     final info = await PackageInfo.fromPlatform();
  //     currentVersion.value = info.version;
  //
  //     if(os.value == "android"){
  //       Version current = Version.parse(currentVersion.value);
  //       Version newV = Version.parse(version["android"]);
  //       versionInStore.value = version["android"];
  //
  //       if (current < newV) {
  //
  //         updateUrl.value = "https://play.google.com/store/apps/details?id=com.prachakij.pms_app&hl=th&gl=US";
  //         // pop up Update Version
  //         AppWidget.showDialogPageSlide(context, const AlertUpdateVersionPage());
  //       }
  //     }else{
  //       Version current = Version.parse(currentVersion.value);
  //       Version newV = Version.parse(version["ios"]);
  //       versionInStore.value = version["ios"];
  //       if (current < newV) {
  //
  //         updateUrl.value = "https://apps.apple.com/th/app/prachakij/id1507877291";
  //         // pop up Update Version
  //         AppWidget.showDialogPageSlide(context, const AlertUpdateVersionPage());
  //       }
  //     }
  //     update();
  //   } catch (e) {
  //     AppService.sendError(e, 'ERROR : getVersion SettingController');
  //   }
  // }

  changeCloseHead(input) {
    closeHead.value = input;
    update();
  }
}