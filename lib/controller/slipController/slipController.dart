import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:get/get.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';

class SlipController extends GetxController {
  // Global key for capturing the slip widget
  final GlobalKey slipKey = GlobalKey();

  // Loading state
  RxBool isLoading = false.obs;

  // Transaction details
  RxString fromAddress = ''.obs;
  RxString toAddress = ''.obs;
  RxString fromName = ''.obs;
  RxString toName = ''.obs;
  RxString amount = '0'.obs;
  RxString fee = '0 LIKE'.obs;
  RxString note = ''.obs;
  RxString txHash = ''.obs;
  RxString currency = 'LIKE'.obs;
  RxDouble rateCurrency = 0.0.obs;
  RxDouble rate = 0.0.obs;
  RxBool isVending = false.obs;

  // Date and time formatting
  RxString date = ''.obs;
  RxString time = ''.obs;
  RxString month = ''.obs;
  RxString year = ''.obs;

  // Number formatter for currency display
  final NumberFormat formatter = NumberFormat("#,##0.00", "en_US");

  // // Auto-save preference
  // RxBool autoSaveSlip = true.obs;

  @override
  void onInit() {
    super.onInit();
    setCurrentDateTime();
  }

  // Set current date and time
  void setCurrentDateTime() {
    DateTime now = DateTime.now();
    String formattedDate = DateFormat('dd-MM-yyyy – kk:mm').format(now);

    // Extract date components
    String day = formattedDate.substring(0, 2);
    String monthNum = formattedDate.substring(3, 5);
    String yearStr = formattedDate.substring(6, 10);

    // Month names
    List<String> monthNames = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec',
    ];

    // Set values
    date.value = day;
    month.value = monthNames[int.parse(monthNum) - 1];
    year.value = yearStr;
    time.value = DateFormat('hh:mm a').format(now);
  }

  // Set transaction data
  void setTransactionData({
    required String from,
    required String to,
    required String fromNameStr,
    required String toNameStr,
    required String amountStr,
    required String feeStr,
    required String noteStr,
    required String txHashStr,
    required String currencyStr,
    required double rateCurrencyValue,
    required double rateValue,
    required bool isVendingValue,
  }) {
    fromAddress.value = from;
    toAddress.value = to;
    fromName.value = fromNameStr;
    toName.value = toNameStr;
    amount.value = amountStr;
    fee.value = feeStr;
    note.value = noteStr;
    txHash.value = txHashStr;
    currency.value = currencyStr;
    rateCurrency.value = rateCurrencyValue;
    rate.value = rateValue;
    isVending.value = isVendingValue;
  }

  // Capture slip as image and save to gallery
  Future<Uint8List> captureSlip() async {
    isLoading.value = true;

    try {
      // Check and request permissions
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        final androidVersion = int.parse(androidInfo.version.sdkInt.toString());
        if (androidVersion >= 33) {
          // Android 13+ (ใช้ Media permission)
          final imagePerm = await Permission.photos.request();
          final videoPerm = await Permission.videos.request();

          if (!imagePerm.isGranted && !videoPerm.isGranted) {
            throw Exception("Permission denied");
          }
        } else {
          // Android < 13 (ใช้ storage permission)
          final storagePerm = await Permission.storage.request();
          if (!storagePerm.isGranted) {
            throw Exception("Storage permission denied");
          }
        }
      } else if (Platform.isIOS) {
        final photosPermission = await Permission.photosAddOnly.request();
        if (!photosPermission.isGranted) {
          throw Exception("Photos permission denied");
        }
      }

      // Capture the widget as image
      final boundary = slipKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) throw Exception("Cannot find render object");

      final image = await boundary.toImage(pixelRatio: 3.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) throw Exception("Failed to convert image to bytes");

      final pngBytes = byteData.buffer.asUint8List();

      // Save to gallery
      final result = await ImageGallerySaver.saveImage(
        pngBytes,
        quality: 100,
        name: "slip_${DateTime.now().millisecondsSinceEpoch}",
      );

      if (result['isSuccess'] != true) {
        throw Exception("Failed to save image: $result");
      }

      print('Slip saved to gallery: $result');

      return pngBytes;
    } catch (e) {
      print('Error capturing slip: $e');
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }

  // Share slip as image
  Future<void> shareSlip() async {
    isLoading.value = true;

    try {
      // Capture the widget as an image
      RenderRepaintBoundary boundary = slipKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List pngBytes = byteData!.buffer.asUint8List();

      // Save to temporary file for sharing
      final tempDir = await getTemporaryDirectory();
      final file = await File('${tempDir.path}/transaction_slip.png').create();
      await file.writeAsBytes(pngBytes);

      // Share the file
      await Share.shareFiles(
        ['${tempDir.path}/transaction_slip.png'],
        subject: 'Transaction Receipt',
        text: 'My LIKE Wallet transaction receipt',
      );

      isLoading.value = false;
    } catch (e) {
      print('Error sharing slip: $e');
      isLoading.value = false;
    }
  }

  // Save transaction to history in Firestore
  Future<void> saveTransactionToHistory() async {
    try {
      final auth = FirebaseAuth.instance;
      String? currentUser = auth.currentUser?.phoneNumber;

      if (currentUser == null || currentUser.isEmpty) return;

      await FirebaseFirestore.instance.collection('transactions').add({
        'txHash': txHash.value,
        'sender': fromAddress.value,
        'senderName': fromName.value,
        'recipient': toAddress.value,
        'recipientName': toName.value,
        'amount': double.tryParse(amount.value) ?? 0,
        'currency': currency.value,
        'fee': fee.value,
        'timestamp': FieldValue.serverTimestamp(),
        'status': 'completed',
        'type': isVending.value ? 'vending' : 'transfer',
        'message': note.value,
      });
    } catch (e) {
      print('Error saving transaction history: $e');
      // Non-critical error, don't affect the main flow
    }
  }
}
