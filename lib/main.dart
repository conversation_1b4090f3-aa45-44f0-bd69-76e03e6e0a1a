
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:global_configuration/global_configuration.dart';
import 'package:likewallet/controller/setting_controller/lazyPutController.dart';
import 'package:likewallet/controller/setting_controller/setting_controller.dart';
import 'package:likewallet/controller/translate/translate_controller.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/notificationService.dart';
import 'package:likewallet/view/login/index.dart';

// Background message handler must be a top-level function
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Ensure Firebase is initialized
  // await Firebase.initializeApp();

  if (kDebugMode) {
    print('Handling a background message: ${message.messageId}');
    print('Background message data: ${message.data}');
    print('Background message notification: ${message.notification?.title}');
  }

  // You can perform background tasks here, but keep them lightweight
  // For example, you could store the notification in a local database

  // Note: You cannot directly interact with the UI from here
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();

  // Initialize Firebase
  await Firebase.initializeApp();

  // Set the background message handler
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // Request notification permissions for iOS
  if (Platform.isIOS) {
    await FirebaseMessaging.instance.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );
  }

  // Load configuration
  try {
    await GlobalConfiguration()
        .loadFromUrl("https://new.likepoint.io/configAPInew");
  } catch (e) {
    print('Error loading configuration: $e');
    // Load local configuration as fallback
    try {
      await GlobalConfiguration().loadFromAsset("assets/cfg/assets/config.json");
      print('Loaded configuration from local asset');
    } catch (e) {
      print('Error loading local configuration: $e');
      print('The asset does not exist or has empty data.');

      // Try one more location as a last resort
      try {
        await GlobalConfiguration().loadFromAsset("assets/config.json");
        print('Loaded configuration from assets/config.json');
      } catch (e2) {
        print('Error loading from assets/config.json: $e2');
      }
    }
  }

  // Initialize controllers
  await AppBindings.lazyLoadControllers();

  // Run the app
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {

    Get.put(SettingController(), permanent: true);

    return ScreenUtilInit(
      designSize: const Size(402, 874),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context , child) {
        return GetMaterialApp(
          builder: EasyLoading.init(),
          debugShowCheckedModeBanner: false,
          title: 'Likewallet',
          theme: ThemeData(
            fontFamily: 'Proxima Nova',
            scaffoldBackgroundColor: Colors.white70,
            primaryColor: const Color(0xFFFF8500),
            colorScheme:
            ColorScheme.fromSwatch().copyWith(secondary: Colors.amber),
          ),
          supportedLocales: const [
            Locale('en'),
            Locale('th'),
            Locale('lo'),
            Locale('km'),
          ],
          locale: const Locale('en'),
          fallbackLocale: const Locale('en'),
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          translations: TranslationsService(),
          home: const HomePage(),
        );
      },
    );
  }
}

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusNode().unfocus();
      },
      child: Stack(
        children: [
          const IndexLike(),
          Notify(),
        ],
      ),
    );
  }
}

class Notify extends StatefulWidget {
  const Notify({Key? key}) : super(key: key);

  @override
  _NotifyState createState() => _NotifyState();
}

class _NotifyState extends State<Notify> {
  RxString _homeScreenText = "Waiting for token...".obs;
  RxDouble heightAnimate = 0.0.obs;

  @override
  void initState() {
    super.initState();
    _initializeNotifications();
  }

  Future<void> _initializeNotifications() async {
    final fcm = FirebaseMessaging.instance;

    // Request permissions (iOS)
    // if (Platform.isIOS) {
    //   NotificationSettings settings = await fcm.requestPermission(
    //     alert: true,
    //     badge: true,
    //     sound: true,
    //   );
    //   print('User granted permission: ${settings.authorizationStatus}');
    // }

    // Subscribe to topics
    await subscribeToTopicLikePoint(fcm);
    await subscribeToNotifyAllTopic(fcm);
    // if (Platform.isIOS) {
    //   await getAPNToken(fcm);
    // }

    // Foreground display config
    await fcm.setForegroundNotificationPresentationOptions(
      sound: true,
      badge: true,
      alert: true,
    );

    // Get and save token
    String? token = await fcm.getToken();
    print('FCM token: $token');
    if (token != null && token.isNotEmpty) {
      await setTokenNotify(token);
      // setState(() {
        _homeScreenText.value = "Push Messaging token: $token";
        print(_homeScreenText.value);
      // });
    }

    // Listen for token refresh
    fcm.onTokenRefresh.listen((newToken) {
      setTokenNotify(newToken);
      print('FCM token refreshed: $newToken');
    });

    // Message listeners
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundNotificationTap);
  }

  // Future<void> getAPNToken(FirebaseMessaging fcm) async {
  //   if (Platform.isIOS) {
  //     String? apnsToken = await fcm.getAPNSToken();
  //     if (apnsToken != null) {
  //       await fcm.subscribeToTopic('K4QBZ5ZDCH');
  //     } else {
  //       await Future<void>.delayed(const Duration(seconds: 3));
  //       apnsToken = await fcm.getAPNSToken();
  //       if (apnsToken != null) {
  //         await fcm.subscribeToTopic('K4QBZ5ZDCH');
  //       }
  //     }
  //   } else {
  //     await fcm.subscribeToTopic('K4QBZ5ZDCH');
  //   }
  // }

  Future<void> subscribeToTopicLikePoint(FirebaseMessaging fcm) async {
    await fcm.subscribeToTopic('LIKE_POINT_2_0');
    print('Subscribed to LIKE_POINT_2_0');
  }

  Future<void> subscribeToNotifyAllTopic(FirebaseMessaging fcm) async {
    try {
      await fcm.subscribeToTopic('dev'); // => ตรงนี้ต้องปิดนะ ห้ามเปิด
      await fcm.subscribeToTopic('notifyAll');
      await fcm.subscribeToTopic('ads');
      await fcm.subscribeToTopic('notifyTier1');
      await fcm.subscribeToTopic('notifyNormal');
      print('Subscribed to notifyAll');
    } catch (e) {
      print('Failed to subscribe to notifyAll: $e');
    }
  }

  Future<void> setTokenNotify(String tokenNotify) async {
    await Storage.save(StorageKeys.tokenNotify, tokenNotify);
    print('Notification token saved: $tokenNotify');
  }

  String? getTokenNotify() {
    return Storage.get<String>(StorageKeys.tokenNotify);
  }

  void _handleForegroundMessage(RemoteMessage remoteMessage) async {
    print('Got a message whilst in the foreground!');
    print('Message data: ${remoteMessage.data}');

    if (remoteMessage.notification != null) {
      print('Message also contained a notification: ${remoteMessage.notification}');

      Get.snackbar(
        remoteMessage.notification!.title ?? 'Notification',
        remoteMessage.notification!.body ?? 'You have a new notification',
        snackPosition: SnackPosition.TOP,
        snackStyle: SnackStyle.FLOATING,
        duration: const Duration(seconds: 7),
        padding: const EdgeInsets.only(top: 10, left: 6, right: 6, bottom: 10),
        icon: Container(
          width: 50,
          height: 50,
          alignment: Alignment.topLeft,
          margin: const EdgeInsets.only(left: 10, right: 6),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(5),
            child: Image.asset("assets/icon/icon.png"),
          ),
        ),
        mainButton: remoteMessage.data['type']?.toString() != '1'
            ? TextButton(
          onPressed: () async {
            Get.back();
            _handleNotificationAction(remoteMessage);
          },
          child: Text(
            'รายละเอียด',
            style: TextStyle(color: Colors.black, fontSize: 12),
          ),
        )
            : null,
      );
    }
  }

  void _handleBackgroundNotificationTap(RemoteMessage remoteMessage) {
    print('Notification tapped in background: ${remoteMessage.data}');
    _handleNotificationAction(remoteMessage);
  }

  void _handleNotificationAction(RemoteMessage remoteMessage) {
    String notificationType = remoteMessage.data['type']?.toString() ?? '';

    switch (notificationType) {
      case '2':
      // Navigate to transaction history
      // Get.to(() => TransactionHistoryPage());
        break;
      case '3':
      // Navigate to profile page
      // Get.to(() => ProfilePage());
        break;
      default:
      // Default case
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(); // No UI required
  }
}

