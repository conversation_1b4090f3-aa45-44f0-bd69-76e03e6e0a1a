class Logo {
  String? logo;
  int? running;
  String? title;
  String? details;
  String? shopGroup;
  String? address;
  String? contract;
  String? abi;
  String? callFunction;

  Logo({
    this.logo,
    this.running,
    this.title,
    this.details,
    this.shopGroup,
    this.address,
    this.contract,
    this.abi,
    this.callFunction,
  });

  factory Logo.fromJson(Map<String, dynamic> json) {
    return Logo(
      logo: json["logo"] as String?,
      running: json["running"] as int?,
      title: json["title"] as String?,
      details: json["details"] as String?,
      shopGroup: json["groupShop"] as String?,
      address: json["address"] as String?,
      contract: json["contract"] as String?,
      abi: json["abi"] as String?,
      callFunction: json["callFunction"] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "logo": logo,
      "running": running,
      "title": title,
      "details": details,
      "groupShop": shopGroup,
      "address": address,
      "contract": contract,
      "abi": abi,
      "callFunction": callFunction,
    };
  }
}
