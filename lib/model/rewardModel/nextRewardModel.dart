class NextRewards{
  String round_time;
  String rewards;
  String total_locked;
  String round;
  String created_time;

  NextRewards({
    required this.round_time,
    required this.rewards,
    required this.total_locked,
    required this.round,
    required this.created_time,

  });
  NextRewards.fromJson(Map<String, dynamic> json)
      : round_time =  json['round_time'] as String,
        rewards = json['rewards'] as String,
        total_locked =  json['total_locked'] as String,
        round =  json ['round'] as String,
        created_time =  json ['created_time'] as String;

  Map<String, dynamic> toJson() =>
      {
        round_time: round_time,
        rewards : rewards,
        total_locked: total_locked,
        round : round,
        created_time : created_time,
      };
}
