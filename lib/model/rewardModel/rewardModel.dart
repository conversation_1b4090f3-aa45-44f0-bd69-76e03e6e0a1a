class ButtonStatus {
  bool claim;
  bool claimLock;
  bool lock;
  bool unlock;

  ButtonStatus(
      this.claim,
      this.claimLock,
      this.lock,
      this.unlock,
      );

  ButtonStatus.fromJson(Map<String, dynamic> json)
      : claim = json['claim'],
        claimLock = json['claimLock'],
        lock = json['lock'],
        unlock = json['unlock'];

  Map<String, dynamic> toJson() => {
    'claim': claim,
    'claimLock': claimLock,
    'lock': lock,
    'unlock': unlock,
  };
}
