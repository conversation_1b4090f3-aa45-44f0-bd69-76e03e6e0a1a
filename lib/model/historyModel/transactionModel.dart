class TransactionModel{
  int? updateTime;
  String? uid;
  String? paymentMethod;
  String? bankName;
  String? tx;
  String? accountNumber;
  String? fee;
  String? baht;
  String? phoneNumber;
  String? status;
  String? slip;
  String? type;
  String? to;
  String? title;
  bool? isExpanded;
  String? message;
  String? currency;

  TransactionModel(
      {this.updateTime,
        this.uid,
        this.paymentMethod,
        this.bankName,
        this.tx,
        this.accountNumber,
        this.fee,
        this.baht,
        this.slip,
        this.phoneNumber,
        this.status,
        this.title,
        this.type,
        this.to,
        this.isExpanded,
        this.message,this.currency});


  factory TransactionModel.fromJson(Map<String, dynamic> parsedJson) {
    return TransactionModel(
        updateTime: parsedJson['updateTime'] as int,
        uid: parsedJson['uid'] as String,
        paymentMethod: parsedJson['paymentMethod'] as String,
        bankName: parsedJson['bankName'] as String,
        tx: parsedJson['tx'] as String,
        accountNumber: parsedJson['accountNumber'] as String,
        fee: parsedJson['fee'] as String,
        baht: parsedJson['baht'] as String,
        phoneNumber: parsedJson['phoneNumber'] as String,
        status: parsedJson['status'] as String,
        type: parsedJson['type'] as String,
        to: parsedJson['to'] as String,
        title: parsedJson['title'] as String,
        slip: parsedJson['slip'],
        isExpanded: false,
        message: parsedJson['message'] as String,
        currency: parsedJson['currency'] as String
    );
  }
}
