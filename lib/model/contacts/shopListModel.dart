class ShopList {
//  int id;
  String? logo;
  int? running;
  String? title;
  String? details;
  String? shopGroup;
  String? address;
  String? contract;
  String? abi;
  String? callFunction;
  ShopList({this.logo, this.running, this.title, this.details, this.shopGroup, this.address, this.contract, this.abi, this.callFunction});

  factory ShopList.fromJson(Map<String, dynamic> json) {
    return ShopList(
      logo: json["logo"] as String,
      running: json["running"] as int,
      title: json["title"] as String,
      details: json["details"] as String,
      address:json["address"] as String,
      shopGroup: json["groupShop"] as String,
      contract: json["contract"] as String,
      abi: json["abi"] as String,
      callFunction: json["callFunction"] as String,
//      email: json["email"] as String,
    );
  }
}
