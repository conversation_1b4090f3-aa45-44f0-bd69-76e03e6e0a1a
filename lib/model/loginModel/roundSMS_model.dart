// class ProviderSMS {
//   final String value;
//
//   const ProviderSMS._(this.value);
//
//   static const Firebase = ProviderSMS._("Firebase");
//   static const Twilio = ProviderSMS._("Twilio");
//   static const Nexmo = ProviderSMS._("Nexmo");
//   static const Email = ProviderSMS._("Email");
//   static const MKT = ProviderSMS._("MKT");
//
//   static const List<ProviderSMS> values = [
//     Firebase,
//     Twilio,
//     Nexmo,
//     Email,
//     MKT,
//   ];
//
//   @override
//   String toString() => value;
//
//   // Optional: สำหรับแปลงจาก string เป็น object
//   static ProviderSMS fromString(String str) {
//     return values.firstWhere((e) => e.value == str, orElse: () => Firebase);
//   }
// }
