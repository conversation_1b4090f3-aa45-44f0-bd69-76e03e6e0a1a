class DataUser {
  final String mnemonic;
  final String address;
  final String token;

  DataUser({
    required this.mnemonic,
    required this.address,
    required this.token,
  });

  DataUser.fromJson(Map<String, dynamic> json)
      : mnemonic = json['mnemonic'],
        address = json['address'],
        token = json['token'];

  Map<String, dynamic> toJson() => {
        'mnemonic': mnemonic,
        'address': address,
        'token': token,
      };
}
