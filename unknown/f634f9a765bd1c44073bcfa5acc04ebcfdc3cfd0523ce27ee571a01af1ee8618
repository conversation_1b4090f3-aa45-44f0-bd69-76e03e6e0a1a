class CheckYourLotto {
  String number;
  String reward;
  int amountWin;

  CheckYourLotto(
    this.number,
    this.reward,
    this.amountWin,
  );

  CheckYourLotto.fromJson(Map<String, dynamic> json)
      : number = json['number'],
        reward = json['reward'],
        amountWin = json['amountWin'];

  Map<String, dynamic> toJson() => {
        'number': number,
        'reward': reward,
        'amountWin': amountWin,
      };
}
