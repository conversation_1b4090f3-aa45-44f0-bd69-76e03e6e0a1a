<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="120.246" height="53.464" viewBox="0 0 120.246 53.464">
  <defs>
    <filter id="X_-_LOCK" x="62.246" y="9.964" width="58" height="18" filterUnits="userSpaceOnUse">
      <feOffset dy="2" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1" result="blur"/>
      <feFlood flood-opacity="0.102"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#8e80ff"/>
      <stop offset="1" stop-color="#6d4aff"/>
    </linearGradient>
    <filter id="Path_59030" x="32.985" y="9.792" width="43.671" height="43.671" filterUnits="userSpaceOnUse">
      <feOffset dx="-3" dy="-6" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_35666" data-name="Group 35666" transform="translate(0 0)">
    <g id="Group_34845" data-name="Group 34845" transform="translate(22.015 1.685)">
      <g id="Group_34844" data-name="Group 34844" transform="translate(43.23 9.279)">
        <g transform="matrix(1, 0, 0, 1, -65.25, -10.96)" filter="url(#X_-_LOCK)">
          <text id="X_-_LOCK-2" data-name="X - LOCK" transform="translate(65.25 20.96)" fill="#738097" font-size="10" font-family="SFProText-Bold, SF Pro Text" font-weight="700" letter-spacing="0.08em"><tspan x="0" y="0">X - LOCK</tspan></text>
        </g>
      </g>
      <g id="Group_34886" data-name="Group 34886">
        <path id="Path_63718" data-name="Path 63718" d="M-3943.428,783.757a17.922,17.922,0,0,0-17.922,17.922,17.922,17.922,0,0,0,17.922,17.922,17.921,17.921,0,0,0,17.921-17.922A17.921,17.921,0,0,0-3943.428,783.757Zm8.572,22.955-2.559-3.832h-11.472l-2.438,3.832h-2.99l3.077-5.084-3.077-5.089,3.012-.011,2.4,3.851h11.5l2.6-3.824h2.979l-3.095,5.055,3.095,5.1Z" transform="translate(3961.349 -783.757)" fill="url(#linear-gradient)"/>
      </g>
      <g id="Group_34843" data-name="Group 34843" transform="translate(22.97 23.107)">
        <g id="Group_34142" data-name="Group 34142" transform="translate(0 0)" style="isolation: isolate">
          <g transform="matrix(1, 0, 0, 1, -44.98, -24.79)" filter="url(#Path_59030)" style="isolation: isolate">
            <path id="Path_59030-2" data-name="Path 59030" d="M380.108,192.489a12.836,12.836,0,1,1-12.836,12.836h0A12.836,12.836,0,0,1,380.108,192.489Z" transform="translate(-322.29 -167.7)" fill="#c184fd"/>
          </g>
        </g>
        <path id="Path_61358" data-name="Path 61358" d="M823.689,255.859h-.629v-1.776a2.813,2.813,0,1,0-5.626,0v1.777h-.63a1.285,1.285,0,0,0-1.284,1.283V262.5a1.285,1.285,0,0,0,1.284,1.283h6.884a1.285,1.285,0,0,0,1.283-1.283v-5.354A1.285,1.285,0,0,0,823.689,255.859Zm-5.218-1.777a1.776,1.776,0,1,1,3.553,0v1.776h-3.553Zm5.464,8.415a.247.247,0,0,1-.247.247H816.8a.247.247,0,0,1-.247-.247v-5.354a.247.247,0,0,1,.247-.247h6.884c.136,0,.247.247.247.247Z" transform="translate(-807.411 -244.956)"/>
      </g>
    </g>
    <g id="Group_34162" data-name="Group 34162" transform="translate(0 0)">
      <path id="Path_60686" data-name="Path 60686" d="M416.9,386.674l.157-2.978.185,2.98a1.047,1.047,0,0,0,.845,1.044l2.411.228-2.409.195a1.017,1.017,0,0,0-.835,1.032l-.158,2.978-.185-2.981a1.047,1.047,0,0,0-.844-1.044l-2.411-.228,2.409-.195A1.018,1.018,0,0,0,416.9,386.674Z" transform="translate(-352.047 -383.696)" fill="#8a96ab"/>
      <path id="Path_60687" data-name="Path 60687" d="M387.416,387.782l.257-4.863.3,4.867a1.71,1.71,0,0,0,1.379,1.705l3.936.373-3.933.318a1.662,1.662,0,0,0-1.364,1.686l-.257,4.863-.3-4.867a1.71,1.71,0,0,0-1.379-1.705l-3.936-.373,3.933-.318A1.661,1.661,0,0,0,387.416,387.782Z" transform="translate(-382.12 -358.77)" fill="#8a96ab"/>
      <path id="Path_60691" data-name="Path 60691" d="M454.865,519.282l.147-2.778.172,2.78a.977.977,0,0,0,.788.974l2.249.213-2.247.182a.949.949,0,0,0-.779.963l-.147,2.778-.172-2.78a.977.977,0,0,0-.788-.974l-2.248-.213,2.247-.182A.949.949,0,0,0,454.865,519.282Z" transform="translate(-430.71 -479.636)" fill="#8a96ab"/>
    </g>
  </g>
</svg>
