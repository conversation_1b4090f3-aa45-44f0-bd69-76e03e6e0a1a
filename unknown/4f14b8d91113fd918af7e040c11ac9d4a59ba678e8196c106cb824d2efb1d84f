import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lock_to_win/main.dart';

class LockScrenn extends StatefulWidget {
  const LockScrenn({Key? key}) : super(key: key);

  @override
  _LockScrennState createState() => _LockScrennState();
}

class _LockScrennState extends State<LockScrenn> {
  bool isButtonShow = true;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: isDarkMode ? Colors.black : Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            width: Get.width,
            height: Get.height,
            color: isDarkMode ? const Color(0xff11161E) : Colors.white,
            child: Column(
              children: [
                Container(
                  color: isDarkMode ? const Color(0xff1D2532) : Colors.white,
                  padding: const EdgeInsets.all(10),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: () {
                              Get.back();
                            },
                            child: const Icon(
                              Icons.arrow_back_ios,
                              size: 12,
                            ),
                          ),
                          Image.asset(
                            'assets/images/Group 35898.png',
                            height: 47,
                          ),
                          const SizedBox(),
                        ],
                      ),
                      SizedBox(
                        height: Get.height * 0.06,
                      ),
                      const Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Locked Likepoint',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xff738097),
                            ),
                          ),
                          Text(
                            'Available Likepoint',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xff738097),
                            ),
                          )
                        ],
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '754,048.09',
                            style: TextStyle(
                              fontSize: 16,
                              color: isDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          Text(
                            '754,048.09',
                            style: TextStyle(
                              fontSize: 16,
                              color: isDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Container(
                        width: Get.width,
                        margin:
                            const EdgeInsets.symmetric(horizontal: 20, vertical: 25),
                        height: 40,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(60),
                          color: const Color(0xff030406),
                        ),
                        child: isButtonShow == true
                            ? SizedBox(
                                width: Get.width,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    Expanded(
                                      child: Container(
                                        alignment: Alignment.center,
                                        height: 40,
                                        decoration: BoxDecoration(
                                          color: const Color(0xff0078FF),
                                          borderRadius:
                                              BorderRadius.circular(60),
                                        ),
                                        child: const Text('Unlock'),
                                      ),
                                    ),
                                    Expanded(
                                      child: InkWell(
                                        onTap: () {},
                                        child: InkWell(
                                          onTap: () {
                                            isButtonShow = false;
                                            setState(() {});
                                          },
                                          child: Container(
                                            child: const Text(
                                              'Lock More',
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              )
                            : SizedBox(
                                width: Get.width,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    Expanded(
                                      child: InkWell(
                                        onTap: () {
                                          isButtonShow = true;
                                          setState(() {});
                                        },
                                        child: Container(
                                          child: const Text(
                                            'Unlock',
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: Container(
                                        alignment: Alignment.center,
                                        height: 40,
                                        decoration: BoxDecoration(
                                          color: const Color(0xff0078FF),
                                          borderRadius:
                                              BorderRadius.circular(60),
                                        ),
                                        child: const Text('Lock More'),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                      ),
                      TextFormField(
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.center,
                        decoration: InputDecoration(
                          border: const OutlineInputBorder(
                            borderSide: BorderSide.none,
                          ),
                          isCollapsed: true,
                          hintText: '0 LIKE',
                          hintStyle: TextStyle(
                            fontSize: 34,
                            color: isDarkMode ? Colors.white : Colors.black,
                          ),
                        ),
                      ),
                      Text(
                        '฿0',
                        style: TextStyle(
                          color: isDarkMode ? const Color(0xff738097) : Colors.black,
                        ),
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Container(
                            alignment: Alignment.center,
                            height: 48,
                            width: 48,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(60),
                              color: const Color(0xff030406),
                            ),
                            child: const Text(
                              'ALL',
                              style: TextStyle(color: Color(0xff0078FF)),
                            ),
                          ),
                          Container(
                            alignment: Alignment.center,
                            height: 48,
                            width: 48,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(60),
                              color: const Color(0xff030406),
                            ),
                            child: Image.asset(
                              'assets/images/Reverse-arrow8-unscreen.png',
                              height: 38,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
