class LottoRound {
  List<String> winnerNumber;
  String totalPrev;
  String matchFour;
  String matchThree;
  String matchTwo;
  String totalReward4;
  String totalReward3;
  String totalReward2;

  LottoRound(this.winnerNumber, this.totalPrev, this.matchFour, this.matchThree,
      this.matchTwo, this.totalReward4, this.totalReward3, this.totalReward2);

  LottoRound.fromJson(Map<String, dynamic> json)
      : winnerNumber = json['winnerNumber'],
        totalPrev = json['totalPrev'],
        matchFour = json['matchFour'],
        matchThree = json['matchThree'],
        matchTwo = json['matchTwo'],
        totalReward4 = json['totalReward4'],
        totalReward3 = json['totalReward3'],
        totalReward2 = json['totalReward2'];

  Map<String, dynamic> toJson() => {
        'winnerNumber': winnerNumber,
        'totalPrev': totalPrev,
        'matchFour': matchFour,
        'matchThree': matchThree,
        'matchTwo': matchTwo,
        'totalReward4': totalReward4,
        'totalReward3': totalReward3,
        'totalReward2': totalReward2,
      };
}
