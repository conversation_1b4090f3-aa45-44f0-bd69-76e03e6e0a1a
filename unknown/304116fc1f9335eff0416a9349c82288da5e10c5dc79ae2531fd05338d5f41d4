import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lock_to_win/controller/lottery.dart';
import 'package:lock_to_win/screens/lock_to_win_screen.dart';

ThemeMode currentThemeMode = ThemeMode.dark;
bool isDarkMode = true;

// void main() async {
//   WidgetsFlutterBinding.ensureInitialized();
//
//   runApp(LockToWinMain());
// }

class LockToWinMain extends StatefulWidget {
  final dynamic data;
  const LockToWinMain({super.key, required this.data});

  @override
  State<LockToWinMain> createState() => _LockToWinMainState();
}

class _LockToWinMainState extends State<LockToWinMain> {

  @override
  void initState() {
    super.initState();

    // ✅ ใส่ Get.put() ที่นี่ เพื่อให้ controller พร้อมใช้งาน
    Get.put(LotteryController());
  }

  @override
  Widget build(BuildContext context) {
    return LockToWinScreen(data: widget.data);
  }
}



// class LockToWinMain extends StatefulWidget {
//   LockToWinMain(this.data);
//   final data;
//   @override
//   State<LockToWinMain> createState() => _LockToWinMainState();
// }
//
// class _LockToWinMainState extends State<LockToWinMain> {
//   refresh() {
//     setState(() {});
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return GetMaterialApp(
//       builder: (context, child) {
//         return MediaQuery(
//           data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
//           child: child!,
//         );
//       },
//       debugShowCheckedModeBanner: false,
//       title: 'Likewallet',
//       theme: ThemeData(
//         fontFamily: 'Proxima Nova',
//         scaffoldBackgroundColor: Colors.white70,
//         primaryColor: const Color(0xFFFF8500),
//         colorScheme:
//         ColorScheme.fromSwatch().copyWith(secondary: Colors.amber),
//       ),
//       supportedLocales: const [
//         Locale('en'),
//         Locale('th'),
//         Locale('lo'),
//         Locale('km'),
//       ],
//       locale: const Locale('en'),
//       fallbackLocale: const Locale('en'),
//       localizationsDelegates: const [
//         GlobalMaterialLocalizations.delegate,
//         GlobalWidgetsLocalizations.delegate,
//         GlobalCupertinoLocalizations.delegate,
//       ],
//       translations: TranslationsService(),
//       home: LockToWinScreen(data: widget.data),
//     );
//   }
// }
