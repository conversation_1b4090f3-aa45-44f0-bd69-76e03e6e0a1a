# 🪙 LikeWallet - Digital Wallet & Rewards Platform

[![Flutter](https://img.shields.io/badge/Flutter-3.32.0-blue.svg)](https://flutter.dev/)
[![Dart](https://img.shields.io/badge/Dart-3.8.0-blue.svg)](https://dart.dev/)
[![Shorebird](https://img.shields.io/badge/Shorebird-1.6.39-green.svg)](https://shorebird.dev/)
[![Firebase](https://img.shields.io/badge/Firebase-Enabled-orange.svg)](https://firebase.google.com/)

LikeWallet is a comprehensive digital wallet and rewards platform built with Flutter, featuring cryptocurrency support, loyalty points management, and innovative gaming features like Lock-to-Win lottery system.

## 🌟 Key Features

### 💰 Digital Wallet
- **Multi-currency Support**: Handle both traditional points (Likepoint) and cryptocurrency
- **Secure Transactions**: Web3-enabled blockchain transactions with smart contract integration
- **Real-time Balance**: Live balance updates with THB conversion
- **Transaction History**: Comprehensive transaction tracking and receipts

### 🎯 Rewards & Loyalty System
- **Likepoint Accumulation**: Earn points through partner activities and transactions
- **Tier-based Benefits**: Multiple user tiers with increasing privileges
- **Lock & Earn**: Stake tokens to earn passive rewards with competitive APY
- **Partner Network**: Integrated with alliance companies for point redemption

### 🎲 Lock-to-Win Gaming
- **Lottery System**: Blockchain-based lottery with transparent smart contracts
- **NFT Tickets**: Lottery tickets as NFTs with verifiable ownership
- **Multi-round Games**: Participate in multiple lottery rounds
- **Reward Claims**: Automated reward distribution for winners

### 🔐 Security & Authentication
- **Biometric Authentication**: Fingerprint and Face ID support
- **KYC Verification**: Know Your Customer process for enhanced security
- **Bank Verification**: Secure bank account linking for cash redemptions
- **Firebase Integration**: Secure user authentication and data management

### 📱 User Experience
- **Multi-language Support**: English, Thai, Lao, and Khmer languages
- **Responsive Design**: Optimized for various screen sizes
- **Push Notifications**: Real-time updates and promotional messages
- **Offline Capability**: Core features available without internet connection

## 🏗️ Technical Architecture

### Frontend
- **Framework**: Flutter 3.32.0 with Dart 3.8.0
- **State Management**: GetX for reactive state management
- **UI Components**: Custom widgets with Material Design
- **Responsive Design**: ScreenUtil for adaptive layouts

### Backend Integration
- **Firebase**: Authentication, Firestore, Cloud Messaging, Analytics
- **Web3**: Ethereum blockchain integration with smart contracts
- **REST APIs**: Custom backend services for wallet operations
- **Real-time Updates**: WebSocket connections for live data

### Blockchain Features
- **Smart Contracts**: ERC-20 token management and lottery contracts
- **Web3 Integration**: Direct blockchain interaction for transactions
- **Cryptocurrency Support**: Native support for various digital assets
- **NFT Support**: Lottery tickets and collectibles as NFTs

## 🚀 Getting Started

### Prerequisites
- Flutter SDK 3.32.0 or higher
- Dart SDK 3.8.0 or higher
- Android Studio / VS Code
- Firebase account and project setup
- Shorebird CLI (for OTA updates)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/likewallet.git
   cd likewallet
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure Firebase**
   - Add your `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
   - Update Firebase configuration in `lib/firebase_options.dart`

4. **Set up environment configuration**
   - Configure API endpoints in `assets/config.json`
   - Update blockchain network settings

5. **Run the application**
   ```bash
   flutter run
   ```

### Shorebird Setup (OTA Updates)

1. **Install Shorebird CLI**
   ```bash
   curl --proto '=https' --tlsv1.2 https://raw.githubusercontent.com/shorebirdtech/install/main/install.sh -sSf | bash
   ```

2. **Initialize Shorebird**
   ```bash
   shorebird init
   ```

3. **Create a release**
   ```bash
   shorebird release android
   shorebird release ios
   ```

## 📁 Project Structure

```
lib/
├── controller/           # Business logic controllers
│   ├── walletController/ # Wallet operations
│   ├── rewardController/ # Rewards management
│   ├── transferController/ # Transaction handling
│   └── otherController/  # Utility controllers
├── model/               # Data models
├── service/             # Core services
├── view/                # UI screens and widgets
│   ├── home/           # Home dashboard
│   ├── login/          # Authentication
│   ├── profile/        # User profile
│   ├── reward/         # Rewards system
│   └── transferPoint/  # Transaction flows
└── main.dart           # Application entry point

lock_to_win/            # Lock-to-Win lottery module
├── lib/
│   ├── controller/     # Lottery game logic
│   ├── models/         # Lottery data models
│   ├── screens/        # Lottery UI screens
│   └── services/       # Blockchain services

assets/
├── image/              # Application images
├── icon/               # App icons
├── fonts/              # Custom fonts
├── svgs/               # Vector graphics
└── config.json         # Configuration files
```

## 🔧 Configuration

### Environment Setup
Create configuration files for different environments:

```json
{
  "apiUrl": "https://api.likepoint.io",
  "likeapi": "https://new.likepoint.io",
  "contractLike": "0x...",
  "rpcUrl": "https://...",
  "wsUrl": "wss://..."
}
```

### Firebase Configuration
1. Create a Firebase project
2. Enable Authentication, Firestore, Cloud Messaging
3. Download configuration files
4. Update `firebase_options.dart`

## 📱 Features Deep Dive

### Wallet Operations
- Send and receive Likepoints
- Cryptocurrency transactions
- QR code scanning for payments
- Contact management for easy transfers
- Transaction receipts and history

### Rewards System
- Daily check-ins and bonuses
- Partner merchant rewards
- Referral programs
- Lock & Earn staking with APY calculations
- Tier progression system

### Lock-to-Win Lottery
- Purchase lottery tickets with locked tokens
- Multiple number selection strategies
- Real-time draw results
- Automated prize distribution
- Historical lottery data

## 🛠️ Development

### Code Style
- Follow Flutter/Dart style guidelines
- Use meaningful variable and function names
- Implement proper error handling
- Add comprehensive comments for complex logic

### Testing
```bash
# Run unit tests
flutter test

# Run integration tests
flutter test integration_test/
```

### Building for Production

**Android:**
```bash
flutter build apk --release
flutter build appbundle --release
```

**iOS:**
```bash
flutter build ios --release
```

## 🔄 OTA Updates with Shorebird

Deploy instant updates without app store approval:

```bash
# Create a patch
shorebird patch android
shorebird patch ios

# Check patch status
shorebird patch list
```

## 🌐 Localization

Supported languages:
- English (en)
- Thai (th)
- Lao (lo)
- Khmer (km)

Add new translations in the translation controller and update supported locales.

## 🔒 Security Considerations

- All sensitive operations require biometric authentication
- Private keys are securely stored using platform keystore
- API communications use HTTPS with certificate pinning
- Smart contract interactions are validated and signed locally

## 📊 Analytics & Monitoring

- Firebase Analytics for user behavior tracking
- Crashlytics for error monitoring
- Custom events for business metrics
- Performance monitoring for optimization

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary software owned by CypherMines Corporation. All rights reserved.

## 📞 Support

For technical support or business inquiries:
- Email: <EMAIL>
- Website: https://likepoint.io
- Documentation: https://docs.likepoint.io

## 🔗 Related Projects

- [Lock-to-Win Module](./lock_to_win/) - Lottery gaming system
- [Web3 Integration](./lib/controller/web3/) - Blockchain connectivity
- [Rewards Engine](./lib/controller/rewardController/) - Loyalty system

---

**Built with ❤️ by the LikeWallet Team**
