# 🧪 Testing Guide for _checkForUpdates Function

## Quick Start (5 Minutes)

### Option 1: Test UI Only (No Shorebird Setup Required)
1. **Run your app**
2. **Look for debug buttons** in the top-right corner of the main screen
3. **Tap "Force Show Dialog"** (green button) - This tests just the UI
4. **Tap "Test Page"** (purple button) - Opens comprehensive test page

### Option 2: Test Real Functionality (Requires Shorebird Setup)

## 📋 Prerequisites
- ✅ Shorebird CLI installed and initialized
- ✅ shorebird.yaml file exists in your project
- ✅ Android device or emulator

## 🚀 Step-by-Step Testing

### Step 1: Create Initial Release
```bash
# Navigate to your project directory
cd /Users/<USER>/Documents/GitHub/LikeWalletPharse3

# Set up Shorebird path
export PATH="$HOME/.shorebird/bin:$PATH"

# Create a release (this is the base version)
shorebird release android
```

### Step 2: Install Release on Device
```bash
# The release command creates an APK
# Install it on your device/emulator
# Location: build/app/outputs/flutter-apk/app-release.apk
```

### Step 3: Make Code Changes
```bash
# Option A: Use the script
./test_shorebird.sh
# Choose option 4: Make test code change

# Option B: Manual change
# Edit any Dart file, add a comment or change text
echo "// Test change $(date)" >> lib/view/navigationBar/mainNavigator.dart
```

### Step 4: Create Patch
```bash
# Create a patch with your changes
shorebird patch android
```

### Step 5: Test the Function
1. **Open your app** (the release version installed in Step 2)
2. **Use one of these methods**:

#### Method A: Debug Buttons
- **Blue button**: "Test Update Check" - Tests real function
- **Green button**: "Force Show Dialog" - Tests UI only
- **Purple button**: "Test Page" - Comprehensive testing

#### Method B: Test Page
1. Tap purple "Test Page" button
2. Tap "Check for Updates"
3. Watch the status and console logs

#### Method C: Automatic (on app start)
- The function runs automatically when MainScreen loads
- Check console logs for output

## 🔍 What to Look For

### Console Output (Success):
```
🔍 Starting update check...
📱 Update check result: true
✅ Update available! Showing dialog...
```

### Console Output (No Updates):
```
🔍 Starting update check...
📱 Update check result: false
❌ No updates available
```

### Console Output (Error):
```
❌ Error checking for updates: [error message]
📍 Stack trace: [stack trace]
```

## 🐛 Troubleshooting

### "No updates available" always shows
**Cause**: No patch created or wrong version installed
**Solution**:
1. Verify you installed the release version (not debug)
2. Make sure you created a patch after the release
3. Check: `shorebird patches list`

### Function throws errors
**Cause**: Shorebird not properly initialized or network issues
**Solution**:
1. Run: `shorebird doctor`
2. Check internet connection
3. Verify shorebird.yaml exists

### Dialog doesn't appear
**Cause**: AlertUpdatePatchPage not found or import issue
**Solution**:
1. Test with "Force Show Dialog" first
2. Check if AlertUpdatePatchPage file exists
3. Verify import statement

## 🎯 Quick Test Commands

```bash
# Check Shorebird status
shorebird doctor

# List all releases
shorebird releases list

# List all patches
shorebird patches list

# Check current patch info
shorebird patch list --release-version=1.0.0+1
```

## 📱 Testing Scenarios

| Test Case | Expected Result | How to Verify |
|-----------|----------------|---------------|
| Fresh install, no patches | `isUpdateAvailable = false` | No dialog shows |
| Patch available | `isUpdateAvailable = true` | Dialog appears |
| Network error | Catches exception | Error logged |
| Invalid state | Handles gracefully | No crash |

## 🔧 Debug Mode

To enable more detailed logging, you can:

1. **Check Flutter logs**:
```bash
flutter logs
```

2. **Use Android Studio logcat** to see print statements

3. **Use the test page** for real-time status updates

## 🚀 Production Notes

Before releasing:
1. Set debug buttons to `if (false)`
2. Remove or comment out test page navigation
3. Reduce console logging (optional)

## 📞 Need Help?

If you're still having issues:
1. Run `./test_shorebird.sh` and choose option 5 for detailed info
2. Check the ShorebirdTestPage for real-time debugging
3. Verify all prerequisites are met
4. Test with "Force Show Dialog" to isolate UI vs logic issues
