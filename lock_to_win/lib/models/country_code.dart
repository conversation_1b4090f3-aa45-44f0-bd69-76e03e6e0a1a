class CountryCode {
  final String flagPath;
  final String countryName;
  final String isdCode;

  CountryCode({
    required this.flagPath,
    required this.countryName,
    required this.isdCode,
  });
}

CountryCode cambodia = CountryCode(
  flagPath: 'assets/svgs/cambodia.svg',
  countryName: 'Cambodia',
  isdCode: '+855',
);
CountryCode lao = CountryCode(
  flagPath: 'assets/svgs/lao.svg',
  countryName: 'Lao',
  isdCode: '+856',
);
CountryCode thailand = CountryCode(
  flagPath: 'assets/svgs/thailand.svg',
  countryName: 'Thailand',
  isdCode: '+66',
);
CountryCode vietnam = CountryCode(
  flagPath: 'assets/svgs/vietnam.svg',
  countryName: 'Vietnam',
  isdCode: '+84',
);

List<CountryCode> countryCodes = [cambodia, lao, thailand, vietnam];
