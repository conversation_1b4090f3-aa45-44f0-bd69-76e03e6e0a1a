class Contact {
  final String imgPath;
  final String name;
  final String number;
  bool isFav;

  Contact({
    required this.imgPath,
    required this.name,
    required this.number,
    this.isFav = false,
  });
}

Contact atom = Contact(
  imgPath: '',
  name: 'Atom',
  number: '+66804987664',
);
Contact bethany = Contact(
  imgPath: '',
  name: '<PERSON>',
  number: '+66889752112',
);
Contact catcat = Contact(
  imgPath: 'assets/images/cat.jpg',
  name: '<PERSON><PERSON> Catayama',
  number: '+66845687661',
);
Contact gandalama = Contact(
  imgPath: '',
  name: '<PERSON><PERSON><PERSON><PERSON>',
  number: '+66804987664',
  isFav: true,
);
Contact jonathan = Contact(
  imgPath: '',
  name: '<PERSON>',
  number: '+66804987664',
);
Contact lamalama = Contact(
  imgPath: '',
  name: '<PERSON><PERSON><PERSON>',
  number: '+66804987664',
);
Contact mango = Contact(
  imgPath: '',
  name: '<PERSON><PERSON>',
  number: '+66804987664',
);
Contact muttana = Contact(
  imgPath: '',
  name: '<PERSON><PERSON><PERSON>',
  number: '+66804987664',
  isFav: true,
);
Contact nannalin = Contact(
  imgPath: '',
  name: 'Nannalin Poenateetal',
  number: '+66804987664',
  isFav: true,
);
Contact pvpneo = Contact(
  imgPath: '',
  name: 'PVPNeo',
  number: '+66804987664',
  isFav: true,
);
Contact sarannna = Contact(
  imgPath: '',
  name: 'Sarannna Thannrijjalaliha',
  number: '+66804987664',
  isFav: true,
);
Contact zara = Contact(
  imgPath: '',
  name: 'Zara',
  number: '+66804987664',
);

List<Contact> quickContacts = [
  atom,
  bethany,
  catcat,
  gandalama,
  jonathan,
  lamalama,
  mango,
  muttana,
  nannalin,
  pvpneo,
  sarannna,
  zara,
];

// LikeWallet Friends List

List<Contact> likeWalletFriendsContacts = [
  atom,
  bethany,
  catcat,
  gandalama,
  jonathan,
  lamalama,
  mango,
  muttana,
  nannalin,
  pvpneo,
  sarannna,
  zara,
];

List<Contact> favContacts = [
  gandalama,
  muttana,
  nannalin,
  pvpneo,
  sarannna,
];
