class Currency {
  final String svgPath;
  final String symbol;
  final String isoTitle;
  final String shortTitle;
  final String completeTitle;
  final double likepointConversionFactor;

  Currency({
    required this.svgPath,
    required this.symbol,
    required this.isoTitle,
    required this.shortTitle,
    required this.completeTitle,
    required this.likepointConversionFactor,
  });
}

Currency lak = Currency(
  svgPath: 'assets/svgs/lak.svg',
  symbol: '₭',
  isoTitle: 'LAK',
  shortTitle: 'Kip',
  completeTitle: 'LAO Kip',
  likepointConversionFactor: 0.29,
);

Currency like = Currency(
  svgPath: 'assets/svgs/like_currency.svg',
  symbol: 'LIKE',
  isoTitle: 'LIKE',
  shortTitle: 'LIKE',
  completeTitle: 'Likepoint',
  likepointConversionFactor: 1.0,
);

Currency thb = Currency(
  svgPath: 'assets/svgs/thb.svg',
  symbol: '฿',
  isoTitle: 'THB',
  shortTitle: 'Baht',
  completeTitle: 'THAI Baht',
  likepointConversionFactor: 100.0,
);

Currency usd = Currency(
  svgPath: 'assets/svgs/usd.svg',
  symbol: '\$',
  isoTitle: 'USD',
  shortTitle: 'Dollar',
  completeTitle: 'US Dollar',
  likepointConversionFactor: 3342.0,
);

Currency vnd = Currency(
  svgPath: 'assets/svgs/vnd.svg',
  symbol: '₫',
  isoTitle: 'VND',
  shortTitle: 'Dong',
  completeTitle: 'VIETNAMESE Dong',
  likepointConversionFactor: 0.13,
);

List<Currency> currencies = [lak, like, thb, usd, vnd];
