import 'package:flutter/material.dart';

enum ProgressType { SUCCESS, PROCESSING, INCOMPLETE }

extension ParseToString on ProgressType {
  String toShortString() {
    return toString().split('.').last;
  }
}

enum TransactionType { Locked, Unlocked, Reward, Addition, Removal }

class Transaction {
  final int colorVariationType;
  final TransactionType transactionType;
  final IconData icon;
  final String topLeftText;
  final String bottomLeftNumber;
  final String bottomLeftType;
  final String topRightType;
  final String topRightString;
  final String bottomRightText;
  final ProgressType progressType;
  final String date;
  final bool showContactUs;
  final String note;
  final String fee;
  final String youGet;
  final String youPay;
  final String bankAccountNumber;

  Transaction({
    required this.colorVariationType,
    required this.transactionType,
    required this.icon,
    required this.topLeftText,
    required this.bottomLeftNumber,
    required this.bottomLeftType,
    required this.topRightType,
    required this.topRightString,
    required this.bottomRightText,
    required this.progressType,
    required this.date,
    this.showContactUs = false,
    this.note = '',
    this.fee = '',
    this.youGet = '',
    this.youPay = '',
    this.bankAccountNumber = '',
  });
}

Transaction transaction0 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Locked,
  icon: Icons.lock_outline,
  topLeftText: 'Locked',
  bottomLeftNumber: '2,000,000',
  bottomLeftType: 'LIKE',
  topRightType: '',
  topRightString: '',
  bottomRightText: '2:02 PM',
  progressType: ProgressType.SUCCESS,
  date: '20 March 2021',
);
Transaction transaction1 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Reward,
  icon: Icons.add,
  topLeftText: 'Reward',
  bottomLeftNumber: '145.98',
  bottomLeftType: 'LIKE',
  topRightType: 'from',
  topRightString: 'Lock & Earn',
  bottomRightText: '3:30 PM',
  progressType: ProgressType.SUCCESS,
  date: '19 March 2021',
);
Transaction transaction2 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Reward,
  icon: Icons.add,
  topLeftText: 'Reward',
  bottomLeftNumber: '120.11',
  bottomLeftType: 'LIKE',
  topRightType: 'from',
  topRightString: 'X - Lock',
  bottomRightText: '3:30 PM',
  progressType: ProgressType.SUCCESS,
  date: '19 March 2021',
);
Transaction transaction3 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Locked,
  icon: Icons.lock_outline,
  topLeftText: 'Locked',
  bottomLeftNumber: '2,000',
  bottomLeftType: 'LIKE',
  topRightType: '',
  topRightString: '',
  bottomRightText: '2:02 PM',
  progressType: ProgressType.PROCESSING,
  date: '18 March 2021',
);
Transaction transaction4 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Locked,
  icon: Icons.lock_outline,
  topLeftText: 'Locked',
  bottomLeftNumber: '2,000',
  bottomLeftType: 'LIKE',
  topRightType: '',
  topRightString: '',
  bottomRightText: '2:00 PM',
  progressType: ProgressType.INCOMPLETE,
  showContactUs: true,
  date: '18 March 2021',
);
Transaction transaction5 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Addition,
  icon: Icons.add,
  topLeftText: 'Received',
  bottomLeftNumber: '75,000',
  bottomLeftType: 'LIKE',
  topRightType: 'from',
  topRightString: 'Unknown',
  bottomRightText: '3:30 PM',
  progressType: ProgressType.SUCCESS,
  date: '17 March 2021',
);
Transaction transaction6 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Removal,
  icon: Icons.remove,
  topLeftText: 'Sent',
  bottomLeftNumber: '37,000',
  bottomLeftType: 'LIKE',
  topRightType: 'to',
  topRightString: 'Suwanna',
  bottomRightText: '2:30 PM',
  progressType: ProgressType.SUCCESS,
  date: '17 March 2021',
  note:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod '
      'tempor incididunt ut labore et dolore magna aliqua. Ut enim ad '
      'minim veniam, quis nostrud exercitation ullamco',
);
Transaction transaction7 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Locked,
  icon: Icons.lock_outline,
  topLeftText: 'Locked',
  bottomLeftNumber: '50,000',
  bottomLeftType: 'LIKE',
  topRightType: 'as',
  topRightString: 'Collateral',
  bottomRightText: '2:30 PM',
  progressType: ProgressType.SUCCESS,
  date: '17 March 2021',
);
Transaction transaction8 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Removal,
  icon: Icons.remove,
  topLeftText: 'Cash-Out',
  bottomLeftNumber: '40,000',
  bottomLeftType: 'LIKE',
  topRightType: 'to',
  topRightString: 'Bank Acct.',
  bottomRightText: '11:30 AM',
  progressType: ProgressType.SUCCESS,
  date: '17 March 2021',
  fee: '10',
  youGet: '390',
  bankAccountNumber: '....869-3',
);
Transaction transaction9 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Addition,
  icon: Icons.add,
  topLeftText: 'Buy LIKE',
  bottomLeftNumber: '15,000',
  bottomLeftType: 'LIKE',
  topRightType: '',
  topRightString: '',
  bottomRightText: '10:30 AM',
  progressType: ProgressType.SUCCESS,
  date: '17 March 2021',
  youPay: '150',
  bankAccountNumber: '....869-3',
);
Transaction transaction10 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Unlocked,
  icon: Icons.lock_open_outlined,
  topLeftText: 'Unlocked',
  bottomLeftNumber: '6,000',
  bottomLeftType: 'LIKE',
  topRightType: '',
  topRightString: '',
  bottomRightText: '9:30 AM',
  progressType: ProgressType.SUCCESS,
  date: '17 March 2021',
);
Transaction transaction11 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Reward,
  icon: Icons.add,
  topLeftText: 'Reward',
  bottomLeftNumber: '145.98',
  bottomLeftType: 'LIKE',
  topRightType: 'from',
  topRightString: 'LDX',
  bottomRightText: '8:30 AM',
  progressType: ProgressType.SUCCESS,
  date: '17 March 2021',
);

Transaction transaction12 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Removal,
  icon: Icons.remove,
  topLeftText: 'Cash-Out',
  bottomLeftNumber: '40,000',
  bottomLeftType: 'LIKE',
  topRightType: 'to',
  topRightString: 'Bank Acct.',
  bottomRightText: '7:30 AM',
  progressType: ProgressType.PROCESSING,
  date: '17 March 2021',
);
Transaction transaction13 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Addition,
  icon: Icons.add,
  topLeftText: 'Buy LIKE',
  bottomLeftNumber: '15,000',
  bottomLeftType: 'LIKE',
  topRightType: '',
  topRightString: '',
  bottomRightText: '6:30 AM',
  progressType: ProgressType.PROCESSING,
  date: '17 March 2021',
);
Transaction transaction14 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Unlocked,
  icon: Icons.lock_open_outlined,
  topLeftText: 'Unlocked',
  bottomLeftNumber: '6,000',
  bottomLeftType: 'LIKE',
  topRightType: '',
  topRightString: '',
  bottomRightText: '6:30 AM',
  progressType: ProgressType.PROCESSING,
  date: '17 March 2021',
);

Transaction transaction15 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Removal,
  icon: Icons.remove,
  topLeftText: 'Cash-Out',
  bottomLeftNumber: '40,000',
  bottomLeftType: 'LIKE',
  topRightType: 'to',
  topRightString: 'Bank Acct.',
  bottomRightText: '5:50 AM',
  progressType: ProgressType.INCOMPLETE,
  date: '17 March 2021',
  showContactUs: true,
);
Transaction transaction16 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Addition,
  icon: Icons.add,
  topLeftText: 'Buy LIKE',
  bottomLeftNumber: '15,000',
  bottomLeftType: 'LIKE',
  topRightType: '',
  topRightString: '',
  bottomRightText: '5:30 AM',
  progressType: ProgressType.INCOMPLETE,
  date: '17 March 2021',
  showContactUs: true,
);
Transaction transaction17 = Transaction(
  colorVariationType: 1,
  transactionType: TransactionType.Unlocked,
  icon: Icons.lock_open_outlined,
  topLeftText: 'Unlocked',
  bottomLeftNumber: '6,000',
  bottomLeftType: 'LIKE',
  topRightType: '',
  topRightString: '',
  bottomRightText: '5:03 AM',
  progressType: ProgressType.INCOMPLETE,
  date: '17 March 2021',
  showContactUs: true,
);

List<Transaction> likepointTransactions = [
  transaction0,
  transaction1,
  transaction2,
  transaction3,
  transaction4,
  transaction5,
  transaction6,
  transaction7,
  transaction8,
  transaction9,
  transaction10,
  transaction11,
  transaction12,
  transaction13,
  transaction14,
  transaction15,
  transaction16,
  transaction17,
];

Transaction xtransaction0 = Transaction(
  colorVariationType: 2,
  transactionType: TransactionType.Locked,
  icon: Icons.lock_outline,
  topLeftText: 'X - Locked',
  bottomLeftNumber: '2,000,000',
  bottomLeftType: 'XLK',
  topRightType: '',
  topRightString: '',
  bottomRightText: '2:02 PM',
  progressType: ProgressType.SUCCESS,
  date: '20 March 2021',
);
Transaction xtransaction1 = Transaction(
  colorVariationType: 2,
  transactionType: TransactionType.Unlocked,
  icon: Icons.lock_open_outlined,
  topLeftText: 'Unlocked',
  bottomLeftNumber: '6,000',
  bottomLeftType: 'LIKE',
  topRightType: '',
  topRightString: '',
  bottomRightText: '9:30 PM',
  progressType: ProgressType.SUCCESS,
  date: '20 March 2021',
);
Transaction xtransaction2 = Transaction(
  colorVariationType: 2,
  transactionType: TransactionType.Locked,
  icon: Icons.lock_outline,
  topLeftText: 'X - Locked',
  bottomLeftNumber: '2,000,000',
  bottomLeftType: 'XLK',
  topRightType: '',
  topRightString: '',
  bottomRightText: '2:02 PM',
  progressType: ProgressType.PROCESSING,
  date: '20 March 2021',
);
Transaction xtransaction3 = Transaction(
  colorVariationType: 2,
  transactionType: TransactionType.Unlocked,
  icon: Icons.lock_open_outlined,
  topLeftText: 'Unlocked',
  bottomLeftNumber: '6,000',
  bottomLeftType: 'LIKE',
  topRightType: '',
  topRightString: '',
  bottomRightText: '9:30 PM',
  progressType: ProgressType.PROCESSING,
  date: '20 March 2021',
);
Transaction xtransaction4 = Transaction(
  colorVariationType: 2,
  transactionType: TransactionType.Locked,
  icon: Icons.lock_outline,
  topLeftText: 'X - Locked',
  bottomLeftNumber: '2,000,000',
  bottomLeftType: 'XLK',
  topRightType: '',
  topRightString: '',
  bottomRightText: '2:02 PM',
  progressType: ProgressType.INCOMPLETE,
  date: '20 March 2021',
  showContactUs: true,
);
Transaction xtransaction5 = Transaction(
  colorVariationType: 2,
  transactionType: TransactionType.Unlocked,
  icon: Icons.lock_open_outlined,
  topLeftText: 'Unlocked',
  bottomLeftNumber: '6,000',
  bottomLeftType: 'LIKE',
  topRightType: '',
  topRightString: '',
  bottomRightText: '9:30 PM',
  progressType: ProgressType.INCOMPLETE,
  date: '20 March 2021',
  showContactUs: true,
);

List<Transaction> xlikeTransactions = [
  xtransaction0,
  xtransaction1,
  xtransaction2,
  xtransaction3,
  xtransaction4,
  xtransaction5,
];
