/// Flutter icons Xlite
/// Copyright (C) 2022 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  Xlite
///      fonts:
///       - asset: fonts/Xlite.ttf
///
/// 
///
import 'package:flutter/widgets.dart';

class Xlite {
  Xlite._();

  static const _kFontFam = 'Xlite';
  static const String? _kFontPkg = null;

  static const IconData personwitharrow = IconData(0xe800, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData back = IconData(0xe801, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData check = IconData(0xe802, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData graph = IconData(0xe803, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData hamburger = IconData(0xe804, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData help = IconData(0xe805, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData home = IconData(0xe806, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData notificationwithdot = IconData(0xe807, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData notificationwithoutdot = IconData(0xe808, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData person_with_arrow_filled = IconData(0xe809, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData help_filled = IconData(0xe80a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData home_filled = IconData(0xe80b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData notification_with_dot_filled = IconData(0xe80c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData notification_without_dot_filled = IconData(0xe80d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData plus = IconData(0xe80e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData minus = IconData(0xe80f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData switch_currency_with_like = IconData(0xe810, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData wallet = IconData(0xe811, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData fav = IconData(0xe812, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData forward = IconData(0xe813, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData qr_scanner = IconData(0xe814, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData select_currency = IconData(0xe815, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData back_2 = IconData(0xe816, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData qr = IconData(0xe817, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData backspace = IconData(0xe818, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData close = IconData(0xe819, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData drag = IconData(0xe81a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData more_info = IconData(0xe81b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData search = IconData(0xe81c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData heart = IconData(0xe81d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}
