import 'dart:core';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:encrypt/encrypt.dart' as Encrypt;

abstract class CryptoEncryptInterface {
  Future<String> decrypt(String text);
  Future<bool> generateKey();
  Future<List<String>> getKeyEncrypt();
}

class CryptoEncrypt implements CryptoEncryptInterface {
  static const chars = "abcdefghijklmnopqrstuvwxyz0123456789";

  String RandomString(int strlen) {
    Random rnd = new Random( new DateTime.now().millisecondsSinceEpoch);
    String result = "";
    for (var i = 0; i < strlen; i++) {
      result += chars[rnd.nextInt(chars.length)];
    }
    return result;
  }

  @override
  Future<String> decrypt(String text) async {
    List<String> getKey = await getKeyEncrypt();
    String keyEncrypt = getKey[0];
    String ivEncrypt = getKey[1];

    final key = Encrypt.Key.fromUtf8(keyEncrypt);
    final iv = Encrypt.IV.fromUtf8(ivEncrypt);
    final encrypter =
        Encrypt.Encrypter(Encrypt.AES(key, mode: Encrypt.AESMode.cbc));
    final decrypted =
        encrypter.decrypt(Encrypt.Encrypted.fromBase64(text), iv: iv);
    return decrypted;
//
  }

  @override
  Future<bool> generateKey() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String keyEncrypt = RandomString(32);
    String ivEncrypt = RandomString(16);
    prefs.setString('keyEncrypt', keyEncrypt);
    prefs.setString('ivEncrypt', ivEncrypt);
    return true;
  }

  @override
  Future<List<String>> getKeyEncrypt() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String keyEncrypt = prefs.getString('keyEncrypt') ?? '';
    String ivEncrypt = prefs.getString('ivEncrypt') ?? '';

    return [keyEncrypt, ivEncrypt];
  }
}
