import 'dart:async';
import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import 'package:lock_to_win/app_config.dart';

abstract class BaseAuth {
  // Future<User?> singInWithEmailAndPassword(String email, String password);
  // Future<String?> createUserWithEmailAndPassword(String email, String password);
  // Future<String> currentUser();

  // Future<User?> getCurrentUser();

  Future<String?> getTokenFirebase();
  // Future<void> signOut();
  // Future<bool> checkExistUser(String phoneNumber);
  // Future<List<String>> checkOldUserName(String phoneNumber);
  // Future<bool> checkExistsUserEmail(String email);
}

class Auth implements BaseAuth {
  final FirebaseAuth firebaseAuth = FirebaseAuth.instance;

  // Future<User?> singInWithEmailAndPassword(
  //     String email, String password) async {
  //   User? user = (await firebaseAuth.signInWithEmailAndPassword(
  //           email: email, password: password))
  //       .user;
  //   return user;
  // }

  // Future<String?> createUserWithEmailAndPassword(
  //     String email, String password) async {
  //   User? user = (await firebaseAuth.createUserWithEmailAndPassword(
  //           email: email, password: password))
  //       .user;
  //   var token = await user!.getIdTokenResult();
  //   return token.token;
  // }
  //
  // Future<String> currentUser() async {
  //   User? user = firebaseAuth.currentUser;
  //   return user!.uid;
  // }

  // Future<User?> getCurrentUser() async {
  //   User? user = firebaseAuth.currentUser;
  //   return user!;
  // }

  @override
  Future<String?> getTokenFirebase() async {
    User? user = firebaseAuth.currentUser;
    var token = await user!.getIdTokenResult();
    return token.token;
  }

  // Future<void> signOut() async {
  //   return firebaseAuth.signOut();
  // }

  Future<bool> checkExistUser(String phoneNumber) async {
    var url = Uri.https(env.apiUrl, '/checkExistUser');
    var response = await http.post(url, body: {
      'apiKey': env.APIKEY,
      'secretKey': env.SECRETKEY,
      'phone_number': phoneNumber
    });
    var body = json.decode(response.body);
    return body['result'];
  }

  Future<List<String>> checkOldUserName(String phoneNumber) async {
    var url = Uri.https(env.OldAPI, '/checkOldUserName');
    var response = await http.post(url, body: {'phone_number': phoneNumber});
    var body = json.decode(response.body);
    return [body['statusCode'].toString(), body['firstname'], body['lastname']];
  }

  Future<bool> checkExistsUserEmail(String email) async {
    var url = Uri.https(env.apiUrl, '/checkExistUserEmail');
    var response = await http.post(url, body: {
      'apiKey': env.APIKEY,
      'secretKey': env.SECRETKEY,
      'email': email
    });
    var body = json.decode(response.body);
    return body['result'];
  }

  // Future<void> sendEmailVerification() async {
  //   // User? user = firebaseAuth.currentUser;
  //   user!.sendEmailVerification();
  // }

  // Future<bool> isEmailVerified() async {
  //   // User? user = firebaseAuth.currentUser;
  //   return user!.emailVerified;
  // }
}
