import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
// import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart' as formatIntl;
import 'package:lock_to_win/app_config.dart';
import 'package:http/http.dart' as http;
import 'package:lock_to_win/library/auth.dart';
import 'package:lock_to_win/library/lottery.dart';
import 'package:lock_to_win/models/lotto/annonces_time.dart';
import 'package:lock_to_win/models/lotto/lotto_now_round.dart';
import 'package:lock_to_win/models/lotto/lotto_round.dart';
import 'package:lock_to_win/models/lotto/your_lotto_list.dart';
import 'package:lock_to_win/widgets/loading.dart';

abstract class LotteryServiceFunction {
  Future<YourLottoList> lottoNumber(
      {required String pketh, required int round});
  Future<List<dynamic>> lottoPreNumber(
      {required String pketh, required int round});
  Future<LottoRound> lottoRound({required int round});
  Future<List<num>> preRoundWinner({required int round});
  Future<double> lastRound();
  Future<double> balanceTicket({required String address});
  Future<AnnoncesTime> announcesCountDown();
  Future<AnnoncesTime> previousTime();
  Future<LottoNowRound> lottoNowRound();
  Future<bool> checkApprove({required String address});
  Future<bool> approveBuy();
  Future<bool> buyTicket();
  Future<bool> checkMultiClaim(
      {required List tokenNumber,
      required String idToken,
      required String address,
      required double preRound});
  Future<bool> multipleClaimButton(
      {required List<dynamic> tokenList,
      required String address,
      required double preRound});
}

class LotteryService implements LotteryServiceFunction {
  late LotteryInterface lottery;
  bool approved = false;
  final f = formatIntl.NumberFormat("###,###.##");
  late YourLottoList yourLottoList;
  late BaseAuth auth;

  @override
  Future<YourLottoList> lottoNumber(
      {required String pketh, required int round}) async {
    List yourLottoRound = [];
    lottery = Lottery();

    var res = await lottery.NFTbalanceOf(pk: pketh);
    var res2 = res.toInt() - 10;

    print(res);
    for (var i = res2.toInt(); i < res.toInt(); i++) {
      print(i);
      var x = await lottery.tokenOfOwnerByIndex(pk: pketh, i: i);
      var lottoIndex = await lottery.getLotteryIssueIndex(i: x.toInt());
      print(lottoIndex.toInt());
      var index = x;

      if (lottoIndex.toInt() == round.toInt()) {
        var number = await lottery.getLotteryNumbers(i: index.toInt());
        var tokenIdIndex = index;
        var rewards = await lottery.getRewardView(i: tokenIdIndex.toInt());
        var tokenNumber = tokenIdIndex;
        yourLottoRound.add({
          "number": number[0].join(''),
          "tokendId": tokenNumber.toString(),
          "reward": rewards,
        });
      }
      print(yourLottoRound);
    }

    return YourLottoList.fromJson(yourLottoRound);
  }

  @override
  Future<List<dynamic>> lottoPreNumber(
      {required String pketh, required int round}) async {
    lottery = Lottery();
    List yourLottoRound = [];
    List yourLotto = [];
    var res = await lottery.NFTbalanceOf(pk: pketh);
    print("res $res");
    var res2 = res.toInt() - 20;
    print("res2 $res2");
    for (var i = res2.toInt(); i < res.toInt(); i++) {
      var x = await lottery.tokenOfOwnerByIndex(pk: pketh, i: i);
      // print(x);
      var lottoIndex = await lottery.getLotteryIssueIndex(i: x.toInt());
      print(lottoIndex.toInt());
      var index = x;

      if (lottoIndex.toInt() == round.toInt()) {
        var number = await lottery.getLotteryNumbers(i: index.toInt());
        var tokenIdIndex = index;
        var rewards = await lottery.getRewardView(i: tokenIdIndex.toInt());
        var tokenNumber = tokenIdIndex;
        yourLotto.add({
          '"number"': number[0],
          '"tokendId"': tokenNumber.toString(),
          '"reward"': rewards,
        });
        yourLottoRound.add({
          'number': number[0],
          'tokendId': tokenNumber.toString(),
          'reward': rewards,
        });
      }
    }
    return [yourLottoRound, yourLotto];
  }

  @override
  Future<List<num>> preRoundWinner({required int round}) async {
    lottery = Lottery();
    final round = await lottery.issueIndex();
    int roundPre = round.toInt() - 1;
    final winnerNum1 =
        await lottery.historyNumbers(index: roundPre, position: 0);
    final winnerNum2 =
        await lottery.historyNumbers(index: roundPre, position: 1);
    final winnerNum3 =
        await lottery.historyNumbers(index: roundPre, position: 2);
    final winnerNum4 =
        await lottery.historyNumbers(index: roundPre, position: 3);

    return [winnerNum1, winnerNum2, winnerNum3, winnerNum4];
  }

  @override
  Future<LottoRound> lottoRound({required int round}) async {
    lottery = Lottery();
    final round = await lottery.issueIndex();
    int roundPre = round.toInt() - 1;
    print(roundPre);
    final winnerNum1 =
        await lottery.historyNumbers(index: roundPre, position: 0);
    final winnerNum2 =
        await lottery.historyNumbers(index: roundPre, position: 1);
    final winnerNum3 =
        await lottery.historyNumbers(index: roundPre, position: 2);
    final winnerNum4 =
        await lottery.historyNumbers(index: roundPre, position: 3);

    final totalPrev = await lottery.historyAmount(index: roundPre, position: 0);

    final matchFour = await lottery.historyAmount(index: roundPre, position: 1);
    final matchThree =
        await lottery.historyAmount(index: roundPre, position: 2);
    final matchTwo = await lottery.historyAmount(index: roundPre, position: 3);

    var allocationFirst = await lottery.allocation(index: 0);
    var allocationSec = await lottery.allocation(index: 1);
    var allocationThird = await lottery.allocation(index: 2);

    var totalPreReward4 = (totalPrev * allocationFirst) / 100;
    var totalPreReward3 = (totalPrev * allocationSec) / 100;
    var totalPreReward2 = (totalPrev * allocationThird) / 100;

    var data = {
      "winnerNumber": [
        winnerNum1.toStringAsFixed(0),
        winnerNum2.toStringAsFixed(0),
        winnerNum3.toStringAsFixed(0),
        winnerNum4.toStringAsFixed(0)
      ],
      "totalPrev": f.format(totalPrev).toString(),
      "matchFour": matchFour.toStringAsFixed(0),
      "matchThree": matchThree.toStringAsFixed(0),
      "matchTwo": matchTwo.toStringAsFixed(0),
      "totalReward4": f.format(totalPreReward4).toString(),
      "totalReward3": f.format(totalPreReward3).toString(),
      "totalReward2": f.format(totalPreReward2).toString(),
    };
    return LottoRound.fromJson(data);
  }

  @override
  Future<LottoNowRound> lottoNowRound() async {
    lottery = Lottery();
    var allocationFirst = await lottery.allocation(index: 0);
    var allocationSec = await lottery.allocation(index: 1);
    var allocationThird = await lottery.allocation(index: 2);

    final totalAmount = await lottery.totalAmount();

    var totalNowReward4 = (totalAmount * allocationFirst) / 100;
    var totalNowReward3 = (totalAmount * allocationSec) / 100;
    var totalNowReward2 = (totalAmount * allocationThird) / 100;

    var data = {
      "total": (totalNowReward2 + totalNowReward3 + totalNowReward4)
          .toStringAsFixed(3),
      "totalNowReward4": totalNowReward4.toStringAsFixed(3),
      "totalNowReward3": totalNowReward3.toStringAsFixed(3),
      "totalNowReward2": totalNowReward2.toStringAsFixed(3),
    };
    return LottoNowRound.fromJson(data);
  }

  @override
  Future<double> lastRound() async {
    try {
      lottery = Lottery();
      final round = await lottery.issueIndex();
      return round.toDouble();
    } catch (e) {
      return 0.0;
    }
  }

  @override
  Future<double> balanceTicket({required String address}) async {
    lottery = Lottery();
    final balanceTicket = await lottery.getAmountLottery(address: address);
    print("balanceTicket : " + balanceTicket.toString());
    return balanceTicket.toDouble();
  }

  @override
  Future<AnnoncesTime> announcesCountDown() async {
    final timer = await FirebaseFirestore.instance
        .collection('lotto')
        .doc('round')
        .collection('timer')
        .doc('next')
        .get();
    var date =
        DateTime.fromMillisecondsSinceEpoch(timer.data()!['time'] * 1000);
    String formattedDate =
        formatIntl.DateFormat('dd MMMM yyyy kk:mm', 'th').format(date);
    var data = {"date": date, "formattedDate": formattedDate};
    print(data);
    return AnnoncesTime.fromJson(data);
  }

  @override
  Future<bool> checkApprove({required String address}) async {
    final allowance = await lottery.allowance(
        addressOwner: address, addressUsed: env.lottery);
    print("allowance " + allowance.toString());
    if (allowance > 0) {
      approved = true;
      print("approved :" + approved.toString());
    } else {
      print("approved :" + approved.toString());
    }
    return approved;
  }

  @override
  Future<bool> approveBuy() async {
    await EasyLoading.show(
      status: 'กำลังดำเนินการ',
      maskType: EasyLoadingMaskType.black,
      dismissOnTap: true,
    );
    var url = Uri.parse('http://new.likepoint.io/approveBuy');
    var token = await auth.getTokenFirebase();
    bool status = false;
    try {
      final response = await http.post(url, body: {
        "apiKey": env.APIKEY,
        "secretKey": env.SECRETKEY,
        "token": token
      });
      if (response.statusCode == 200) {
        var body = json.decode(response.body);
        if (body['result'] == 'ALREADY_APPROVE') {
          // LikeWalletAppTheme.configLoadingSuccess();
          EasyLoading.showSuccess('อนุมัติเรียบร้อย');
          return status = true;
        } else if (body['result'] == 'APPROVED') {
          // LikeWalletAppTheme.configLoadingSuccess();
          EasyLoading.showSuccess('อนุมัติเรียบร้อย');
          return status = true;
        } else {
          return status = false;
        }
      }
    } catch (e) {
      // LikeWalletAppTheme.configLoadingError();
      EasyLoading.showError(e.toString());
      return status = false;
    }
    return status;
  }

  @override
  Future<bool> buyTicket() async {
    await EasyLoading.show(
      status: 'กำลังดำเนินการ',
      maskType: EasyLoadingMaskType.black,
      dismissOnTap: true,
    );
    auth = Auth();
    // var url = Uri.parse('http://new.likepoint.io/buyticket');
    var url = Uri.https(env.apiUrl, "/buyticket");
    var token = await auth.getTokenFirebase();
    bool status = false;
    try {
      final response = await http.post(url, body: {
        "apiKey": env.APIKEY,
        "secretKey": env.SECRETKEY,
        "token": token
      });
      if (response.statusCode == 200) {
        var body = json.decode(response.body);
        // LikeWalletAppTheme.configLoadingError();
        EasyLoading.showError('คุณต้องมี likepoint ติดกระเป๋า');
        print(body);
        if (body['result'] == 'SUCCESS') {
          // LikeWalletAppTheme.configLoadingError();
          EasyLoading.showSuccess('สำเร็จ');
          return status = true;
        } else if (body['result'] == 'NEED_LIKEPOINT_IN_WALLET') {
          // LikeWalletAppTheme.configLoadingError();
          EasyLoading.showError('คุณต้องมี likepoint ติดกระเป๋า');
          return status = false;
        } else if (body['result'] == 'USED_TICKET') {
          // LikeWalletAppTheme.configLoadingError();
          EasyLoading.showError('คุณทายไปเเล้ว');
          return status = false;
        } else if (body['result'] == 'NO_TICKET') {
          // LikeWall/etAppTheme.configLoadingError();
          EasyLoading.showError('คุณไม่มีตั๋ว โปรดล็อคไลท์เพิ่ม');
          return status = false;
        }
        await EasyLoading.dismiss();
      }
    } catch (e) {
      // LikeWalletAppTheme.configLoadingError();
      EasyLoading.showError(e.toString());
      return status = false;
    }
    return status;
  }

  @override
  Future<bool> checkMultiClaim(
      {required List tokenNumber,
      required String idToken,
      required String address,
      required double preRound}) async {
    // print(tokenNumber);

    auth = Auth();
    var url = Uri.https(env.apiUrl, "/checkMultiClaim");
    // var url = Uri.parse'http://new.likepoint.io/checkMultiClaim');
    var token = idToken;

    bool status = false;
    try {
      final response = await http.post(url, body: {
        "apiKey": env.APIKEY,
        "secretKey": env.SECRETKEY,
        "token": token,
        "tokenNumber": tokenNumber.toString()
      });
      if (response.statusCode == 200) {
        var body = json.decode(response.body);
        // LikeWalletAppTheme.configLoadingError();
        // EasyLoading.showError('คุณต้องมี likepoint ติดกระเป๋า');
        print(body);
        if (body['result'] == true) {
          Loading.typeLoadingDismiss();
          await FirebaseFirestore.instance
              .collection('lockToWin')
              .doc(address)
              .collection('round')
              .doc('${preRound.toInt()}')
              .update({"statusWinner": true});
          return status = true;
        } else {
          await FirebaseFirestore.instance
              .collection('lockToWin')
              .doc(address)
              .collection('round')
              .doc('${preRound.toInt()}')
              .update({"statusWinner": false, "statusClaim": "SUCCESS"});
          Loading.typeLoadingDismiss();
          return status = false;
        }
      }
    } catch (e) {
      // LikeWalletAppTheme.configLoadingError();
      EasyLoading.showError(e.toString());
      return status = false;
    }
    return status;
  }

  @override
  Future<bool> multipleClaimButton(
      {required List<dynamic> tokenList,
      required String address,
      required double preRound}) async {
    await EasyLoading.show(
      status: 'กำลังดำเนินการ',
      maskType: EasyLoadingMaskType.black,
      dismissOnTap: true,
    );
    auth = Auth();
    var url = Uri.https(env.apiUrl, "/multipleClaimButton");
    var token = await auth.getTokenFirebase();
    bool status = false;
    try {
      final response = await http.post(url, body: {
        "apiKey": env.APIKEY,
        "secretKey": env.SECRETKEY,
        "token": token,
        "tokenList": tokenList.toString()
      });
      if (response.statusCode == 200) {
        var body = json.decode(response.body);
        print(body);
        if (body['statusCode'] == 200) {
          // LikeWalletAppTheme.configLoadingSuccess();
          EasyLoading.showSuccess('รับรางวัลเเล้ว');

          status = true;
        } else {
          // LikeWalletAppTheme.configLoadingError();

          EasyLoading.showError('เกิดข้อผิดพลาด');
          status = false;
        }
      }
      return status;
    } catch (e) {
      print(e);
      // LikeWalletAppTheme.configLoadingError();
      EasyLoading.showError(e.toString());
      return status;
    }
  }

  @override
  Future<AnnoncesTime> previousTime() async {
    final round = await lottery.issueIndex();
    int roundPre = round.toInt() - 1;
    final timer = await FirebaseFirestore.instance
        .collection('lotto')
        .doc('logs')
        .collection('startDrawing')
        .where('issueIndex', isEqualTo: roundPre.toString())
        .orderBy('time', descending: true)
        .limit(1)
        .get();
    var time = timer.docs[0].data()['time'];
    var date =
    DateTime.fromMillisecondsSinceEpoch(time * 1000);
    String formattedDate =
    formatIntl.DateFormat('dd/MM/yyyy', 'th').format(date);
    var data = {"date": date, "formattedDate": formattedDate};
    print(data);
    return AnnoncesTime.fromJson(data);
  }
}
