import 'package:flutter/material.dart';

const Color layerBG3Dark = Color(0xFF1D2532);
const Color layerBG2Dark = Color(0xFF161C26);
const Color layerBG1Dark = Color(0xFF11161E);
const Color screenBGDark = Color(0xFF080B0F);

const Color textMainColorDark = Color(0xFFE9ECF5);
const Color textGray100Dark = Color(0xFFCAD2DE);
const Color textGray200Dark = Color(0xFFAAB4C4);
const Color textGray300Dark = Color(0xFF8a96ab);
const Color textGray400Dark = Color(0xFF738097);
const Color textGray700Dark = Color(0xFF3E4B5E);

const Color block4Dark = Color(0xFF334157);
const Color block3Dark = Color(0xFF2B374B);
const Color block2Dark = Color(0xFF161C28);
const Color block1Dark = Color(0xFF030406);

const Color hamburgerMenuColorDark = Color(0xFF7F8BA1);

const Color blueDark = Color(0xFF0078FF);
const Color blackButtonDark = Color(0xFF040507);

// G series colors
const Color G1startDark = Color(0xFF7979FF);
const Color G1endDark = Color(0xFF4D4DFF);

const Color G2startDark = Color(0xFFD7A8FF);
const Color G2endDark = Color(0xFF7846FF);

const Color G3startDark = Color(0xFF94F990);
const Color G3endDark = Color(0xFF00CBD4);

const Color G4startDark = Color(0xFFFDC441);
const Color G4endDark = Color(0xFFFF9300);

const Color G5startDark = Color(0xFF3DFFFF);
const Color G5endDark = Color(0xFF00AADD);

const Color G6startDark = Color(0xFFA69BFF);
const Color G6endDark = Color(0xFF6D4AFF);

const Color G7startDark = Color(0xFFD9EB45);
const Color G7endDark = Color(0xFF9FCE01);

const Color G8startDark = Color(0xFFFFA96E);
const Color G8endDark = Color(0xFFDE6A1B);

const Color G9startDark = Color(0xFFF9B1FF);
const Color G9endDark = Color(0xFFBA5EFD);

const Color G10startDark = Color(0xFFFDE847);
const Color G10endDark = Color(0xFFFFB900);

const Color G11startDark = Color(0xFFD0F74E);
const Color G11endDark = Color(0xFFFFDC64);
