import 'package:flutter/material.dart';
import 'package:lock_to_win/config/color_system.dart';
import 'package:lock_to_win/main.dart';

import '../xlite_icons.dart';

class SearchTextField extends StatefulWidget {
  const SearchTextField({super.key, 
    required this.searchTextController,
    this.text,
    this.autofocus = true,
    required this.onChanged,
  });

  final TextEditingController searchTextController;
  final bool autofocus;
  final void Function(String keyword) onChanged;
  final String? text;

  @override
  State<SearchTextField> createState() => _SearchTextFieldState();
}

class _SearchTextFieldState extends State<SearchTextField> {
  bool showPrefix = true;
  final FocusNode _focus = FocusNode();

  @override
  void initState() {
    super.initState();
    _focus.addListener(_onFocusChange);
  }

  void _onFocusChange() {
    setState(() {
      showPrefix = !_focus.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      child: TextField(
        textAlign: TextAlign.left,
        textAlignVertical: TextAlignVertical.center,
        style: TextStyle(
          color: textMainColor,
          fontSize: 12,
          height: 1,
          letterSpacing: 0.75,
          fontWeight: FontWeight.w400,
        ),
        inputFormatters: const [
          // FilteringTextInputFormatter.digitsOnly,
        ],
        keyboardType: TextInputType.text,
        textCapitalization: TextCapitalization.words,
        controller: widget.searchTextController,
        focusNode: _focus,
        cursorColor: blueColor,
        autofocus: widget.autofocus,
        onChanged: widget.onChanged,
        decoration: InputDecoration(
          hintText: widget.text,
          hintStyle: TextStyle(
            color: isDarkMode ? const Color(0xff8A96AB) : const Color(0xff4E5D75),
            fontSize: 12,
            height: 1,
            letterSpacing: 0.75,
            fontWeight: FontWeight.w400,
          ),
          prefixIcon: showPrefix
              ? Padding(
                  padding: const EdgeInsets.only(left: 4),
                  child: Icon(
                    Xlite.search,
                    color: isDarkMode ? const Color(0xffE9ECF5) : const Color(0xff262F3E),
                    size: 24,
                  ),
                )
              : null,
          filled: true,
          fillColor: isDarkMode ? const Color(0xff1D2532) : const Color(0xffFEFEFE),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6.0),
            borderSide: BorderSide.none,
          ),
          contentPadding: EdgeInsets.symmetric(
            horizontal: showPrefix ? 12 : 16,
          ),
        ),
      ),
    );
  }
}
