import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lock_to_win/main.dart';

class GameRulesScreen extends StatelessWidget {
  const GameRulesScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xff11161E) : const Color(0xffEFF1F4),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            color: isDarkMode ? const Color(0xff11161E) : const Color(0xffEFF1F4),
            padding: const EdgeInsets.only(top: 20, left: 20, right: 20),
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(bottom: 25),
                  alignment: Alignment.topRight,
                  child: InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Image.asset(
                      'assets/images/Group 36634_blue.png',
                      height: 22,
                      package: 'lock_to_win',
                    ),
                  ),
                ),

                Text(
                  Get.locale?.languageCode.toString() == 'en' ? "LikeWallet invites you to join our “LOCK TO WIN“ activity." : "เชิญร่วมสนุกกับกิจกรรม “ล็อคทูวิน“ ลุ้นรับรางวัลสุดพิเศษจากไลค์วอลเลท",
                  style: TextStyle(
                    fontFamily: "IBMPlexSansThai-Medium",
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1,
                    fontSize: 14,
                    color: isDarkMode ? const Color(0xffE9ECF5) : const Color(0xff262F3E),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  Get.locale?.languageCode.toString() == 'en'
                      ? 'ltw_title_rule'.tr +' '+ 'ltw_game_rule'.tr
                      : 'ltw_game_rule'.tr +' '+ 'ltw_title_rule'.tr,
                  style: TextStyle(
                    fontFamily: "IBMPlexSansThai-Medium",
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1,
                    fontSize: 14,
                    color: isDarkMode ? const Color(0xffE9ECF5) : const Color(0xff262F3E),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                RichText(
                  text: TextSpan(
                      text: 'ltw_hot_to_get'.tr+' ',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        fontFamily: "IBMPlexSansThai-Regular",
                        color: Color(0xff0078FF),
                      ),
                      children: <TextSpan>[
                        TextSpan(
                          text: 'ltw_title_rule'.tr+' ',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1,
                            fontFamily: "IBMPlexSansThai-Medium",
                            color: isDarkMode
                                ? const Color(0xffEBEDFD)
                                : const Color(0xff0078FF),
                            fontSize: 14,
                          ),
                        ),
                        TextSpan(
                            text: Get.locale?.languageCode.toString() == 'en' ?
                            ' '+'ltw_tickets'.tr : '',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              fontFamily: "IBMPlexSansThai-Regular",
                              color: Color(0xff0078FF),
                            ))
                      ]),
                ),
                const SizedBox(
                  height: 5,
                ),
                Text(
                  'ltw_ticket_rule_detail'.tr,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.3,
                    fontFamily: "IBMPlexSansThai-Regular",
                    color: Color(0xff738097),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                Row(
                  children: [
                    Text(
                      'ltw_user_limit'.tr+': ',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        fontFamily: "IBMPlexSansThai-Regular",
                        color: Color(0xff0078FF),
                      ),
                    ),
                    Text(
                      'ltw_maximum_ticket'.tr+' ',
                      style: const TextStyle(
                        color: Color(0xff738097),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
                // Text('ltw_how_to_win'.tr+':',
                //     style: TextStyle(
                //       fontWeight: FontWeight.bold,
                //       fontSize: 14,
                //       fontFamily: "IBMPlexSansThai-Regular",
                //       color: Color(0xff0078FF),
                //     )),
                // SizedBox(
                //   height: 5,
                // ),
                // Text(
                //   'ltw_win_rule_detail1'.tr,
                //   style: TextStyle(
                //     color: Color(0xff738097),
                //     fontSize: 14,
                //     height: 1.3,
                //     fontFamily: "IBMPlexSansThai-Regular",
                //   ),
                // ),
                // SizedBox(
                //   height: 15,
                // ),
                // Text(
                //   'ltw_win_rule_detail2'.tr,
                //   style: TextStyle(
                //     color: Color(0xff738097),
                //     fontSize: 14,
                //     height: 1.3,
                //     fontFamily: "IBMPlexSansThai-Regular",
                //   ),
                // ),
                // SizedBox(
                //   height: 30,
                // ),
                Text(
                  'ltw_how_to_win'.tr+':',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    fontFamily: "IBMPlexSansThai-Regular",
                    color: Color(0xff0078FF),
                  ),
                ),
                const SizedBox(
                  height: 5,
                ),
                Text(
                  'ltw_wining_ratio_detail'.tr,
                  style: const TextStyle(
                    color: Color(0xff738097),
                    fontSize: 14,
                    height: 1.3,
                    fontFamily: "IBMPlexSansThai-Regular",
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                RichText(
                  text: TextSpan(
                    text: 'ltw_note'.tr+' ',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      fontFamily: "IBMPlexSansThai-Regular",
                      color: Color(0xff0078FF),
                    ),
                    children: <TextSpan>[
                      TextSpan(
                          text:
                          'ltw_note_detail'.tr,
                          style: const TextStyle(
                            color: Color(0xff00CEFF),
                            fontSize: 14,
                            fontWeight: FontWeight.normal,
                            height: 1.3,
                            fontFamily: "IBMPlexSansThai-Regular",
                          )),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 40,
                ),
                Text(
                  Get.locale?.languageCode.toString() == 'en'
                      ? 'ltw_title_rule'.tr+' '
                      +'ltw_game_phase'.tr +':'
                      :'ltw_game_phase'.tr+' '
                      +'ltw_title_rule'.tr +':',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    fontFamily: "IBMPlexSansThai-Regular",
                    color: Color(0xff0078FF),
                  ),
                ),
                const SizedBox(
                  height: 5,
                ),
                Text(
                  'ltw_game_phase_detail'.tr,
                  style: const TextStyle(
                    color: Color(0xff738097),
                    fontSize: 14,
                    height: 1.3,
                    fontFamily: "IBMPlexSansThai-Regular",
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Text(
                  'ltw_how_draw'.tr,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    fontFamily: "IBMPlexSansThai-Regular",
                    color: Color(0xff0078FF),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Text(
                  'ltw_how_draw_detail'.tr,
                  style: const TextStyle(
                    color: Color(0xff738097),
                    fontSize: 14,
                    height: 1.3,
                    fontFamily: "IBMPlexSansThai-Regular",
                  ),
                ),
                const SizedBox(
                  height: 40,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}