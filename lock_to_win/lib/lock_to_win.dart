import 'dart:async';

import 'package:flutter/services.dart';
import 'package:get/route_manager.dart';
import 'package:lock_to_win/main.dart';

class LockToWin {
  static const MethodChannel _channel = MethodChannel('lock_to_win');

  static Future<String?> get platformVersion async {
    final String? version = await _channel.invokeMethod('getPlatformVersion');
    return version;
  }

  static void startLockToWin({required Map<String, Object> data}) async {
    print("startLockToWin");
    print(data);
    Get.to(() => LockToWinMain(data: data["data"]));
  }
}
