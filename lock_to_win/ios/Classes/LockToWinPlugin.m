#import "LockToWinPlugin.h"
#if __has_include(<lock_to_win/lock_to_win-Swift.h>)
#import <lock_to_win/lock_to_win-Swift.h>
#else
// Support project import fallback if the generated compatibility header
// is not copied when this plugin is created as a library.
// https://forums.swift.org/t/swift-static-libraries-dont-copy-generated-objective-c-header/19816
#import "lock_to_win-Swift.h"
#endif

@implementation LockToWinPlugin
+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
  [SwiftLockToWinPlugin registerWithRegistrar:registrar];
}
@end
