import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:lock_to_win/lock_to_win.dart';

void main() {
  const MethodChannel channel = MethodChannel('lock_to_win');

  TestWidgetsFlutterBinding.ensureInitialized();

  setUp(() {
    channel.setMockMethodCallHandler((MethodCall methodCall) async {
      return '42';
    });
  });

  tearDown(() {
    channel.setMockMethodCallHandler(null);
  });

  test('getPlatformVersion', () async {
    expect(await LockToWin.platformVersion, '42');
  });
}
