<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="110" height="109.998" viewBox="0 0 110 109.998">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#1080fe"/>
      <stop offset="1" stop-color="#4065ff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#0effd4"/>
      <stop offset="1" stop-color="#00ceff"/>
    </linearGradient>
  </defs>
  <g id="rewards" transform="translate(-169 -1461)">
    <g id="Group_34806" data-name="Group 34806" transform="translate(169 1461)">
      <g id="Group_34790" data-name="Group 34790" transform="translate(14.503 11.489)">
        <g id="Group_23635" data-name="Group 23635" transform="translate(0 0)">
          <g id="Group_23633" data-name="Group 23633">
            <path id="Path_7980" data-name="Path 7980" d="M276.7,408.9v2.728a6.983,6.983,0,0,0,2.33,13.566,2.33,2.33,0,0,1,.237,4.655q-.118.006-.237,0a4.21,4.21,0,0,1-3.022-1.76,2.33,2.33,0,0,0-3.564,3,9.652,9.652,0,0,0,4.258,3.032v2.717a2.33,2.33,0,0,0,4.655,0V434.1a6.983,6.983,0,0,0-2.33-13.566,2.33,2.33,0,0,1,0-4.656,3.736,3.736,0,0,1,2.517,1.217,2.33,2.33,0,1,0,3.4-3.191c-.051-.053-.1-.105-.159-.154a9,9,0,0,0-3.428-2.142V408.9a2.33,2.33,0,0,0-4.655-.237q-.006.118,0,.237Z" transform="translate(-250.459 -347.795)" fill="#1d2532"/>
            <path id="Path_26742" data-name="Path 26742" d="M-500.551,436.717a1.852,1.852,0,0,0,.1,2.595,1.8,1.8,0,0,0,2.47,0,1.852,1.852,0,0,0-.078-2.6,1.8,1.8,0,0,0-2.493.006Zm17.237,25.9a28.569,28.569,0,0,1-19.546,4.395q.057.818.059,1.649v1.611q.307.038.615.071,1.569.155,3.139.156a31.881,31.881,0,0,0,17.46-5.206,1.61,1.61,0,0,0,.49-2.206,1.568,1.568,0,0,0-2.179-.5l-.04.026Zm1.225-23.23a1.587,1.587,0,0,0,1.577-1.6,1.587,1.587,0,0,0-1.577-1.6h-1.468a1.587,1.587,0,0,0-1.577,1.6,1.587,1.587,0,0,0,1.577,1.6Zm-15.331-17.206V420.7a1.593,1.593,0,0,0-1.617-1.57,1.6,1.6,0,0,0-1.551,1.57v1.478a1.593,1.593,0,0,0,1.617,1.569A1.593,1.593,0,0,0-497.42,422.177Zm-3.209,31.18v1.479a1.593,1.593,0,0,0,1.617,1.569,1.593,1.593,0,0,0,1.55-1.569v-1.479a1.593,1.593,0,0,0-1.617-1.57A1.6,1.6,0,0,0-500.629,453.357Zm13.864-25.687-.23.232-7.615,7.674a5.01,5.01,0,0,1-2.237,6.672,4.9,4.9,0,0,1-6.591-2.264,5.039,5.039,0,0,1,0-4.408l-3.511-3.538a1.605,1.605,0,0,1-.06-2.251l.006-.007a1.576,1.576,0,0,1,2.24-.054c.018.017.036.036.054.054l3.51,3.538a4.874,4.874,0,0,1,4.347,0l7.844-7.907a1.576,1.576,0,0,1,2.24,0,1.606,1.606,0,0,1,.008,2.251Zm19.576,6.892a32.246,32.246,0,0,0-25.08-28.255l-3.2-.543a32.628,32.628,0,0,0-7.737.006l-3.2.549a32.229,32.229,0,0,0-18.451,11.723c-.04,0-.08-.006-.121-.006h-11.39a1.587,1.587,0,0,0-1.577,1.6,1.587,1.587,0,0,0,1.577,1.6h9.335a31.948,31.948,0,0,0-2.836,6.136h-17.393a1.587,1.587,0,0,0-1.577,1.6,1.587,1.587,0,0,0,1.577,1.6h16.49q-.359,1.574-.559,3.178h3.221a28.893,28.893,0,0,1,8.225-16.512,29.143,29.143,0,0,1,41.134,0,29.155,29.155,0,0,1,3.849,36.344,1.6,1.6,0,0,0,.452,2.194l.006,0,.012.007a1.6,1.6,0,0,0,2.211-.469A32.318,32.318,0,0,0-467.189,434.562Zm-7.7,7.835a24.85,24.85,0,0,0-19.56-29.062,24.584,24.584,0,0,0-28.712,19.8q-.056.3-.105.6h3.216a21.385,21.385,0,0,1,24.928-17.286,21.612,21.612,0,0,1,17.076,25.233,21.383,21.383,0,0,1-24.927,17.285q-.992-.187-1.962-.469a22.865,22.865,0,0,1,1.317,3.594A24.58,24.58,0,0,0-474.887,442.4Z" transform="translate(562.529 -405.537)" fill="url(#linear-gradient)"/>
            <path id="Path_63726" data-name="Path 63726" d="M-4237.25,928.01a3.528,3.528,0,0,1-1.135,4.857,3.522,3.522,0,0,1-1.865.53h-50.528a3.531,3.531,0,0,1-3.527-3.537h0a3.536,3.536,0,0,1,.526-1.85l3.38-5.418a5.905,5.905,0,0,0,.637-2.686v-16.7a19.661,19.661,0,0,1,19.7-19.615h0l-9.457-10.563h27.341l-8.794,10.563h0a19.662,19.662,0,0,1,19.7,19.619V919.9a5.906,5.906,0,0,0,.637,2.686Zm-30.676-33.783v2.728a6.984,6.984,0,0,0-4.254,8.913,6.983,6.983,0,0,0,6.584,4.653,2.332,2.332,0,0,1,2.446,2.209,2.331,2.331,0,0,1-2.209,2.446,2.367,2.367,0,0,1-.237,0,4.2,4.2,0,0,1-3.022-1.76,2.33,2.33,0,0,0-3.289-.2,2.33,2.33,0,0,0-.275,3.2,9.645,9.645,0,0,0,4.258,3.032v2.717a2.33,2.33,0,0,0,2.435,2.22,2.331,2.331,0,0,0,2.22-2.22v-2.738a6.981,6.981,0,0,0,4.253-8.912,6.983,6.983,0,0,0-6.583-4.654,2.33,2.33,0,0,1-2.232-2.424,2.332,2.332,0,0,1,2.232-2.232,3.738,3.738,0,0,1,2.517,1.217,2.331,2.331,0,0,0,3.295.1,2.332,2.332,0,0,0,.1-3.3h0a1.947,1.947,0,0,0-.159-.154,9.019,9.019,0,0,0-3.428-2.142v-2.708a2.332,2.332,0,0,0-2.209-2.446,2.33,2.33,0,0,0-2.446,2.209h0a2.132,2.132,0,0,0,0,.237Z" transform="translate(4294.305 -834.886)" fill="url(#linear-gradient-2)"/>
          </g>
        </g>
      </g>
      <g id="Group_34798" data-name="Group 34798">
        <path id="Path_63694" data-name="Path 63694" d="M3.264,1.633A1.632,1.632,0,1,0,1.632,3.265,1.633,1.633,0,0,0,3.264,1.633Z" transform="translate(3.266 14.067) rotate(90)" fill="#cad2de"/>
        <ellipse id="Ellipse_4010" data-name="Ellipse 4010" cx="1.632" cy="1.632" rx="1.632" ry="1.632" transform="translate(3.265 28.133) rotate(90)" fill="#cad2de"/>
        <ellipse id="Ellipse_4016" data-name="Ellipse 4016" cx="1.632" cy="1.632" rx="1.632" ry="1.632" transform="translate(3.265 42.199) rotate(90)" fill="#cad2de"/>
        <ellipse id="Ellipse_4013" data-name="Ellipse 4013" cx="1.632" cy="1.632" rx="1.632" ry="1.632" transform="translate(3.265 0.001) rotate(90)" fill="#cad2de"/>
        <path id="Path_63695" data-name="Path 63695" d="M3.264,1.632A1.632,1.632,0,1,0,1.632,3.264,1.632,1.632,0,0,0,3.264,1.632Z" transform="translate(18.423 14.067) rotate(90)" fill="#cad2de"/>
        <ellipse id="Ellipse_4014" data-name="Ellipse 4014" cx="1.632" cy="1.632" rx="1.632" ry="1.632" transform="translate(18.423) rotate(90)" fill="#cad2de"/>
      </g>
    </g>
  </g>
</svg>
