<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="110.41" height="109.678" viewBox="0 0 110.41 109.678">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#1080fe"/>
      <stop offset="1" stop-color="#4065ff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#0effd4"/>
      <stop offset="1" stop-color="#00ceff"/>
    </linearGradient>
  </defs>
  <g id="lockAndEarn" transform="translate(-323.543 -207.623)">
    <g id="Group_34796" data-name="Group 34796">
      <circle id="Ellipse_4010-2" data-name="Ellipse 4010-2" cx="1.632" cy="1.632" r="1.632" transform="translate(351.677 300.752)" fill="#cad2de"/>
      <path id="Path_63695-2" data-name="Path 63695-2" d="M337.609,302.386a1.632,1.632,0,1,1,1.632,1.632h0A1.632,1.632,0,0,1,337.609,302.386Z" fill="#cad2de"/>
      <path id="Path_63696" data-name="Path 63696" d="M337.609,286.758a1.632,1.632,0,1,1,1.632,1.632h0A1.632,1.632,0,0,1,337.609,286.758Z" fill="#cad2de"/>
      <circle id="Ellipse_4011" data-name="Ellipse 4011" cx="1.632" cy="1.632" r="1.632" transform="translate(323.543 270.124)" fill="#cad2de"/>
      <circle id="Ellipse_4012" data-name="Ellipse 4012" cx="1.632" cy="1.632" r="1.632" transform="translate(323.543 285.124)" fill="#cad2de"/>
      <path id="Ellipse_4014-2" data-name="Ellipse 4014-2" d="M351.676,302.384" fill="#cad2de"/>
      <circle id="Ellipse_4015" data-name="Ellipse 4015" cx="1.632" cy="1.632" r="1.632" transform="translate(351.676 285.124)" fill="#cad2de"/>
    </g>
    <path id="Path_63848" data-name="Path 63848" d="M358.246,264.8l-.039-.027-.023-.014a1.572,1.572,0,0,0-2.161.516,1.614,1.614,0,0,0,.491,2.21,31.939,31.939,0,0,0,17.492,5.215q1.573,0,3.145-.156c.206-.022.41-.046.616-.071v-1.612q0-.834.059-1.652a28.635,28.635,0,0,1-19.582-4.4Zm-8.173-19.9.018.1A24.582,24.582,0,0,0,378.8,264.6,22.742,22.742,0,0,1,380.121,261q-.971.282-1.962.469l-.131.023a21.381,21.381,0,0,1-24.8-17.308,21.611,21.611,0,0,1,17.076-25.233l.128-.024a21.386,21.386,0,0,1,24.8,17.31h3.216q-.049-.3-.105-.6c-.01-.052-.019-.1-.029-.154a24.582,24.582,0,0,0-28.683-19.646,24.849,24.849,0,0,0-19.56,29.062Zm22.462,4.666v-6.041h4.673v-2.609h-4.671v-1.93h4.671V236.3h-4.671v-3.136h5.812v-4.079H368.5v7.223H364.27V239h4.221v1.929H364.27v2.609h4.221v10.122h17.99v-4.085Zm51.349-18.453a1.59,1.59,0,0,1-1.577,1.6H405.783a32.064,32.064,0,0,1,.56,3.181h-3.227a29.13,29.13,0,1,0-53.3,19.868,1.6,1.6,0,0,1-.452,2.2l-.013.007,0,0a1.6,1.6,0,0,1-2.213-.472,32.383,32.383,0,0,1-5.042-20.794l0,0a32.481,32.481,0,0,1,.68-3.994H328.112a1.6,1.6,0,0,1,0-3.2h15.544a32.175,32.175,0,0,1,2.864-6.151h-7.5a1.6,1.6,0,0,1,0-3.2h9.666A32.258,32.258,0,0,1,367.216,208.4l3.206-.544a32.617,32.617,0,0,1,7.75.006l3.207.549a32.3,32.3,0,0,1,18.484,11.756h11.528a1.6,1.6,0,0,1,0,3.2h-9.355a32.023,32.023,0,0,1,2.841,6.151H422.3A1.591,1.591,0,0,1,423.884,231.113Z" fill="url(#linear-gradient)"/>
    <path id="Path_63847" data-name="Path 63847" d="M426.849,314.058l6.985-26.1c.664-2.48-1.507-5.236-4.894-6.324L432,270.2a19.547,19.547,0,0,0-13.826-23.937h0a19.547,19.547,0,0,0-23.938,13.824h0l-3.062,11.443c-3.483-.752-6.74.551-7.407,3.029l-6.984,26.1c-.694,2.59,1.7,5.487,5.363,6.463l18.413,4.929h0l18.413,4.93C422.63,317.954,426.155,316.648,426.849,314.058ZM410.06,276.566l-12-3.213,3.061-11.42a12.413,12.413,0,0,1,15.161-8.846h0a12.413,12.413,0,0,1,8.845,15.162h0l-.027.1-3.058,11.426Zm1.661,13.339a5.262,5.262,0,0,1-4.112,3.81l-2.015,7.511a2.274,2.274,0,0,1-2.783,1.61h0c-.044-.012-.087-.025-.129-.041a2.264,2.264,0,0,1-1.462-2.726l2.01-7.508a5.262,5.262,0,0,1-.6-7.417h0a5.267,5.267,0,0,1,5.385-1.669c2.8.755,3.706,6.433,3.706,6.433Z" fill="url(#linear-gradient-2)"/>
  </g>
</svg>
