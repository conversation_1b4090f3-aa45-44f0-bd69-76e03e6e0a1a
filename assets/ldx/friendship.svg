<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="112.813" height="114.5" viewBox="0 0 112.813 114.5">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#374452"/>
      <stop offset="1"/>
    </linearGradient>
    <filter id="Path_5414" x="54.738" y="0.093" width="58.075" height="67.313" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1.5" result="blur"/>
      <feFlood flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-2" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#212830"/>
      <stop offset="1"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ba00ff"/>
      <stop offset="1" stop-color="#8900ff"/>
    </linearGradient>
    <filter id="Path_5416" x="1.887" y="0.739" width="62.979" height="45.096" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1.5" result="blur-2"/>
      <feFlood flood-opacity="0.302"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_5418" x="8.049" y="61.577" width="103.402" height="41.728" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1.5" result="blur-3"/>
      <feFlood flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-3"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_5421" x="72.773" y="50.831" width="22.733" height="30.459" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1.5" result="blur-4"/>
      <feFlood flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-4"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_5422" x="55.281" y="13.047" width="56.986" height="55.492" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1.5" result="blur-5"/>
      <feFlood flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-5"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_5424" x="3.242" y="61.577" width="55.351" height="41.728" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1.5" result="blur-6"/>
      <feFlood flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-6"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_5426" x="19.152" y="50.845" width="22.733" height="29.707" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1.5" result="blur-7"/>
      <feFlood flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-7"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_5427" x="2.426" y="18.787" width="56.986" height="48.938" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1.5" result="blur-8"/>
      <feFlood flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-8"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_5429" x="0" y="60.152" width="19.772" height="25.616" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1.5" result="blur-9"/>
      <feFlood flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-9"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="MR" x="24.481" y="60.5" width="73" height="54" filterUnits="userSpaceOnUse">
      <feOffset dy="-1" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="4" result="blur-10"/>
      <feFlood flood-color="#026a72"/>
      <feComposite operator="in" in2="blur-10"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="friendship-2" transform="translate(2.906)">
    <g transform="matrix(1, 0, 0, 1, -2.91, 0)" filter="url(#Path_5414)">
      <path id="Path_5414-2" data-name="Path 5414" d="M292.364,28.987h10.571a6.571,6.571,0,0,1,6.03,3.961l.675,1.561h3.19a9.8,9.8,0,0,1,9.8,9.8V80.34a6.96,6.96,0,0,1-6.96,6.96H280.514a6.96,6.96,0,0,1-6.96-6.96V47.8A18.81,18.81,0,0,1,292.364,28.987Z" transform="translate(-214.32 -27.39)" fill="url(#linear-gradient)"/>
    </g>
    <path id="Path_5415" data-name="Path 5415" d="M401.424,34.509h-3.19l-.676-1.561a6.57,6.57,0,0,0-6.03-3.961h-5.475a6.57,6.57,0,0,1,6.03,3.961l.676,1.561h3.19a9.8,9.8,0,0,1,9.8,9.8V80.34a6.96,6.96,0,0,1-6.96,6.96h5.475a6.96,6.96,0,0,0,6.96-6.96V44.308A9.8,9.8,0,0,0,401.424,34.509Z" transform="translate(-305.816 -27.394)" fill="url(#linear-gradient-2)"/>
    <g transform="matrix(1, 0, 0, 1, -2.91, 0)" filter="url(#Path_5416)">
      <path id="Path_5416-2" data-name="Path 5416" d="M56.954,36.969,36.906,32.2a6.327,6.327,0,0,0-5.938,1.681l-5.7,5.7H22.851a6.327,6.327,0,0,0-6.327,6.327s-.319,7.763,0,10.324c.379,3.043,3.01,11.891,3.01,11.891H62.291L70.3,36.415a1.753,1.753,0,0,0-2.791-1.8h0A12.361,12.361,0,0,1,56.954,36.969Z" transform="translate(-9.99 -29.79)" fill="url(#linear-gradient-3)"/>
    </g>
    <path id="Path_5417" data-name="Path 5417" d="M217.2,42.761h0a12.349,12.349,0,0,1-2.7,1.628c-.009.058-.014.114-.029.173l-8.012,31.709h5.518l8.012-31.709A1.753,1.753,0,0,0,217.2,42.761Z" transform="translate(-163.418 -37.935)" fill="#8226ff"/>
    <g transform="matrix(1, 0, 0, 1, -2.91, 0)" filter="url(#Path_5418)">
      <path id="Path_5418-2" data-name="Path 5418" d="M128.6,329H45.379v12.8h56.428v19.924h37.973V340.392A11.291,11.291,0,0,0,128.6,329Z" transform="translate(-32.83 -265.92)" fill="#13a89d"/>
    </g>
    <path id="Path_5419" data-name="Path 5419" d="M426.38,329H421.5a11.4,11.4,0,0,1,11.4,11.4v21.332h4.878V340.392A11.4,11.4,0,0,0,426.38,329Z" transform="translate(-333.732 -265.919)" fill="#007574"/>
    <path id="Path_5420" data-name="Path 5420" d="M98.617,341.8v-1.408a11.4,11.4,0,0,0-11.4-11.4H63.662a11.4,11.4,0,0,0-11.4,11.4V341.8Z" transform="translate(-41.16 -265.358)" fill="#007574"/>
    <g transform="matrix(1, 0, 0, 1, -2.91, 0)" filter="url(#Path_5421)">
      <path id="Path_5421-2" data-name="Path 5421" d="M356.714,275.109v13.84c0,3.792,6.866,7.619,6.866,7.619s6.866-3.827,6.866-7.619v-13.84Z" transform="translate(-279.44 -222.78)" fill="#f2a077"/>
    </g>
    <g transform="matrix(1, 0, 0, 1, -2.91, 0)" filter="url(#Path_5422)">
      <path id="Path_5422-2" data-name="Path 5422" d="M324.1,112.768v2.022a4.272,4.272,0,0,1-4.271,4.274h-1.45a18.308,18.308,0,0,1-21.066,17.126h0a18.3,18.3,0,0,1-15.473-17.126h-1.45a4.274,4.274,0,0,1-4.274-4.274v-2.022a4.274,4.274,0,0,1,4.274-4.274h1.422V99.219c0-.248.005-.494.018-.737a40.988,40.988,0,0,0,29.681-5.641,40.421,40.421,0,0,0,3.993-2.892,16.2,16.2,0,0,1,2.9,9.27v9.275h1.425A4.272,4.272,0,0,1,324.1,112.768Z" transform="translate(-216.33 -75.4)" fill="#fca"/>
    </g>
    <path id="Path_5423" data-name="Path 5423" d="M398.389,108.494h-1.424v-3.786h0c0-6.287,0-5.794,0-5.882a16.186,16.186,0,0,0-2.9-8.876,40.448,40.448,0,0,1-3.993,2.892,16.216,16.216,0,0,1,1.3,6.378v9.275l-.028,10.57a18.3,18.3,0,0,1-15.468,17.126,18.309,18.309,0,0,0,20.695-14.281h0v0q.144-.683.237-1.388c0-.014,0-.029.005-.043.026-.2.048-.4.067-.6,0-.051.01-.1.014-.151.019-.219.036-.438.047-.66h1.45a4.272,4.272,0,0,0,4.271-4.274v-2.022A4.272,4.272,0,0,0,398.389,108.494Z" transform="translate(-297.799 -75.402)" fill="#f2a077"/>
    <g transform="matrix(1, 0, 0, 1, -2.91, 0)" filter="url(#Path_5424)">
      <path id="Path_5424-2" data-name="Path 5424" d="M69.108,361.724H22.757V340.392a11.4,11.4,0,0,1,11.4-11.4H57.712a11.4,11.4,0,0,1,11.4,11.4Z" transform="translate(-15.01 -265.92)" fill="#00eae7"/>
    </g>
    <path id="Path_5425" data-name="Path 5425" d="M169.175,329H164.3a11.4,11.4,0,0,1,11.4,11.4v21.332h4.878V340.392A11.4,11.4,0,0,0,169.175,329Z" transform="translate(-130.063 -265.919)" fill="#21c5df"/>
    <g transform="matrix(1, 0, 0, 1, -2.91, 0)" filter="url(#Path_5426)">
      <path id="Path_5426-2" data-name="Path 5426" d="M99.509,275.109v13.84a6.866,6.866,0,0,0,6.866,6.866h0a6.866,6.866,0,0,0,6.866-6.866v-13.84Z" transform="translate(-75.86 -222.76)" fill="#f2a077"/>
    </g>
    <g transform="matrix(1, 0, 0, 1, -2.91, 0)" filter="url(#Path_5427)">
      <path id="Path_5427-2" data-name="Path 5427" d="M66.9,131.25V133.2a4.206,4.206,0,0,1-4.274,4.133h-1.45a17.251,17.251,0,0,1-3.133,9.011s0,0,0,0A18.723,18.723,0,0,1,40.113,153.9h0a17.934,17.934,0,0,1-15.473-16.561h-1.45a4.2,4.2,0,0,1-4.271-4.133V131.25a4.2,4.2,0,0,1,4.271-4.133h1.425v-6.505c4.908-.32,6.5-1.775,6.5-6.454,0,0,6.245,10.757,24.5,7.551a50.487,50.487,0,0,0,5.6-1.332v6.74h1.422A4.206,4.206,0,0,1,66.9,131.25Z" transform="translate(-11.99 -93.87)" fill="#fca"/>
    </g>
    <path id="Path_5428" data-name="Path 5428" d="M141.182,151.391H139.76v-6.97a49.115,49.115,0,0,1-5.6,1.377v5.593l-.028,10.57a18.3,18.3,0,0,1-15.468,17.126,18.492,18.492,0,0,0,17.931-7.8s0,0,0-.005a17.4,17.4,0,0,0,2.837-6.953h.01c.1-.53.173-1.069.226-1.613,0-.022,0-.043.007-.065.021-.228.04-.457.052-.687h1.45a4.274,4.274,0,0,0,4.274-4.274v-2.022A4.274,4.274,0,0,0,141.182,151.391Z" transform="translate(-94.003 -119.213)" fill="#f2a077"/>
    <g transform="matrix(1, 0, 0, 1, -2.91, 0)" filter="url(#Path_5429)">
      <path id="Path_5429-2" data-name="Path 5429" d="M15.354,336.382H11.473A3.973,3.973,0,0,1,7.5,332.409v-8.671a3.973,3.973,0,0,1,3.973-3.973h3.881a2.919,2.919,0,0,1,2.919,2.919v10.779A2.919,2.919,0,0,1,15.354,336.382Z" transform="translate(-3 -258.11)" fill="#fca"/>
    </g>
    <g id="Group_24668" data-name="Group 24668">
      <g id="Group_4052" data-name="Group 4052" opacity="0.501">
        <path id="Path_5430" data-name="Path 5430" d="M349.328,224.168a9.006,9.006,0,0,0,16.688,0,1.594,1.594,0,1,0-2.954-1.2,5.817,5.817,0,0,1-10.778,0,1.594,1.594,0,0,0-2.956,1.193Z" transform="translate(-276.458 -180.426)"/>
        <path id="Path_5431" data-name="Path 5431" d="M340.017,175.216a1.594,1.594,0,0,0,1.594-1.594v-.884a1.594,1.594,0,0,0-3.187,0v.884A1.594,1.594,0,0,0,340.017,175.216Z" transform="translate(-267.734 -140.098)"/>
        <path id="Path_5432" data-name="Path 5432" d="M426.225,175.216a1.594,1.594,0,0,0,1.594-1.594v-.884a1.594,1.594,0,0,0-3.187,0v.884A1.594,1.594,0,0,0,426.225,175.216Z" transform="translate(-336.197 -140.098)"/>
        <path id="Path_5433" data-name="Path 5433" d="M100.468,229.785a8.965,8.965,0,0,0,8.343-5.615,1.594,1.594,0,0,0-2.954-1.2,5.817,5.817,0,0,1-10.778,0,1.594,1.594,0,1,0-2.956,1.192,9,9,0,0,0,8.344,5.618Z" transform="translate(-72.839 -180.426)"/>
        <path id="Path_5434" data-name="Path 5434" d="M84.405,173.622v-.884a1.594,1.594,0,0,0-3.187,0v.884a1.594,1.594,0,1,0,3.187,0Z" transform="translate(-63.96 -140.098)"/>
        <path id="Path_5435" data-name="Path 5435" d="M169.02,175.216a1.594,1.594,0,0,0,1.594-1.594v-.884a1.594,1.594,0,0,0-3.187,0v.884A1.594,1.594,0,0,0,169.02,175.216Z" transform="translate(-132.454 -140.098)"/>
        <path id="Path_5436" data-name="Path 5436" d="M105.433,43.9A1.562,1.562,0,0,0,107,42.341V38.02A11.181,11.181,0,0,0,95.8,26.887H93.689l-.256-.588a8.027,8.027,0,0,0-7.369-4.809h-10.4A20.026,20.026,0,0,0,55.6,41.43v31.8a8.4,8.4,0,0,0,8.414,8.36h8.965v2.2H35.868V80.818a19.562,19.562,0,0,0,5.17-3.519,19.306,19.306,0,0,0,5.911-11.583,5.759,5.759,0,0,0,5.765-5.734V58.006a5.686,5.686,0,0,0-1.347-3.676l3.607-14.187a1.567,1.567,0,0,0-3.039-.763L48.6,52.513a5.772,5.772,0,0,0-1.483-.238V47.018a1.554,1.554,0,0,0-.636-1.252,1.575,1.575,0,0,0-1.391-.237c-8.589,2.618-16.118,2.48-21.77-.4a15.281,15.281,0,0,1-6-5.157,1.569,1.569,0,0,0-2.934.763c0,3.858-.829,4.694-4.927,4.968a1.562,1.562,0,0,0-1.462,1.554v5.019a5.771,5.771,0,0,0-1.641.286,50.428,50.428,0,0,1-1.229-5.42,97.907,97.907,0,0,1,.011-9.834c0-.021,0-.043,0-.064a4.645,4.645,0,0,1,4.654-4.625h2.376a1.573,1.573,0,0,0,1.108-.456l5.607-5.572a4.7,4.7,0,0,1,4.37-1.229l19.715,4.66a13.8,13.8,0,0,0,11.724-2.6.132.132,0,0,1,.183-.009.13.13,0,0,1,.067.168l-1.187,4.668a1.567,1.567,0,0,0,3.039.763l1.187-4.669A3.218,3.218,0,0,0,56.567,24.8a3.264,3.264,0,0,0-3.834.19,10.645,10.645,0,0,1-9.046,2l-19.714-4.66a7.872,7.872,0,0,0-7.313,2.056L11.512,29.5H9.785A7.774,7.774,0,0,0,2,37.21a102.028,102.028,0,0,0,.012,10.315,59.845,59.845,0,0,0,1.609,6.947,5.684,5.684,0,0,0-1.231,3.535v1.976a5.758,5.758,0,0,0,5.761,5.734,19.43,19.43,0,0,0,11.081,15.1v2.974H15.964a12.879,12.879,0,0,0-2.821.311,4.442,4.442,0,0,0-3.852-2.229H5.474A5.463,5.463,0,0,0,0,87.313v8.473a5.444,5.444,0,0,0,3.189,4.941v16.6a1.562,1.562,0,0,0,1.567,1.557H50.342a1.562,1.562,0,0,0,1.567-1.557V96.485A12.632,12.632,0,0,0,47.5,86.905H72.979v.777c0,4.492,6.785,8.379,7.559,8.807a1.575,1.575,0,0,0,1.526,0c.773-.428,7.556-4.315,7.556-8.807v-.777h3.264a9.621,9.621,0,0,1,9.64,9.579v19.288H98.132V105.338a1.567,1.567,0,0,0-3.135,0v10.435H67.6V97.86A1.562,1.562,0,0,0,66.037,96.3h-9.4a1.558,1.558,0,1,0,0,3.115H64.47V117.33a1.562,1.562,0,0,0,1.567,1.557h38.056a1.562,1.562,0,0,0,1.567-1.557V96.485A12.749,12.749,0,0,0,92.885,83.791H89.621v-2.2h8.968A8.4,8.4,0,0,0,107,73.231V49.695a1.567,1.567,0,0,0-3.135,0V53.22a5.755,5.755,0,0,0-3-.945V44.766a17.261,17.261,0,0,0-3.135-9.949,1.574,1.574,0,0,0-2.281-.315,38.876,38.876,0,0,1-24.5,8.7,38.973,38.973,0,0,1-7.323-.69,1.576,1.576,0,0,0-1.264.3,1.555,1.555,0,0,0-.6,1.146c-.013.253-.019.515-.019.8v7.509a5.758,5.758,0,0,0-3,.945V41.43A16.9,16.9,0,0,1,75.667,24.6h10.4a4.893,4.893,0,0,1,4.492,2.932l.665,1.526A1.568,1.568,0,0,0,92.66,30H95.8a8.052,8.052,0,0,1,8.069,8.018v4.321A1.562,1.562,0,0,0,105.433,43.9ZM11.145,64.078A1.563,1.563,0,0,0,9.58,62.6H8.154a2.629,2.629,0,0,1-2.633-2.619V58.006a2.629,2.629,0,0,1,2.633-2.619h1.4a1.562,1.562,0,0,0,1.567-1.557V48.661c3.266-.463,5.1-1.728,5.9-4.192a20.283,20.283,0,0,0,4.654,3.326c7.319,3.829,15.84,2.972,22.3,1.286V53.83a1.562,1.562,0,0,0,1.567,1.557h1.4a2.63,2.63,0,0,1,2.636,2.619v1.976A2.63,2.63,0,0,1,46.943,62.6H45.517a1.563,1.563,0,0,0-1.565,1.477,16.21,16.21,0,0,1-5.076,10.966A16.409,16.409,0,0,1,33.7,78.361l-.029.012a16.587,16.587,0,0,1-12.248,0c-.029-.013-.06-.024-.09-.035A16.312,16.312,0,0,1,11.145,64.078Zm16.4,18.582a19.725,19.725,0,0,0,5.183-.695v5.716a5.1,5.1,0,0,1-1.518,3.644,5.193,5.193,0,0,1-8.852-3.644V81.967A19.718,19.718,0,0,0,27.55,82.661ZM3.135,87.313a2.335,2.335,0,0,1,2.34-2.325H9.291a1.3,1.3,0,0,1,1.3,1.295V96.816a1.3,1.3,0,0,1-1.3,1.295H5.474a2.335,2.335,0,0,1-2.34-2.325Zm45.64,9.172v19.288H44.381V105.338a1.567,1.567,0,0,0-3.135,0v10.435H13.852V105.338a1.567,1.567,0,0,0-3.135,0v10.435H6.324V101.226H9.291a4.429,4.429,0,0,0,4.438-4.41v-9.65a9.717,9.717,0,0,1,2.235-.26h3.264v.777a8.332,8.332,0,0,0,14.2,5.846,8.187,8.187,0,0,0,2.436-5.846v-.777h3.266A9.621,9.621,0,0,1,48.775,96.485Zm37.711-8.8c0,1.726-2.821,4.166-5.185,5.636-2.366-1.471-5.187-3.91-5.187-5.636V81.967a19.718,19.718,0,0,0,10.372,0Zm17.379-14.451a5.267,5.267,0,0,1-5.277,5.246H93.425q.706-.555,1.366-1.177a19.318,19.318,0,0,0,5.91-11.583,5.759,5.759,0,0,0,3.164-.947v8.462ZM59.27,58.006a2.63,2.63,0,0,1,2.636-2.619h1.4a1.562,1.562,0,0,0,1.567-1.557V45.885a42.382,42.382,0,0,0,6.07.436,42.059,42.059,0,0,0,25.084-8.312,14.169,14.169,0,0,1,1.7,6.757V53.83a1.562,1.562,0,0,0,1.567,1.557h1.4a2.629,2.629,0,0,1,2.633,2.619v1.976A2.629,2.629,0,0,1,100.7,62.6H99.269A1.563,1.563,0,0,0,97.7,64.078a16.218,16.218,0,0,1-5.076,10.966,16.411,16.411,0,0,1-5.145,3.306l-.082.035a16.594,16.594,0,0,1-12.222-.01c-.03-.013-.061-.025-.093-.036A16.312,16.312,0,0,1,64.9,64.079,1.563,1.563,0,0,0,63.332,62.6H61.906a2.63,2.63,0,0,1-2.636-2.619V58.006Zm2.629,7.71a19.353,19.353,0,0,0,7.273,12.76H64.014a5.268,5.268,0,0,1-5.279-5.246V64.77A5.761,5.761,0,0,0,61.9,65.716Z" transform="translate(0 -21.49)"/>
      </g>
    </g>
    <g transform="matrix(1, 0, 0, 1, -2.91, 0)" filter="url(#MR)">
      <text id="MR-2" data-name="MR" transform="translate(36.48 96.5)" fill="#bbfdf1" font-size="30" font-family="Helvetica" letter-spacing="0.05em"><tspan x="0" y="0">MR</tspan></text>
    </g>
  </g>
</svg>
