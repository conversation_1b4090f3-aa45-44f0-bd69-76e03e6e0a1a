<svg id="Group_24612" data-name="Group 24612" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="223" height="263.502" viewBox="0 0 223 263.502">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#00ffeb" stop-opacity="0.749"/>
      <stop offset="1" stop-color="#9d00ff" stop-opacity="0.745"/>
    </linearGradient>
    <clipPath id="clip-path">
      <rect id="Rectangle_1614" data-name="Rectangle 1614" width="223" height="263.502" stroke="#00ffce" stroke-width="1" fill="url(#linear-gradient)"/>
    </clipPath>
    <linearGradient id="linear-gradient-2" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="aqua"/>
      <stop offset="1" stop-color="#0e4d60"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fffd00"/>
      <stop offset="0.626" stop-color="#fffd00" stop-opacity="0.294"/>
      <stop offset="1" stop-color="#ffe36b" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#374452"/>
      <stop offset="1"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#6b82ff"/>
      <stop offset="0.427" stop-color="#a46bff" stop-opacity="0.302"/>
      <stop offset="1" stop-color="#6bffd9" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <g id="Group_24611" data-name="Group 24611" clip-path="url(#clip-path)">
    <path id="Path_42652" data-name="Path 42652" d="M118.493,97.225a.906.906,0,0,1-.727.952c-1.039.079-2.288.154-3.69.245-3.308-8.647-6.876-15.516-5.94-12.834a44.625,44.625,0,0,1,2.314,13.055c-1.627.094-5.4.275-7.288.366C100.289,83.784,96.6,72.5,96.834,75.98c.929,14.053.1,18.57-.867,23.4-17.587.831-40.653,1.667-57.342,1.912-1.931-8.415-3.62-17.526-3.912-16.888-2.273,4.76-2.89,11.659-2.939,16.973-5.312.04-9.56-.026-12.147-.2,0,0-1.244-7.077-1.423-9.405A48.82,48.82,0,0,1,20.06,74.039c1.763-5.9,4.816-11.113,7.711-16.458a.523.523,0,0,1,.072-.117c-.006-.016.011-.022.022-.045A50.128,50.128,0,0,0,32.3,46.436a.28.28,0,0,0,.029-.084,48.622,48.622,0,0,0,1.37-8.435c.771-10.4,6.6-19.392,16.322-22.146C68.384-1.619,86.79,5.671,92.352,19c.536,1.3,1.056,2.786,1.561,4.41l.014.052c1.885,6,3.572,13.8,5.132,19.824a.024.024,0,0,0,.009.035c-.011.022-.007.039.019.068l0,.016a57.2,57.2,0,0,0,2.186,6.959c4.642,10.841,15.251,16.992,17.216,46.866" transform="translate(7.822 2.437)" stroke="#e0b5ff" stroke-linejoin="round" stroke-width="0.7"/>
    <path id="Path_42653" data-name="Path 42653" d="M46.422,14.655C40.04,18,36.128,22.889,35.142,30.479c-.575,4.423-.866,8.739-2.554,12.916-1.743,4.31-4.505,7.846-7.342,11.379-4.51,5.613-7.655,11.2-8.351,18.647-.71,7.564-.344,16.487,2.2,23.539a.033.033,0,0,0,.063-.02c-2.063-7.152-2.12-17.056-.854-24.552,1.39-8.233,6.029-13.418,10.773-19.68A34.426,34.426,0,0,0,36.043,35.9c.474-3.751.417-7.653,1.918-11.188,1.792-4.219,5.367-6.906,8.912-9.29a.445.445,0,0,0-.452-.766" transform="translate(7.185 6.422)" fill="url(#linear-gradient-2)"/>
    <path id="Path_42654" data-name="Path 42654" d="M89.088,56.537c-4.505-5.888-9.2-10.906-11.839-18.216-2.405-6.661-3.783-13.7-6.233-20.353a.085.085,0,1,0-.162.05c3.776,11.814,4.51,23.738,12.111,33.668,4.131,5.4,8.26,10.4,10.985,16.868a57.949,57.949,0,0,1,4.286,21.368c.006.262.34.246.363,0a47.919,47.919,0,0,0-9.511-33.381" transform="translate(30.711 7.877)" fill="url(#linear-gradient-2)"/>
    <path id="Path_42655" data-name="Path 42655" d="M.027,153.619c.042-.292.1-.57.152-.862-.069.361-.138.71-.179,1.071.013-.069.013-.14.027-.209" transform="translate(0 67.196)" fill="#ffc727"/>
    <path id="Path_42656" data-name="Path 42656" d="M33.5,159.247a3.9,3.9,0,0,1-.5.264l-.069-.124-.014-.014c.166-.042.347-.069.515-.111a.124.124,0,0,0,.069-.014" transform="translate(14.267 70.051)" fill="#ffc727"/>
    <path id="Path_42657" data-name="Path 42657" d="M65.629,73.56C35.761,103.971-1.813,167.942,5.191,180.911c7.8,14.445,49.393-28.187,49.234-30.677-.122-1.882,14.51-40.739,25.609-69C91.123,53,73.014,66.039,65.629,73.56" transform="translate(1.88 28.116)" fill="#eb9481"/>
    <path id="Path_42658" data-name="Path 42658" d="M54.989,150.418c1.271-4.154,21.18-55.394,26.872-71.6s.416-21.218-15.84-6.377S14.144,136.864,6.013,163.176c-7.833,25.348,5.159,25.348,22.3,13.665,14.035-9.567,26.674-26.423,26.674-26.423" transform="translate(1.594 27.979)" stroke="#00ffce" stroke-width="1" fill="url(#linear-gradient-4)"/>
    <path id="Path_42659" data-name="Path 42659" d="M82.71,68.3c-.014.3-.06.6-.1.92a1.043,1.043,0,0,1-.856.452,1.025,1.025,0,1,1,.96-1.372M48.829,89.745a1.026,1.026,0,1,0,1.021,1.025,1.024,1.024,0,0,0-1.021-1.025m16.462,0a1.026,1.026,0,1,0,1.021,1.025,1.024,1.024,0,0,0-1.021-1.025M57.06,79.151a1.026,1.026,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027m16.462,0a1.026,1.026,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027M32.367,111.867a1.026,1.026,0,1,0,1.021,1.025,1.024,1.024,0,0,0-1.021-1.025m16.462,0a1.026,1.026,0,1,0,1.021,1.025,1.024,1.024,0,0,0-1.021-1.025m16.462,0a1.026,1.026,0,1,0,1.021,1.025,1.024,1.024,0,0,0-1.021-1.025M40.6,101.272a1.026,1.026,0,1,0,1.021,1.025,1.024,1.024,0,0,0-1.021-1.025m16.462,0A1.026,1.026,0,1,0,58.08,102.3a1.024,1.024,0,0,0-1.021-1.025M32.367,133.986a1.027,1.027,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027m16.462,0a1.027,1.027,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027M24.136,123.391a1.027,1.027,0,1,0,1.022,1.027,1.025,1.025,0,0,0-1.022-1.027m16.462,0a1.027,1.027,0,1,0,1.021,1.027,1.025,1.025,0,0,0-1.021-1.027m16.462,0a1.027,1.027,0,1,0,1.021,1.027,1.025,1.025,0,0,0-1.021-1.027M15.9,156.107a1.026,1.026,0,1,0,1.022,1.027,1.024,1.024,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027m-8.231-10.595a1.026,1.026,0,1,0,1.022,1.027,1.024,1.024,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027M15.9,178.228a1.026,1.026,0,1,0,1.022,1.027,1.024,1.024,0,0,0-1.022-1.027M7.674,167.634A1.026,1.026,0,1,0,8.7,168.66a1.024,1.024,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.022,1.027,1.024,1.024,0,0,0-1.022-1.027" transform="translate(2.884 29.747)" fill="#fff"/>
    <path id="Path_42660" data-name="Path 42660" d="M139.64,182.892A100.832,100.832,0,0,1,31.8,180.946l.225-1.343,4.295-31.885s-10.092-17.706-4.22-30.406c2.959-6.426,28.89-46.455,39.822-54.4,10.241-7.466,25.106.528,30.646,6.38,19.551,20.694,29.641,60.105,37.074,113.6" transform="translate(13.118 26.342)" fill="url(#linear-gradient-5)"/>
    <path id="Path_42661" data-name="Path 42661" d="M32.255,169.963c.535-5.338,1.1-10.67,1.733-15.994.648-5.322,1.218-10.654,1.932-15.967l.123.467a35.886,35.886,0,0,1-5.856-14.657,35.166,35.166,0,0,1-.34-7.938,29.075,29.075,0,0,1,1.955-7.716,76.386,76.386,0,0,1,7.378-13.822,123.733,123.733,0,0,1,9.306-12.514c-3.05,4.2-5.892,8.549-8.578,12.973a89.789,89.789,0,0,0-6.915,13.817,27.889,27.889,0,0,0-1.785,7.368,36.745,36.745,0,0,0,.378,7.607,34.664,34.664,0,0,0,5.45,14.117l.156.227-.033.239c-.714,5.313-1.573,10.606-2.354,15.909s-1.659,10.595-2.553,15.883" transform="translate(12.902 35.992)" fill="url(#linear-gradient-2)"/>
    <path id="Path_42662" data-name="Path 42662" d="M36.79,92.941a8.156,8.156,0,0,1,.373-2.114,16.07,16.07,0,0,1,.721-2.007,18.8,18.8,0,0,1,.952-1.9,14.3,14.3,0,0,1,1.175-1.781,32.322,32.322,0,0,1-1.471,3.968c-.558,1.287-1.157,2.537-1.75,3.834" transform="translate(15.948 37.451)"/>
    <path id="Path_42663" data-name="Path 42663" d="M46.868,118.013a65.767,65.767,0,0,1,17.047,2.854c5.563,1.582,17.863,6.477,20.95,7.683a.065.065,0,0,1-.037.124,105.484,105.484,0,0,1-11.842-3.58c-5.46-1.865-10.96-3.549-16.541-5.005a77.283,77.283,0,0,0-9.586-1.738.169.169,0,0,1,.009-.338" transform="translate(20.244 51.913)" fill="url(#linear-gradient-2)"/>
    <path id="Path_42664" data-name="Path 42664" d="M39.757,120.122a52.628,52.628,0,0,1,17.91,1.6c.069.017.053.137-.02.128-2.969-.356-5.925-.706-8.9-.956s-5.995-.4-8.991-.6a.081.081,0,0,1,0-.163" transform="translate(17.199 52.752)" fill="url(#linear-gradient-2)"/>
    <path id="Path_42665" data-name="Path 42665" d="M5.071,154.652c6.723,9.659,25.023,11.5,70.669,20.482,6.317,1.243,27.041-41.174,20.364-42.848-8.412-2.111-41.5-8.469-43.983-8.628C31.216,122.325.511,148.1,5.071,154.652" transform="translate(2.001 54.374)" fill="#eb9481"/>
    <path id="Path_42666" data-name="Path 42666" d="M71.565,128.182s19.338,2.936,29.022,7.637c12.969,6.3,21.584,17.093,24.922,21.837,6.97,9.9,17,38.686,9.255,43.035-7.014,3.937-15.791-26.006-15.791-26.006s13.963,29.045,4.65,32.226-17.534-32.16-17.534-32.16,13.681,30.167,3.561,31.921C102,208,92.76,176.411,92.76,176.411s11.581,26.01,3.773,27.714c-8.627,1.882-18.978-26.737-18.978-26.737-20.57.975-30.762-7.014-37.339-13.564-1.591-1.585,31.349-35.642,31.349-35.642" transform="translate(17.408 56.386)" fill="#feccaa" stroke="#333c45" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
    <path id="Path_42667" data-name="Path 42667" d="M92.261,153.3c1.763,3.352,3.571,6.733,5.032,10.232s2.8,7.05,3.931,10.668a65.167,65.167,0,0,1,3.062,14.5c-1.466-8.7-6.5-21.3-7.963-24.8s-2.739-7.055-4.2-10.55c-.037-.086.093-.13.135-.05" transform="translate(39.931 67.417)" fill="#263238" opacity="0"/>
    <path id="Path_42668" data-name="Path 42668" d="M83.413,156.817c2.067,4.379,4.279,8.648,6.154,13.12,1.865,4.449,3.248,8.835,4.805,13.4.119.353-.108.3-.242-.048-1.737-4.5-3.688-8.527-5.476-13.007-1.783-4.464-3.406-9.031-5.348-13.426-.03-.069.076-.108.108-.042" transform="translate(37.085 68.968)" fill="#263238" opacity="0"/>
    <path id="Path_42669" data-name="Path 42669" d="M75.32,158.481c3.5,6.528,5.8,13.7,7.883,20.785.1.353-.183.255-.311-.1-2.49-6.958-4.4-12.435-7.8-20.563-.053-.127.153-.259.226-.121" transform="translate(32.547 69.689)" fill="#263238" opacity="0"/>
    <path id="Path_42670" data-name="Path 42670" d="M91.111,130.219,51.117,174.953s-40.44-9.326-46.4-19.744c-3.922-6.858,26.522-34.635,49.509-31.4Z" transform="translate(1.897 54.349)" fill="url(#linear-gradient-4)"/>
    <path id="Path_42671" data-name="Path 42671" d="M30.9,130.1a1.021,1.021,0,1,1-1.021-1.027A1.023,1.023,0,0,1,30.9,130.1m15.441-1.027a1.027,1.027,0,1,0,1.021,1.027,1.025,1.025,0,0,0-1.021-1.027m16.462,0a1.027,1.027,0,1,0,1.021,1.027,1.025,1.025,0,0,0-1.021-1.027m16.461,0a1.027,1.027,0,1,0,1.022,1.027,1.025,1.025,0,0,0-1.022-1.027M13.416,151.2a1.026,1.026,0,1,0,1.022,1.027,1.024,1.024,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027m16.462,0a1.026,1.026,0,1,0,1.021,1.027A1.024,1.024,0,0,0,46.34,151.2m16.462,0a1.026,1.026,0,1,0,1.021,1.027A1.024,1.024,0,0,0,62.8,151.2M21.647,140.6a1.026,1.026,0,1,0,1.022,1.027,1.024,1.024,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027m16.462,0a1.026,1.026,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027m16.461,0a1.026,1.026,0,1,0,1.022,1.027,1.024,1.024,0,0,0-1.022-1.027M21.647,162.722a1.026,1.026,0,1,0,1.022,1.027,1.024,1.024,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027m16.466-.006a1.026,1.026,0,0,0,0,2.052l1.006-1.132v-.014a1.025,1.025,0,0,0-1.006-.906" transform="translate(5.372 56.779)" fill="#fff"/>
    <path id="Path_42672" data-name="Path 42672" d="M20.036,131.106c3.708-2.7,14.144-8.988,27.454-7.312.118.014.12.2,0,.2-10.034-.285-23.576,4.985-27.365,7.276-.1.06-.182-.1-.089-.166" transform="translate(8.668 54.333)" fill="#263238"/>
    <path id="Path_42673" data-name="Path 42673" d="M29.364,165.584c4.136-5.155,8.541-10.084,12.96-15s8.835-9.849,13.379-14.658c2.566-2.717,5.119-5.45,7.779-8.076.07-.069.2.03.138.109-4.126,5.149-8.516,10.1-12.9,15.038s-8.833,9.856-13.32,14.719c-2.536,2.746-5.122,5.449-7.781,8.076-.132.13-.38-.058-.259-.209" transform="translate(12.715 56.23)" fill="#263238"/>
    <path id="Path_42674" data-name="Path 42674" d="M77.042,129.647a57.041,57.041,0,0,0-7.5-1.715c-2.524-.475-5.056-.907-7.59-1.315-5.043-.812-15.635-3.112-24.945-3.086-.1,0,.424.5.528.5,4.432-.24,36.2,6.107,38.808,6.294a4.162,4.162,0,0,0-.28.533c-.054.13.075.3.212.2.327-.22.559-.5.867-.739a.376.376,0,0,0-.1-.672" transform="translate(16.038 54.341)" fill="#263238"/>
    <path id="Path_42675" data-name="Path 42675" d="M92.376,71.016c38.218,33.489,68.276,74.853,66.324,85.2-3.637,19.254-28.877,20.457-32.7,16.292-15.477-16.841-31.806-49.467-49.5-85.874C60.692,54.106,79.791,59.99,92.376,71.016" transform="translate(30.566 27.397)" stroke="#0089ff" stroke-width="1" fill="url(#linear-gradient-10)"/>
    <path id="Path_42676" data-name="Path 42676" d="M84.018,67.938A1.021,1.021,0,1,1,83,66.911a1.023,1.023,0,0,1,1.021,1.027M83,89.032a1.026,1.026,0,1,0,1.021,1.025A1.024,1.024,0,0,0,83,89.032m16.461,0a1.026,1.026,0,1,0,1.022,1.025,1.023,1.023,0,0,0-1.022-1.025M74.766,78.437a1.026,1.026,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027m16.461,0a1.026,1.026,0,1,0,1.022,1.027,1.024,1.024,0,0,0-1.022-1.027m8.231,32.716a1.026,1.026,0,1,0,1.022,1.025,1.023,1.023,0,0,0-1.022-1.025m16.462,0a1.026,1.026,0,1,0,1.022,1.025,1.023,1.023,0,0,0-1.022-1.025m16.107.055a1.033,1.033,0,0,0-.661.965,1.017,1.017,0,0,0,1.892.528c-.42-.5-.826-.995-1.231-1.493m-40.8-10.649a1.026,1.026,0,1,0,1.022,1.025,1.023,1.023,0,0,0-1.022-1.025m16.462,0a1.026,1.026,0,1,0,1.022,1.025,1.023,1.023,0,0,0-1.022-1.025m15.6.484a1.082,1.082,0,0,0-.149.543,1.014,1.014,0,0,0,1.021,1.025.951.951,0,0,0,.42-.091c-.42-.5-.856-.981-1.292-1.477M99.848,133.349c.211.377.406.755.615,1.116a.591.591,0,0,0,.016-.166,1.008,1.008,0,0,0-.631-.95m16.072-.076a1.026,1.026,0,1,0,1.022,1.027,1.024,1.024,0,0,0-1.022-1.027m16.462,0A1.026,1.026,0,1,0,133.4,134.3a1.025,1.025,0,0,0-1.022-1.027m16.071.076h-.014a1.029,1.029,0,1,0,1.1,1.7c-.345-.559-.707-1.132-1.082-1.7m-40.764-10.67a1.026,1.026,0,1,0,1.022,1.025,1.023,1.023,0,0,0-1.022-1.025m16.462,0a1.026,1.026,0,1,0,1.022,1.025,1.022,1.022,0,0,0-1.022-1.025m17.47.821c-.014-.016-.014-.016,0-.03l-.014-.016c-.165-.226-.315-.422-.48-.632a1,1,0,0,0-.512-.137,1.026,1.026,0,0,0,0,2.052,1.035,1.035,0,0,0,1.022-1.025.95.95,0,0,0-.016-.212m-25.7,31.894a1.026,1.026,0,1,0,1.022,1.027,1.024,1.024,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.022,1.027,1.025,1.025,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027M107.689,144.8a1.026,1.026,0,1,0,1.022,1.027,1.024,1.024,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.022,1.027,1.023,1.023,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.022,1.027,1.025,1.025,0,0,0-1.022-1.027M124.151,166.92a1.026,1.026,0,1,0,1.022,1.027,1.023,1.023,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.022,1.027,1.025,1.025,0,0,0-1.022-1.027" transform="translate(31.966 29.434)" fill="#fff"/>
    <path id="Path_42677" data-name="Path 42677" d="M116.5,154.885c-2.8-4.367-5.44-8.868-8.113-13.316s-5.328-8.932-7.931-13.431c-5.146-8.9-9.928-17.99-14.53-27.174-2.607-5.2-5.257-10.379-7.738-15.639-.077-.163-.357-.039-.291.13q3.6,9.169,7.749,18.069c-.05-.066-.1-.131-.149-.2-.621-.838-1.24-1.677-1.846-2.526-1.2-1.667-2.319-3.388-3.39-5.136-.087-.144-.3-.01-.222.135a59.257,59.257,0,0,0,4.189,6.865,30.609,30.609,0,0,0,2.39,3.034c.023.024.053.053.079.079,1.263,2.641,2.557,5.267,3.915,7.869a311.252,311.252,0,0,0,15.592,27c1.6,2.443,3.212,4.886,4.89,7.282s3.478,4.713,5.206,7.081c.089.121.285.006.2-.124" transform="translate(33.761 37.5)" fill="url(#linear-gradient-2)"/>
    <path id="Path_42680" data-name="Path 42680" d="M125.064,26.182,106.175,89.976c-.765,1.863.116,3.44,1.967,3.519L141.267,94.9a5.366,5.366,0,0,0,4.739-3.234l18.889-63.794c.767-1.868-.116-3.441-1.965-3.519L129.8,22.948a5.363,5.363,0,0,0-4.741,3.234" transform="translate(45.905 10.093)" fill="#263238"/>
    <path id="Path_42681" data-name="Path 42681" d="M127.808,26.273,108.919,90.067c-.765,1.863.116,3.441,1.967,3.519l33.126,1.407a5.362,5.362,0,0,0,4.738-3.234l18.89-63.794c.765-1.868-.116-3.441-1.965-3.519L132.55,23.039a5.363,5.363,0,0,0-4.742,3.234" transform="translate(47.094 10.133)" stroke="#00ffce" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" fill="url(#linear-gradient)"/>
    <path id="Union_1" data-name="Union 1" d="M12.24,151.812C5,141.369,8.67,120.6,16.622,70.8a1.607,1.607,0,0,1,.219-.578,30.37,30.37,0,0,1-.85-10.175C9.723,55.554-.332,56.058.008,50.159c.322-5.646,6.682-5.924,6.682-5.924s-6.8-2.588-5.39-8.178c1.546-6.134,11.8-4.821,11.8-4.821S4.545,27.374,7.59,21.251c3.725-7.5,22.744-1.755,34.7,3.669-3.118-5.238-9.744-8.126-16.5-15.9C16.259-1.949,29.516-1.374,36.812,2.676A73.981,73.981,0,0,1,55.213,16.8a34.347,34.347,0,0,1,7.235-9.3c7.759-6.913,12.2-.631,8.675,8.737-4.63,12.3-3.089,16.8-1.288,27.944a65.677,65.677,0,0,1,1.646,13.037c.275,9.808-2.713,20.761-5.31,28.318-3.227,14.351-8.185,33.209-9.859,36.968-9.351,21.005-28.377,31.536-38.371,31.537C15.287,154.04,13.27,153.3,12.24,151.812Z" transform="translate(150.651 44.364)" fill="#feccaa" stroke="#333c45" stroke-width="1"/>
    <path id="Path_42682" data-name="Path 42682" d="M129.964,28.519a2.817,2.817,0,1,1-2.63-3.006,2.822,2.822,0,0,1,2.63,3.006" transform="translate(53.897 11.22)" fill="#263238"/>
    <path id="Path_42685" data-name="Path 42685" d="M143.123,60.97c-4.637-3.634-17.278-9.172-28.654-8.556-.258.014-.386.216-.148.253a123.165,123.165,0,0,1,28.748,8.409c.063.027.1-.066.053-.107" transform="translate(49.501 23.036)" fill="#263238"/>
    <path id="Path_42686" data-name="Path 42686" d="M138.988,68.237c-4.323-2.723-17.6-7.847-29.111-6.824-.257.023-.371.235-.132.262a131.947,131.947,0,0,1,29.2,6.671c.064.024.1-.075.047-.109" transform="translate(47.515 26.957)" fill="#263238"/>
    <path id="Path_42687" data-name="Path 42687" d="M174.175,91.7,113.782,108.9s-12.047,39.765-4.386,49.745c8.185,10.662,46.209-5.86,53.775-29.1Z" transform="translate(46.315 40.337)" fill="url(#linear-gradient-10)"/>
    <path id="Path_42688" data-name="Path 42688" d="M151.392,98.662a1.021,1.021,0,1,1-1.021-1.025,1.024,1.024,0,0,1,1.021,1.025m15.441-1.025a1.026,1.026,0,1,0,1.021,1.025,1.024,1.024,0,0,0-1.021-1.025m-49.386,22.12a1.026,1.026,0,1,0,1.022,1.027,1.025,1.025,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027m16.462,0a1.026,1.026,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027m-24.693-10.593a1.026,1.026,0,1,0,1.022,1.025,1.025,1.025,0,0,0-1.022-1.025m16.462,0a1.026,1.026,0,1,0,1.022,1.025,1.024,1.024,0,0,0-1.022-1.025m16.462,0a1.026,1.026,0,1,0,1.021,1.025,1.024,1.024,0,0,0-1.021-1.025m-41.155,32.714a1.026,1.026,0,1,0,1.022,1.027,1.025,1.025,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027m16.462,0a1.026,1.026,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027m-41.155-10.595a1.026,1.026,0,1,0,1.022,1.027,1.023,1.023,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.022,1.027,1.025,1.025,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.022,1.027,1.024,1.024,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.021,1.027,1.024,1.024,0,0,0-1.021-1.027M109.217,153.4a1.026,1.026,0,1,0,1.022,1.027,1.023,1.023,0,0,0-1.022-1.027m16.462,0a1.026,1.026,0,1,0,1.022,1.027,1.025,1.025,0,0,0-1.022-1.027" transform="translate(46.9 42.95)" fill="#fff"/>
    <path id="Path_42689" data-name="Path 42689" d="M113.122,117.832c6.742-1.535,13.394-3.43,20.041-5.343s13.31-3.816,19.915-5.9c3.731-1.176,7.471-2.33,11.162-3.636.1-.035.063-.206-.042-.181-6.73,1.529-13.4,3.4-20.047,5.264q-10,2.806-19.944,5.816c-3.746,1.132-7.471,2.331-11.162,3.636-.182.065-.122.386.076.341" transform="translate(48.955 45.209)" fill="#263238"/>
    <path id="Path_42690" data-name="Path 42690" d="M116.342,103.191c-.94.1-1.875.3-2.805.474a.423.423,0,0,0-.29.307c-1.032,3.525-1.767,7.169-2.5,10.769-.72,3.552-1.357,7.122-1.945,10.7-.315,1.927-.595,3.857-.813,5.788a20.825,20.825,0,0,0-2.093,7.5c-.01.131.215.158.231.026a25.866,25.866,0,0,1,1.78-6.742,47.706,47.706,0,0,0,.374,14.2c.006.03.049.016.044-.013-1.19-7,.565-14.31,1.713-21.2.575-3.456,1.223-6.9,1.9-10.333s1.471-6.834,2.094-10.269c.8-.3,1.607-.6,2.378-.969a.125.125,0,0,0-.067-.229" transform="translate(45.905 45.393)" fill="#263238"/>
    <path id="Path_42691" data-name="Path 42691" d="M78.379,70.636c-.315,2-8.738,15.472-17.729,15.709-2.929.072-4.034-15.964-4.034-15.964l-.351-1.165L51.364,52.9,69.846,38.977l2.682-1.86s1.048,4.551,2.3,10.233c.06.262.119.526.178.808.08.32.158.621.218.922.2.847.4,1.712.575,2.577.139.658.277,1.3.4,1.994.139.7.259,1.374.4,2.069a110.131,110.131,0,0,1,1.788,14.916" transform="translate(22.265 16.327)" fill="#ffb788"/>
    <path id="Path_42692" data-name="Path 42692" d="M78.213,67.59C65.72,69.837,58.5,66.881,54.758,64.226L51.365,52.9,69.848,38.978l2.673-1.855s1.051,4.539,2.3,10.226c.059.256.12.527.179.814.076.317.166.619.227.92.195.845.39,1.7.571,2.579.135.649.269,1.3.39,1.991.151.694.27,1.372.406,2.066.8,4.419,1.352,8.959,1.621,11.87" transform="translate(22.265 16.33)" fill="#f2a077"/>
    <path id="Path_42693" data-name="Path 42693" d="M92.9,39.233C98.518,68.6,81.642,75.3,75.338,76.507c-5.725,1.1-25.354,4.111-33.966-24.516s3.2-40.533,16.162-43.742S87.274,9.866,92.9,39.233" transform="translate(16.66 3.251)" fill="#fca"/>
    <path id="Path_42694" data-name="Path 42694" d="M73.918,26.506a19.534,19.534,0,0,1-2.051-.147,3.3,3.3,0,0,1-2.063-.55,1.066,1.066,0,0,1-.175-1.286,2.62,2.62,0,0,1,2.48-.966A3.851,3.851,0,0,1,74.7,24.582a1.136,1.136,0,0,1-.777,1.924" transform="translate(30.122 10.36)" fill="#263238"/>
    <path id="Path_42695" data-name="Path 42695" d="M58.416,27.9a19.628,19.628,0,0,0,1.947-.665,3.321,3.321,0,0,0,1.856-1.058,1.069,1.069,0,0,0-.156-1.29,2.621,2.621,0,0,0-2.643-.3,3.871,3.871,0,0,0-2.242,1.653A1.134,1.134,0,0,0,58.416,27.9" transform="translate(24.711 10.72)" fill="#263238"/>
    <path id="Path_42696" data-name="Path 42696" d="M62.838,32.527c-.026-.075-.143.118-.129.2.354,2.006.423,4.363-1.3,5.3-.053.029-.032.13.034.111,2.169-.635,2.047-3.762,1.392-5.607" transform="translate(26.608 14.301)" fill="#263238"/>
    <path id="Path_42697" data-name="Path 42697" d="M61.59,31.242c-3.263.222-2.58,6.759.442,6.554,2.974-.2,2.292-6.74-.442-6.554" transform="translate(25.764 13.741)" fill="#263238"/>
    <path id="Path_42698" data-name="Path 42698" d="M61.233,31.59c-.53.429-1.019,1.14-1.7,1.281-.784.163-1.455-.459-2.041-1.063-.06-.063-.11,0-.1.086.112,1.254.725,2.49,2.081,2.536,1.237.042,2-1.159,2.177-2.452.033-.239-.2-.562-.417-.389" transform="translate(24.877 13.875)" fill="#263238"/>
    <path id="Path_42699" data-name="Path 42699" d="M70.58,31.112c.007-.079.169.078.175.161.166,2.03.7,4.327,2.595,4.791.059.014.065.117,0,.114-2.259-.059-2.933-3.116-2.765-5.066" transform="translate(30.584 13.676)" fill="#263238"/>
    <path id="Path_42700" data-name="Path 42700" d="M72.136,29.508c3.211-.618,4.206,5.879,1.231,6.451-2.929.564-3.923-5.932-1.231-6.451" transform="translate(30.587 12.962)" fill="#263238"/>
    <path id="Path_42701" data-name="Path 42701" d="M72.734,29.913c.563.265,1.165.812,1.782.759.715-.062,1.13-.847,1.479-1.59.036-.078.095-.03.11.055.225,1.237.014,2.593-1.137,3.015-1.051.384-2.01-.559-2.493-1.749-.09-.22.032-.6.258-.49" transform="translate(31.404 12.776)" fill="#263238"/>
    <path id="Path_42702" data-name="Path 42702" d="M64.085,43.468c.512.448,1.035,1.074,1.772,1.093A5.349,5.349,0,0,0,68,43.92a.071.071,0,0,1,.093.1,2.723,2.723,0,0,1-2.5,1.257,2.162,2.162,0,0,1-1.7-1.7c-.039-.115.115-.168.189-.1" transform="translate(27.695 19.109)" fill="#263238"/>
    <path id="Path_42703" data-name="Path 42703" d="M71.363,43.686s.765,2.972.932,4.4c.016.132-.321.236-.8.321-.023.01-.034.02-.056.007A6.765,6.765,0,0,1,64.6,46.651c-.115-.128.049-.3.188-.223a10.187,10.187,0,0,0,6.363,1.051c.054-.406-1.482-5.045-1.259-5.089a12.366,12.366,0,0,1,3.306.354c-1.416-6.236-3.819-12.194-5.136-18.426a.194.194,0,0,1,.358-.135c2.957,6.066,4.745,13.208,6.373,19.61.211.8-2.833.069-3.429-.107" transform="translate(27.985 10.593)" fill="#263238"/>
    <path id="Path_42704" data-name="Path 42704" d="M44.158,52.978c7.338-.848,7.681-23.735,7.692-26.546.085,1.77.566,11.145,1.306,11.462a7.182,7.182,0,0,0,2.276-.623l.017,0a22.365,22.365,0,0,1,0-3.366c.019-.523.284,1.11.725,3.109.407-.15.847-.324,1.327-.49a27.142,27.142,0,0,1,.142-7.166c.145-.9.694,3.057,1.809,6.464,4.968-1.781,12.157-4.173,18.995-4.947,1.828-.215,3.415-.318,4.821-.363-.208-2.762-.757-5.679-.506-5.061a26.666,26.666,0,0,1,1.554,5.04c.763-.017,1.471-.02,2.106-.009-.119-3.917-2.457-9.991-1.76-9.089a27.6,27.6,0,0,1,4.507,9.3c2.691.305,3.463.767,3.482.071.03-1.256-2.349-12.048-11.443-19.375-9.884-7.964-28.4-8.69-37.686,5.535,0,0-8.075,6.264-8.705,15.367-.635,9.086,5.1,21.175,9.339,20.691" transform="translate(15.072 2.522)" stroke="#8226ff" stroke-width="0.5" fill="url(#linear-gradient)"/>
    <path id="Path_42705" data-name="Path 42705" d="M62.92,45.97a9.205,9.205,0,1,1,8.523-11.758h0A9.192,9.192,0,0,1,62.92,45.97m-.536-17.182a7.869,7.869,0,0,0-1.951.31,7.923,7.923,0,1,0,1.951-.31" transform="translate(23.175 12.12)" fill="url(#linear-gradient)"/>
    <path id="Path_42706" data-name="Path 42706" d="M76.877,43.7c-3.506.111-6.8-3.4-7.506-8.15a11.028,11.028,0,0,1,.969-6.655,5.7,5.7,0,0,1,9.672-1.45,11.027,11.027,0,0,1,2.861,6.081c.745,5.022-1.676,9.562-5.4,10.121a5.261,5.261,0,0,1-.6.053M75.414,26.6a3.842,3.842,0,0,0-.463.042,4.893,4.893,0,0,0-3.523,2.816,9.816,9.816,0,0,0-.844,5.9c.646,4.35,3.648,7.522,6.713,7.06s5.01-4.37,4.365-8.721a9.787,9.787,0,0,0-2.524-5.4,5.042,5.042,0,0,0-3.723-1.7" transform="translate(30.011 11.159)" fill="url(#linear-gradient)"/>
    <path id="Path_42707" data-name="Path 42707" d="M66.179,33.87l-.917-.816c1.2-1.366,4.151-3.4,6.719-1.355l-.761.963c-2.3-1.837-4.928,1.084-5.04,1.208" transform="translate(28.289 13.562)" fill="url(#linear-gradient)"/>
    <path id="Path_42708" data-name="Path 42708" d="M39.594,41.379l-1.16-.4c.185-.544,1.881-5.333,4.523-6.181,2.47-.785,17.107-1.757,17.808-1.794l.065,1.23c-4.723.255-15.593,1.126-17.5,1.738-1.745.559-3.287,4.091-3.736,5.405" transform="translate(16.66 14.519)" fill="url(#linear-gradient)"/>
    <path id="Path_42709" data-name="Path 42709" d="M48.5,40.747s-7.421-8.611-11.376-5.859,2.295,15.7,7.41,17.032a5.034,5.034,0,0,0,6.429-3.4Z" transform="translate(15.551 15.108)" fill="#fca"/>
    <path id="Path_42710" data-name="Path 42710" d="M38.079,37.27a.069.069,0,0,0-.013.135c3.6,1.045,5.748,4.071,7.365,7.271-1.16-1.175-2.63-1.734-4.214-.327-.083.073.017.206.112.176a3.086,3.086,0,0,1,3.5.726,15.077,15.077,0,0,1,1.908,2.585c.194.294.734.069.579-.275l-.036-.078c-.37-4.431-4.431-10.261-9.2-10.213" transform="translate(16.479 16.395)" fill="#263238"/>
    <path id="Path_42711" data-name="Path 42711" d="M47.029,15.983c-.125,2.192-10.474,7.449-17.9,1.852-7.314-5.513-4.325-16.685,5.82-15.59,6.412.693,12.342,9.1,12.078,13.738" transform="translate(10.873 0.955)"/>
    <path id="Path_42712" data-name="Path 42712" d="M25.291,8.906A12.822,12.822,0,0,1,30.936,2.6a7.688,7.688,0,0,1,7.714,0,15.627,15.627,0,0,1,5.7,6.084,24.13,24.13,0,0,1,1.772,3.961,11.937,11.937,0,0,1,.821,4.3,10.466,10.466,0,0,0-.052-4.5A19.358,19.358,0,0,0,45.479,8.1a15.1,15.1,0,0,0-6.038-6.932,8.557,8.557,0,0,0-9.147.331,11.333,11.333,0,0,0-5,7.408" transform="translate(10.963 0)" fill="url(#linear-gradient-2)"/>
    <path id="Path_42713" data-name="Path 42713" d="M33.425,159.256l-.5.125-.013-.014c.166-.042.345-.069.512-.111" transform="translate(14.267 70.055)" fill="#eb9481"/>
    <path id="Path_42714" data-name="Path 42714" d="M.18,152.757c-.069.361-.139.71-.181,1.071.014-.071.014-.14.029-.209.042-.292.1-.57.152-.862" transform="translate(0 67.196)" fill="#ffc727"/>
    <path id="Path_42715" data-name="Path 42715" d="M.18,152.757c-.069.361-.139.71-.181,1.071.014-.071.014-.14.029-.209.042-.292.1-.57.152-.862" transform="translate(0 67.196)" fill="#ffc727"/>
    <path id="Path_42716" data-name="Path 42716" d="M35.313,162.868v.014c-.526.125-1.038.251-1.551.361.513-.125,1.039-.251,1.551-.376" transform="translate(14.635 71.644)" fill="#ffc727"/>
    <path id="Path_42717" data-name="Path 42717" d="M.18,152.757c-.069.361-.139.71-.181,1.071.014-.071.014-.14.029-.209.042-.292.1-.57.152-.862" transform="translate(0 67.196)" fill="#dbdbdb"/>
  </g>
</svg>
